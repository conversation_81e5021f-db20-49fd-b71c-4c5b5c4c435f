package com.vwatj.ppms.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Agent类型枚举
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public enum AgentTypeEnum implements IEnum<String> {

    /**
     * 通用Agent类型 - 基于配置的通用Agent
     */
    GENERAL("general", "通用Agent", "基于配置的通用Agent，通过配置LLM参数、提示词等实现功能"),

    /**
     * 预设Agent类型 - 基于代码实现的预设Agent
     */
    PRESET("preset", "预设Agent", "基于代码实现的预设Agent，集成特定业务功能");

    private final String code;
    private final String name;
    private final String description;

    AgentTypeEnum(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static AgentTypeEnum fromCode(String code) {
        for (AgentTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为通用配置类型
     */
    public boolean isGeneral() {
        return this == GENERAL;
    }

    /**
     * 判断是否为预设类型
     */
    public boolean isPreset() {
        return this == PRESET;
    }

    @Override
    @JsonValue
    public String getValue() {
        return code;
    }
}
