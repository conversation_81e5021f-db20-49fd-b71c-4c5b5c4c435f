<template>
  <div class="flex h-full p-4">
    <!-- 左侧文档列表区域 -->
    <div class="w-[320px] border-r border-gray-200 flex flex-col h-full bg-white shadow-sm">
      <!-- 搜索和视图切换 -->
      <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <!-- 标题和筛选按钮 -->
        <div class="flex items-center justify-between mb-2">
          <div class="text-sm font-medium text-gray-500">文档管理</div>
          <!-- 筛选按钮 -->
          <n-popover trigger="click" placement="bottom-end" :show-arrow="false">
            <template #trigger>
              <n-button size="small" circle :type="selectedCategories.length > 0 ? 'primary' : 'default'">
                <template #icon>
                  <n-icon :component="FunnelOutline" />
                </template>
              </n-button>
            </template>
            <div class="w-48 p-2">
              <div class="text-sm font-medium text-gray-700 mb-2">筛选类别</div>
              <n-select
                v-model:value="selectedCategories"
                multiple
                placeholder="选择类别"
                :options="categoryOptions"
                clearable
                size="small"
                max-tag-count="responsive"
                class="w-full">
              </n-select>
              <div v-if="selectedCategories.length > 0" class="mt-2 pt-2 border-t border-gray-100">
                <n-button size="tiny" text @click="clearAllFilters" class="text-gray-500 hover:text-gray-700">
                  清除所有筛选
                </n-button>
              </div>
            </div>
          </n-popover>
        </div>

        <!-- 搜索和视图切换 -->
        <n-input-group>
          <n-input v-model:value="searchText" placeholder="搜索文档..." clearable class="flex-1">
            <template #prefix>
              <n-icon :component="Search" />
            </template>
          </n-input>
          <n-button-group>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button :type="viewType === 'list' ? 'primary' : 'default'" @click="viewType = 'list'">
                  <template #icon>
                    <n-icon :component="List" />
                  </template>
                </n-button>
              </template>
              列表视图
            </n-tooltip>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button :type="viewType === 'grid' ? 'primary' : 'default'" @click="viewType = 'grid'">
                  <template #icon>
                    <n-icon :component="Grid" />
                  </template>
                </n-button>
              </template>
              平铺视图
            </n-tooltip>
          </n-button-group>
        </n-input-group>
      </div>

      <!-- 筛选状态提示 -->
      <div v-if="selectedCategories.length > 0" class="px-4 py-2 bg-blue-50 border-b border-blue-100">
        <div class="flex items-center gap-2 text-sm">
          <n-icon :component="FunnelOutline" :size="14" class="text-blue-500" />
          <span class="text-blue-700">筛选：</span>
          <div class="flex gap-1 flex-wrap">
            <n-tag v-for="category in selectedCategories" :key="category" size="small" type="info" closable
              @close="removeCategory(category)">
              {{ category }}
            </n-tag>
          </div>
        </div>
      </div>

      <!-- 文档列表 -->
      <div class="flex-1 overflow-auto">
        <n-scrollbar>
          <!-- 列表视图 -->
          <div v-if="viewType === 'list'" class="view-content p-2">
            <!-- 空状态 -->
            <div v-if="filteredDocs.length === 0" class="flex flex-col items-center justify-center h-64 text-gray-400">
              <n-icon :component="DocumentText" :size="48" class="mb-4" />
              <p class="text-sm">{{ searchText ? '未找到匹配的文档' : '暂无文档' }}</p>
            </div>

            <div v-for="doc in filteredDocs" :key="doc.id"
              @click="selectDoc(doc)"
              :class="[
                selectedDoc?.id === doc.id
                  ? 'bg-blue-50 border-l-4 border-blue-500 shadow-sm'
                  : 'hover:bg-gray-50 hover:shadow-sm border-l-4 border-transparent',
                'cursor-pointer transition-all duration-200 mx-2 mb-2 rounded-r-lg border border-gray-100 hover:border-gray-200'
              ]">
              <div class="p-3">
                <!-- 第一行：图标 + 文档名称 -->
                <div class="flex items-center gap-3 mb-2">
                  <div class="flex-shrink-0">
                    <div class="w-7 h-7 bg-blue-100 rounded-lg flex items-center justify-center">
                      <n-icon :component="DocumentText" :size="14" class="text-blue-600" />
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="text-sm font-medium text-gray-900 leading-tight" :title="doc.name">
                      {{ doc.name }}
                    </h3>
                  </div>
                  <!-- 快速操作按钮 -->
                  <div class="opacity-0 hover-actions transition-opacity duration-200 flex gap-1 flex-shrink-0">
                    <n-button size="tiny" text @click.stop="selectDoc(doc)">
                      <template #icon>
                        <n-icon :component="EyeOutline" :size="12" />
                      </template>
                    </n-button>
                    <n-button size="tiny" text @click.stop="">
                      <template #icon>
                        <n-icon :component="Download" :size="12" />
                      </template>
                    </n-button>
                  </div>
                </div>

                <!-- 第二行：上传者信息 -->
                <div class="flex items-center gap-2 mb-2 pl-10">
                  <n-avatar round size="small" :src="doc.avatar"
                    :fallback-src="'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'"
                    class="flex-shrink-0 w-4 h-4" />
                  <span class="text-xs text-gray-600 flex-1 min-w-0 truncate">{{ doc.uploader }}</span>
                  <span class="text-xs text-gray-400 flex-shrink-0">{{ doc.size }}</span>
                </div>

                <!-- 第三行：标签和时间 -->
                <div class="flex items-center justify-between pl-10">
                  <div class="flex gap-1 flex-1 min-w-0">
                    <n-tag size="tiny" type="info">
                      {{ doc.category }}
                    </n-tag>
                  </div>
                  <div class="text-xs text-gray-400 flex-shrink-0 ml-2">
                    {{ formatDateTime(doc.updateTime) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 平铺视图 -->
          <div v-else-if="viewType === 'grid'" class="grid grid-cols-2 gap-3 p-3">
            <!-- 空状态 -->
            <div v-if="filteredDocs.length === 0" class="col-span-2 flex flex-col items-center justify-center h-64 text-gray-400">
              <n-icon :component="DocumentText" :size="48" class="mb-4" />
              <p class="text-sm">{{ searchText ? '未找到匹配的文档' : '暂无文档' }}</p>
            </div>

            <div v-for="doc in filteredDocs" :key="doc.id"
              @click="selectDoc(doc)"
              :class="[
                selectedDoc?.id === doc.id
                  ? 'ring-2 ring-blue-500 bg-blue-50'
                  : 'hover:shadow-lg hover:ring-1 hover:ring-gray-200',
                'cursor-pointer transition-all duration-200 bg-white rounded-lg border border-gray-100 overflow-hidden group'
              ]"
              class="aspect-square flex flex-col">

              <!-- 文档图标区域 (占35%高度) -->
              <div class="flex-none h-[35%] bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center relative">
                <div class="w-8 h-8 bg-white rounded-lg shadow-sm flex items-center justify-center">
                  <n-icon :component="DocumentText" :size="18" class="text-blue-500" />
                </div>
                <!-- 快速操作按钮 -->
                <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-0.5">
                  <n-button size="tiny" circle text @click.stop="selectDoc(doc)" class="!w-5 !h-5 bg-white/80 hover:bg-white">
                    <template #icon>
                      <n-icon :component="EyeOutline" :size="10" />
                    </template>
                  </n-button>
                  <n-button size="tiny" circle text @click.stop="" class="!w-5 !h-5 bg-white/80 hover:bg-white">
                    <template #icon>
                      <n-icon :component="Download" :size="10" />
                    </template>
                  </n-button>
                </div>
              </div>

              <!-- 文档信息区域 (占65%高度) -->
              <div class="flex-1 p-2 flex flex-col justify-between">
                <!-- 文档名称 -->
                <div class="mb-1">
                  <h3 class="text-xs font-medium text-gray-900 leading-tight line-clamp-2" :title="doc.name">
                    {{ doc.name }}
                  </h3>
                </div>

                <!-- 上传者信息 -->
                <div class="flex items-center gap-1 mb-1">
                  <n-avatar round size="small" :src="doc.avatar"
                    :fallback-src="'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'"
                    class="flex-shrink-0 w-4 h-4" />
                  <span class="text-xs text-gray-600 flex-1 truncate">{{ doc.uploader }}</span>
                </div>

                <!-- 标签和时间 -->
                <div class="space-y-1">
                  <div class="flex gap-0.5">
                    <n-tag size="tiny" type="info" class="!text-xs !px-1 !py-0 !leading-none !min-h-[16px]">
                      {{ doc.category }}
                    </n-tag>
                  </div>
                  <div class="text-xs text-gray-400 truncate">
                    {{ formatDateTime(doc.updateTime) }}
                  </div>
                </div>
              </div>
            </div>
          </div>


        </n-scrollbar>
      </div>
    </div>

    <!-- 右侧文档预览区域 -->
    <div class="flex-1 flex flex-col h-full bg-white ml-0.5">
      <div v-if="selectedDoc" class="h-full flex flex-col">
        <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">{{ selectedDoc?.name }}</h2>
            <n-button-group>
              <n-button secondary type="primary">
                <template #icon>
                  <n-icon :component="Download" />
                </template>
                下载
              </n-button>
              <n-button secondary type="primary">
                <template #icon>
                  <n-icon :component="ShareSocial" />
                </template>
                分享
              </n-button>
            </n-button-group>
          </div>
          <div class="text-sm text-gray-500 mt-1">
            更新于 {{ formatDateTime(selectedDoc?.updateTime) }} · {{ selectedDoc?.size }}
          </div>
        </div>
        <div class="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50">
          <!-- 这里可以集成文档预览组件，例如PDF.js或其他文档预览库 -->
          <div class="h-full flex items-center justify-center bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="text-center">
              <n-icon :component="DocumentText" size="64" class="text-gray-300 mb-4 mx-auto" />
              <p class="text-gray-400">文档预览区域</p>
            </div>
          </div>
        </div>
      </div>
      <div v-else
        class="h-full flex items-center justify-center text-gray-400 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="text-center">
          <n-icon :component="DocumentText" size="64" class="mb-4 mx-auto" />
          <p>请从左侧选择文档进行预览</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useMessage, NAvatar, NTag } from 'naive-ui';
import {
  Search,
  List,
  Grid,
  DocumentText,
  Download,
  ShareSocial,
  EyeOutline,
  FunnelOutline,
} from '@vicons/ionicons5';
import { ProjectApi } from '@/apis/project';

const props = defineProps<{
  projectInfo: {
    id: number
    name: string
    [key: string]: any
  }
}>()

const message = useMessage()

// 文档数据
const docs = ref<any[]>([])
const loading = ref(false)

// 获取项目文档列表
const fetchDocuments = async () => {
  try {
    loading.value = true
    const response = await ProjectApi.getProjectDocuments(props.projectInfo.id)
    if (response.data) {
      // 如果返回的是数组，直接使用；如果是分页数据，使用records字段
      const documents = Array.isArray(response.data) ? response.data : []
      docs.value = documents.map((doc: any) => ({
        id: doc.id,
        name: doc.name || doc.title,
        updateTime: doc.updateTime || doc.uploadTime,
        size: doc.size || '未知大小',
        uploader: doc.uploader || doc.author,
        category: doc.category,
        phase: doc.phase || '未分类',
        avatar: doc.avatar || 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
      }))
    }
  } catch (error: any) {
    console.error('获取文档列表失败:', error)
    message.error(error.message || '获取文档列表失败')
  } finally {
    loading.value = false
  }
}



const viewType = ref<'list' | 'grid'>('list');
const searchText = ref('');
const selectedDoc = ref<any | null>(null);
const selectedCategories = ref<string[]>([]);



// 获取所有可用的类别选项
const categoryOptions = computed(() => {
  const categories = [...new Set(docs.value.map(doc => doc.category).filter(Boolean))];
  return categories.map(category => ({
    label: category,
    value: category
  }));
});

// 过滤文档列表
const filteredDocs = computed(() => {
  let filtered = docs.value;

  // 按搜索文本筛选
  if (searchText.value.trim()) {
    const search = searchText.value.toLowerCase();
    filtered = filtered.filter((doc) => doc.name.toLowerCase().includes(search));
  }

  // 按类别筛选
  if (selectedCategories.value.length > 0) {
    filtered = filtered.filter((doc) => selectedCategories.value.includes(doc.category));
  }

  return filtered;
});

// 选择文档
const selectDoc = (doc: any) => {
  selectedDoc.value = doc;
};

// 移除单个类别筛选
const removeCategory = (category: string) => {
  const index = selectedCategories.value.indexOf(category);
  if (index > -1) {
    selectedCategories.value.splice(index, 1);
  }
};

// 清除所有筛选
const clearAllFilters = () => {
  selectedCategories.value = [];
  searchText.value = '';
};

import { formatDate, DATE_FORMATS } from '@/utils/dateTime'

// 格式化日期
const formatDateTime = (dateString: string) => {
  return formatDate(dateString, DATE_FORMATS.DATETIME_MINUTE)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDocuments()
})
</script>

<style scoped>
.view-content {
  height: calc(100vh - 180px);
  min-height: 400px;
}

/* 列表视图优化样式 */
.view-content > div:hover .hover-actions {
  opacity: 1;
}

/* 文档项悬停效果 */
.view-content > div {
  position: relative;
  overflow: hidden;
}

.view-content > div::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 0 0.5rem 0.5rem 0;
}

.view-content > div:hover::before {
  opacity: 1;
}

/* 选中状态的特殊样式 */
.view-content > div.bg-blue-50::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  opacity: 1;
}

/* 文档名称自动换行 */
.view-content h3 {
  word-break: break-all;
  line-height: 1.3;
  max-height: 2.6em; /* 最多显示2行 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 确保标签不会过宽 */
.view-content .n-tag {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 平铺视图样式优化 */
.grid .aspect-square {
  aspect-ratio: 1 / 1;
  min-height: 140px;
  max-height: 160px;
}

/* 文档名称多行截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
  line-height: 1.3;
}

/* 平铺视图悬停效果 */
.grid > div:hover {
  transform: translateY(-2px);
}

/* 选中状态的特殊样式 */
.grid > div.ring-2 {
  transform: translateY(-1px);
}
</style>