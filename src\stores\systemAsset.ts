// 系统资产状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { systemApi } from '@/apis'
import type { SystemAsset, SystemAssetQuery } from '@/types'
import { showMessage } from '@/utils/errorHandler'

export const useSystemAssetStore = defineStore('systemAsset', () => {

  // 状态
  const assets = ref<SystemAsset[]>([])
  const currentAsset = ref<SystemAsset | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 12,
    total: 0
  })

  // 计算属性
  const assetMap = computed(() => {
    const map = new Map<number, SystemAsset>()
    assets.value.forEach((asset: SystemAsset) => {
      map.set(asset.id, asset)
    })
    return map
  })

  // 根据ID获取资产
  const getAssetById = (assetId: string | number): SystemAsset | null => {
    if (!assetId) return null
    return assetMap.value.get(Number(assetId)) || null
  }

  // 获取资产显示名称
  const getAssetDisplayName = (assetId: string | number): string => {
    if (!assetId) return ''
    const asset = assetMap.value.get(Number(assetId))
    if (asset) {
      return `${asset.projectName} (${asset.assetNo})`
    }
    return `资产-${assetId}`
  }

  // 获取系统资产列表
  const fetchAssets = async (params?: SystemAssetQuery) => {
    try {
      loading.value = true
      const response = await systemApi.getSystemAssets({
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...params
      })
      
      assets.value = response.data.data || []
      pagination.value.total = response.data.total || 0
      pagination.value.page = response.data.page || 1
      pagination.value.pageSize = response.data.pageSize || 12
    } catch (error: any) {
      showMessage('error', error.message || '获取系统资产列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取系统资产详情
  const fetchAsset = async (id: number) => {
    try {
      loading.value = true
      const response = await systemApi.getSystemAsset(id)
      currentAsset.value = response.data
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '获取系统资产详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建系统资产
  const createAsset = async (data: Partial<SystemAsset>) => {
    try {
      loading.value = true
      const response = await systemApi.createSystemAsset(data as any)
      assets.value.unshift(response.data)
      pagination.value.total += 1
      showMessage('success', '系统资产创建成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '创建系统资产失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新系统资产
  const updateAsset = async (data: Partial<SystemAsset> & { id: number }) => {
    try {
      loading.value = true
      const response = await systemApi.updateSystemAsset(data)
      
      // 更新列表中的资产
      const index = assets.value.findIndex((a: SystemAsset) => a.id === data.id)
      if (index !== -1) {
        assets.value[index] = response.data
      }
      
      // 更新当前资产
      if (currentAsset.value?.id === data.id) {
        currentAsset.value = response.data
      }
      
      showMessage('success', '系统资产更新成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '更新系统资产失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除系统资产
  const deleteAsset = async (id: number) => {
    try {
      loading.value = true
      await systemApi.deleteSystemAsset(id)
      
      // 从列表中移除
      const index = assets.value.findIndex((a: SystemAsset) => a.id === id)
      if (index !== -1) {
        assets.value.splice(index, 1)
        pagination.value.total -= 1
      }
      
      // 清除当前资产
      if (currentAsset.value?.id === id) {
        currentAsset.value = null
      }
      
      showMessage('success', '系统资产删除成功')
    } catch (error: any) {
      showMessage('error', error.message || '删除系统资产失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.page = page
    pagination.value.pageSize = pageSize
  }

  // 重置状态
  const reset = () => {
    assets.value = []
    currentAsset.value = null
    loading.value = false
    pagination.value = {
      page: 1,
      pageSize: 12,
      total: 0
    }
  }

  return {
    // 状态
    assets,
    currentAsset,
    loading,
    pagination,
    
    // 计算属性
    assetMap,
    
    // 方法
    getAssetById,
    getAssetDisplayName,
    fetchAssets,
    fetchAsset,
    createAsset,
    updateAsset,
    deleteAsset,
    setPagination,
    reset
  }
})
