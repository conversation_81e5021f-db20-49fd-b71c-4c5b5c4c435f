<template>
  <div class="complete-node">
    <div class="node-header">
      <h4 class="node-title">{{ title }}</h4>
      <n-tag :type="statusType" size="small">{{ statusText }}</n-tag>
    </div>

    <div class="completion-content">
      <n-result 
        :status="resultStatus" 
        :title="resultTitle" 
        :description="resultDescription"
      >
        <template #icon v-if="status === 'completed'">
          <n-icon :component="CheckCircleOutlined" size="64" color="#52c41a" />
        </template>
        
        <template #footer>
          <div class="completion-info" v-if="completeData">
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">完成人:</span>
                <span class="info-value">{{ completeData.completedBy }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">完成时间:</span>
                <span class="info-value">{{ formatTime(completeData.completedAt) }}</span>
              </div>
              <div class="info-item full-width" v-if="completeData.remark">
                <span class="info-label">备注:</span>
                <div class="info-value">{{ completeData.remark }}</div>
              </div>
            </div>
          </div>
          
          <div class="completion-actions">
            <n-button 
              v-if="previousNodeData" 
              type="primary" 
              @click="viewPreviousData"
            >
              <template #icon>
                <n-icon :component="EyeOutlined" />
              </template>
              查看{{ previousNodeTitle }}
            </n-button>
            
            <n-button 
              v-if="previousNodeData" 
              @click="editPreviousData"
            >
              <template #icon>
                <n-icon :component="EditOutlined" />
              </template>
              编辑{{ previousNodeTitle }}
            </n-button>
            
            <n-button 
              v-if="status === 'completed'" 
              @click="downloadReport"
            >
              <template #icon>
                <n-icon :component="DownloadOutlined" />
              </template>
              下载报告
            </n-button>
          </div>
        </template>
      </n-result>
    </div>

    <!-- 查看上一节点数据的抽屉 -->
    <n-drawer
      v-model:show="showDataDrawer"
      :width="600"
      placement="right"
      :title="`查看${previousNodeTitle}`"
    >
      <div class="drawer-content">
        <component 
          :is="previousNodeComponent"
          v-if="previousNodeComponent && previousNodeData"
          :title="previousNodeTitle || ''"
          :status="'completed'"
          :data="previousNodeData"
          :readonly="true"
          @edit="handleEditFromDrawer"
        />
      </div>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  NTag, NResult, NIcon, NButton, NDrawer, useMessage 
} from 'naive-ui'
import { 
  CheckCircleOutlined, EyeOutlined, EditOutlined, DownloadOutlined 
} from '@vicons/antd'
import type { CompleteData } from '@/types/project_process'

// 动态导入节点组件
import DocumentUploadNode from './DocumentUploadNode.vue'
import AssignPersonNode from './AssignPersonNode.vue'
import FormNode from './FormNode.vue'

interface Props {
  title: string
  status: 'pending' | 'current' | 'completed'
  data?: CompleteData
  previousNodeData?: any // 上一个节点的数据
  previousNodeType?: 'upload_document' | 'assign_person' | 'form' // 上一个节点的类型
  previousNodeTitle?: string // 上一个节点的标题
}

interface Emits {
  (e: 'view-previous'): void
  (e: 'edit-previous'): void
  (e: 'download-report'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const message = useMessage()

const showDataDrawer = ref(false)
const completeData = ref<CompleteData | null>(props.data || null)

// 状态映射
const statusMap = {
  pending: { type: 'default', text: '待处理' },
  current: { type: 'info', text: '进行中' },
  completed: { type: 'success', text: '已完成' }
}

const statusType = computed(() => statusMap[props.status].type as 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary')
const statusText = computed(() => statusMap[props.status].text)

// 结果状态
const resultStatus = computed(() => {
  switch (props.status) {
    case 'completed': return 'success'
    case 'current': return 'info'
    case 'pending': return 'warning'
    default: return 'info'
  }
})

const resultTitle = computed(() => {
  switch (props.status) {
    case 'completed': return '阶段已完成'
    case 'current': return '等待完成'
    case 'pending': return '等待前置步骤完成'
    default: return '状态未知'
  }
})

const resultDescription = computed(() => {
  switch (props.status) {
    case 'completed': return '此阶段的所有步骤都已成功完成'
    case 'current': return '前置步骤已完成，等待最终确认'
    case 'pending': return '请先完成前置步骤'
    default: return ''
  }
})

// 上一个节点的组件
const previousNodeComponent = computed(() => {
  if (!props.previousNodeType) return null
  
  switch (props.previousNodeType) {
    case 'upload_document': return DocumentUploadNode
    case 'assign_person': return AssignPersonNode
    case 'form': return FormNode
    default: return null
  }
})

import { formatDate, DATE_FORMATS } from '@/utils/dateTime'

// 格式化时间
const formatTime = (timeStr: string) => {
  return formatDate(timeStr, DATE_FORMATS.DATETIME_MINUTE)
}

// 查看上一节点数据
const viewPreviousData = () => {
  if (props.previousNodeData) {
    showDataDrawer.value = true
  } else {
    emit('view-previous')
  }
}

// 编辑上一节点数据
const editPreviousData = () => {
  emit('edit-previous')
}

// 从抽屉中编辑
const handleEditFromDrawer = () => {
  showDataDrawer.value = false
  editPreviousData()
}

// 下载报告
const downloadReport = () => {
  // 这里实现下载报告的逻辑
  message.info('正在生成报告...')
  
  // 模拟下载
  setTimeout(() => {
    const link = document.createElement('a')
    link.href = '#' // 这里应该是实际的下载链接
    link.download = `${props.title}_完成报告.pdf`
    // link.click()
    message.success('报告下载完成')
  }, 2000)
  
  emit('download-report')
}
</script>

<style scoped>
.complete-node {
  padding: 20px;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.node-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.completion-content {
  text-align: center;
}

.completion-info {
  margin: 20px 0;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  text-align: left;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: stretch;
}

.info-label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #374151;
  flex: 1;
}

.completion-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 20px;
}

.drawer-content {
  padding: 0;
}

/* 暗黑模式适配 */
.dark .node-title {
  color: #f3f4f6;
}

.dark .completion-info {
  background: #1f2937;
  border-color: #374151;
}

.dark .info-label {
  color: #9ca3af;
}

.dark .info-value {
  color: #f3f4f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .completion-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .completion-actions .n-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
