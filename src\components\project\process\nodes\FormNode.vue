<template>
  <div class="form-node">
    <div class="node-header">
      <h4 class="node-title">{{ title }}</h4>
      <n-tag :type="statusType" size="small">{{ statusText }}</n-tag>
    </div>

    <div class="form-content" v-if="!readonly">
      <n-form :model="formData" label-placement="left" label-width="120px">
        <n-form-item label="开始时间" required>
          <n-date-picker
            v-model:formatted-value="formData.startTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            clearable
            style="width: 100%"
            placeholder="请选择开始时间"
          />
        </n-form-item>

        <n-form-item label="结束时间" required>
          <n-date-picker
            v-model:formatted-value="formData.endTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            clearable
            style="width: 100%"
            placeholder="请选择结束时间"
          />
        </n-form-item>
        
        <n-form-item label="负责人" required>
          <n-input 
            v-model:value="formData.owner" 
            placeholder="请输入负责人"
          />
        </n-form-item>
        
        <n-form-item label="进度">
          <n-input-number
            v-model:value="formData.progress"
            :min="0"
            :max="100"
            :step="5"
            style="width: 100%"
            placeholder="请输入进度"
          >
            <template #suffix>%</template>
          </n-input-number>
        </n-form-item>
        
        <n-form-item label="优先级">
          <n-select
            v-model:value="formData.priority"
            :options="priorityOptions"
            placeholder="请选择优先级"
            clearable
          />
        </n-form-item>
        
        <n-form-item label="状态">
          <n-select
            v-model:value="formData.status"
            :options="statusOptions"
            placeholder="请选择状态"
            clearable
          />
        </n-form-item>
        
        <n-form-item label="描述">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 6 }"
            placeholder="请填写详细描述..."
          />
        </n-form-item>
        
        <n-form-item label="附件">
          <n-upload
            v-model:file-list="formData.attachments"
            multiple
            :max="5"
            list-type="text"
            class="w-full"
          >
            <n-button>上传附件</n-button>
          </n-upload>
        </n-form-item>
        
        <n-form-item label="备注">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入备注信息（可选）..."
          />
        </n-form-item>
      </n-form>
    </div>

    <div class="form-display" v-else-if="displayData">
      <div class="form-grid">
        <div class="form-item" v-if="displayData.startTime">
          <span class="item-label">开始时间:</span>
          <span class="item-value">{{ displayData.startTime }}</span>
        </div>
        <div class="form-item" v-if="displayData.endTime">
          <span class="item-label">结束时间:</span>
          <span class="item-value">{{ displayData.endTime }}</span>
        </div>
        <div class="form-item" v-if="displayData.owner">
          <span class="item-label">负责人:</span>
          <span class="item-value">{{ displayData.owner }}</span>
        </div>
        <div class="form-item" v-if="displayData.progress !== undefined">
          <span class="item-label">进度:</span>
          <span class="item-value">{{ displayData.progress }}%</span>
        </div>
        <div class="form-item" v-if="displayData.priority">
          <span class="item-label">优先级:</span>
          <span class="item-value">
            <n-tag :type="getPriorityType(displayData.priority)" size="small">
              {{ getPriorityLabel(displayData.priority) }}
            </n-tag>
          </span>
        </div>
        <div class="form-item" v-if="displayData.status">
          <span class="item-label">状态:</span>
          <span class="item-value">
            <n-tag :type="getStatusTagType(displayData.status)" size="small">
              {{ getStatusLabel(displayData.status) }}
            </n-tag>
          </span>
        </div>
      </div>
      
      <div class="form-item full-width" v-if="displayData.description">
        <span class="item-label">描述:</span>
        <div class="item-value description">{{ displayData.description }}</div>
      </div>
      
      <div class="form-item full-width" v-if="displayData.attachments && displayData.attachments.length > 0">
        <span class="item-label">附件:</span>
        <div class="attachments-list">
          <div v-for="file in displayData.attachments" :key="file.id" class="attachment-item">
            <n-icon :component="FileOutlined" />
            <span>{{ file.name }}</span>
          </div>
        </div>
      </div>
      
      <div class="form-item full-width" v-if="displayData.remark">
        <span class="item-label">备注:</span>
        <div class="item-value">{{ displayData.remark }}</div>
      </div>
      
      <div class="form-item full-width" v-if="displayData.submittedAt">
        <span class="item-label">提交时间:</span>
        <span class="item-value">{{ formatTime(displayData.submittedAt) }}</span>
      </div>
    </div>

    <div class="empty-state" v-else-if="readonly">
      <n-empty description="暂无表单数据" />
    </div>

    <div class="node-actions" v-if="!readonly">
      <n-button 
        type="primary" 
        @click="submitNode" 
        :loading="submitting"
        :disabled="!isFormValid"
      >
        提交表单
      </n-button>
      <n-button @click="resetNode">重置</n-button>
    </div>

    <div class="node-actions" v-else>
      <n-button @click="editNode">编辑表单</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  NTag, NForm, NFormItem, NInput, NButton, NDatePicker, NInputNumber,
  NSelect, NUpload, NEmpty, NIcon, useMessage, type UploadFileInfo 
} from 'naive-ui'
import { FileOutlined } from '@vicons/antd'
import type { FormData } from '@/types/project_process'

interface Props {
  title: string
  status: 'pending' | 'current' | 'completed'
  data?: FormData & { submittedAt?: string }
  readonly?: boolean
}

interface Emits {
  (e: 'submit', data: FormData & { submittedAt: string }): void
  (e: 'edit'): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<Emits>()
const message = useMessage()

const submitting = ref(false)
const displayData = ref<(FormData & { submittedAt?: string }) | null>(props.data || null)

// 表单数据
const formData = ref({
  startTime: '',
  endTime: '',
  owner: '',
  progress: 0,
  priority: '',
  status: '',
  description: '',
  attachments: [] as UploadFileInfo[],
  remark: ''
})

// 选项数据
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'Medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

const statusOptions = [
  { label: '待开始', value: 'pending' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' },
  { label: '已取消', value: 'cancelled' }
]

// 状态映射
const statusMap = {
  pending: { type: 'default', text: '待处理' },
  current: { type: 'info', text: '进行中' },
  completed: { type: 'success', text: '已完成' }
}

const statusType = computed(() => statusMap[props.status].type as 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary')
const statusText = computed(() => statusMap[props.status].text)

// 表单验证
const isFormValid = computed(() => {
  return formData.value.startTime && 
         formData.value.endTime && 
         formData.value.owner
})

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    displayData.value = { ...newData }
    Object.assign(formData.value, {
      startTime: newData.startTime || '',
      endTime: newData.endTime || '',
      owner: newData.owner || '',
      progress: newData.progress || 0,
      priority: newData.priority || '',
      status: newData.status || '',
      description: newData.description || '',
      attachments: newData.attachments || [],
      remark: newData.remark || ''
    })
  }
}, { immediate: true })

// 获取优先级类型
const getPriorityType = (priority: string): 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary' => {
  const typeMap: Record<string, 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary'> = {
    low: 'default',
    medium: 'info',
    high: 'warning',
    urgent: 'error'
  }
  return typeMap[priority] || 'default'
}

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const option = priorityOptions.find(opt => opt.value === priority)
  return option?.label || priority
}

// 获取状态标签类型
const getStatusTagType = (status: string): 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary' => {
  const typeMap: Record<string, 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary'> = {
    pending: 'default',
    in_progress: 'info',
    completed: 'success',
    paused: 'warning',
    cancelled: 'error'
  }
  return typeMap[status] || 'default'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const option = statusOptions.find(opt => opt.value === status)
  return option?.label || status
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 提交节点
const submitNode = async () => {
  if (!isFormValid.value) {
    message.error('请填写必填字段')
    return
  }

  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const data: FormData & { submittedAt: string } = {
      ...formData.value,
      submittedAt: new Date().toISOString()
    }
    
    displayData.value = data
    emit('submit', data)
    message.success('表单提交成功')
  } catch (error) {
    message.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 重置节点
const resetNode = () => {
  Object.assign(formData.value, {
    startTime: '',
    endTime: '',
    owner: '',
    progress: 0,
    priority: '',
    status: '',
    description: '',
    attachments: [],
    remark: ''
  })
  message.info('已重置')
}

// 编辑节点
const editNode = () => {
  emit('edit')
}
</script>

<style scoped>
.form-node {
  padding: 20px;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.node-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.form-content {
  margin-bottom: 20px;
}

.form-display {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.form-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: stretch;
}

.item-label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.item-value {
  color: #374151;
  flex: 1;
}

.item-value.description {
  white-space: pre-wrap;
  line-height: 1.5;
  margin-top: 4px;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 4px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
}

.empty-state {
  margin: 40px 0;
}

.node-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* 暗黑模式适配 */
.dark .node-title {
  color: #f3f4f6;
}

.dark .form-display {
  background: #1f2937;
  border-color: #374151;
}

.dark .item-label {
  color: #9ca3af;
}

.dark .item-value {
  color: #f3f4f6;
}

.dark .attachment-item {
  background: #374151;
  border-color: #4b5563;
}

.dark .node-actions {
  border-top-color: #374151;
}
</style>
