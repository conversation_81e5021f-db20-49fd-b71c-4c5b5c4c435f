<template>
  <n-modal v-model:show="showModal" preset="card" :title="modalTitle" style="width: 90%; max-width: 1200px;">
    <div class="modal-content">
      <!-- 主要选项卡 -->
      <n-tabs v-model:value="activeMainTab" type="line" animated>
        <!-- 系统信息 Tab -->
        <n-tab-pane name="systemInfo" tab="系统信息" display-directive="show">
          <div class="tab-content">
            <n-form ref="formRef" :model="formData" :rules="rules" :disabled="!isEditing">
              <div class="form-grid">
                <!-- 基本信息 -->
                <n-form-item label="资产编号" path="assetNo">
                  <n-input v-model:value="formData.assetNo" placeholder="请输入资产编号" />
                </n-form-item>

                <n-form-item label="项目名称" path="projectName">
                  <n-input v-model:value="formData.projectName" placeholder="请输入项目名称" />
                </n-form-item>

                <n-form-item label="短代码" path="shortCode">
                  <n-input v-model:value="formData.shortCode" placeholder="请输入短代码" />
                </n-form-item>

                <n-form-item label="业务部门" path="businessDepartment">
                  <n-select v-model:value="formData.businessDepartment" :options="businessDepartmentOptions"
                    :loading="loadingBusinessDepartments" placeholder="请选择业务部门" filterable clearable
                    @click="handleBusinessDepartmentClick" />
                </n-form-item>

                <n-form-item label="PM负责人" path="pmOwner">
                  <n-select v-model:value="formData.pmOwner" :options="pmOwnerOptions" :loading="loadingPmOwners"
                    placeholder="请选择PM负责人" filterable clearable @click="handlePmOwnerClick" />
                </n-form-item>

                <n-form-item label="资产状态" path="assetState">
                  <n-select v-model:value="formData.assetState" :options="assetStateOptions"
                    :loading="loadingAssetStates" placeholder="请选择资产状态" filterable clearable
                    @click="handleAssetStateClick" />
                </n-form-item>

                <n-form-item label="区域" path="region">
                  <n-select v-model:value="formData.region" :options="regionOptions" :loading="loadingRegions"
                    placeholder="请选择区域" filterable clearable @click="handleRegionClick" />
                </n-form-item>

                <n-form-item label="退役日期" path="retireDate">
                  <n-date-picker v-model:formatted-value="formData.retireDate" type="date" placeholder="请选择退役日期"
                    value-format="yyyy-MM-dd" clearable />
                </n-form-item>


                <n-form-item label="上线日期" path="onlineDate">
                  <n-date-picker v-model:formatted-value="formData.onlineDate" type="date" placeholder="请选择上线日期"
                    value-format="yyyy-MM-dd" clearable />
                </n-form-item>

                <n-form-item label="邮箱" path="mail">
                  <n-input v-model:value="formData.mail" placeholder="请输入邮箱" />
                </n-form-item>

                <n-form-item label="版本" path="version">
                  <n-input v-model:value="formData.version" placeholder="请输入版本" />
                </n-form-item>

                <n-form-item label="语言" path="language">
                  <n-input v-model:value="formData.language" placeholder="请输入语言" />
                </n-form-item>

                <n-form-item label="用户数量" path="userCount">
                  <n-input-number v-model:value="formData.userCount" placeholder="请输入用户数量" />
                </n-form-item>

                <n-form-item label="使用状态" path="usageStatus">
                  <n-select v-model:value="formData.usageStatus" :options="usageStatusOptions"
                    :loading="loadingUsageStatuses" placeholder="请选择使用状态" filterable clearable
                    @click="handleUsageStatusClick" />
                </n-form-item>

                <n-form-item label="业务用户" path="businessUser">
                  <n-input v-model:value="formData.businessUser" placeholder="请输入业务用户" />
                </n-form-item>

                <n-form-item label="描述" path="description" class="full-width">
                  <n-input v-model:value="formData.description" type="textarea" :rows="3" placeholder="请输入描述" />
                </n-form-item>
              </div>
            </n-form>
          </div>
        </n-tab-pane>

        <!-- 服务器信息 Tab -->
        <n-tab-pane name="serverInfo" tab="服务器信息">
          <div class="tab-content">
            <ServerInfoForm v-model:modelValue="formServerInfo" :isEditing="isEditing" />
          </div>
        </n-tab-pane>

        <!-- other -->
        <n-tab-pane name="otherInfo" tab="附加信息">
          <div class="tab-content">
            <OtherInfoForm v-model:modelValue="formOtherInfo" :isEditing="isEditing" />
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="modal-footer">
        <n-button @click="handleCancel">取消</n-button>
        <n-button v-if="mode === 'view'" type="primary" @click="handleEdit">
          编辑
        </n-button>
        <n-button v-if="isEditing" type="primary" @click="handleSave">
          保存
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NModal, NFormItem, NInput, NSelect, NTabs, NTabPane,
  NButton, NDatePicker, NInputNumber, NAlert, useMessage
} from 'naive-ui'
import type { FormInst, FormRules } from 'naive-ui'
import type { ServerInfo, SystemAsset, SystemAssetForm } from '@/types/system'
import { useSystemOptions } from '@/composables/useSystemOptions'
import ServerInfoForm from './ServerInfoForm.vue'
import OtherInfoForm from './OtherInfoForm.vue'

interface Props {
  show: boolean
  asset: SystemAsset | null
  mode: 'view' | 'edit' | 'create'
}

interface Emits {
  (e: 'update:show', show: boolean): void
  (e: 'save', data: SystemAssetForm): void
  (e: 'cancel'): void
  (e: 'edit'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 响应式数据
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const activeMainTab = ref('systemInfo')
const jsonError = ref('')

const formRef = ref<FormInst | null>(null)

const rules: FormRules = {
  assetNo: [
    { required: true, message: '资产编号不能为空', trigger: ['blur', 'input'] }
  ],
  projectName: [
    { required: true, message: '项目名称不能为空', trigger: ['blur', 'input'] }
  ],
  shortCode: [
    { required: true, message: '短代码不能为空', trigger: ['blur', 'input'] }
  ],
  businessDepartment: [
    { required: true, message: '业务部门不能为空', trigger: ['blur', 'input'] }
  ],
  pmOwner: [
    { required: true, message: 'PM负责人不能为空', trigger: ['blur', 'input'] }
  ],
  assetState: [
    { required: true, message: '资产状态不能为空', trigger: ['blur', 'input'] }
  ],
  region: [
    { required: true, message: '区域不能为空', trigger: ['blur', 'input'] }
  ],
  onlineDate: [
    { required: false, message: '上线日期不能为空', trigger: ['blur', 'input'] }
  ],
  mail: [
    { required: true, message: '邮箱不能为空', trigger: ['blur', 'input'] },
    { type: 'email', message: '邮箱格式不正确', trigger: ['blur', 'input'] }
  ],
  version: [
    { required: true, message: '版本不能为空', trigger: ['blur', 'input'] }
  ],
  language: [
    { required: true, message: '语言不能为空', trigger: ['blur', 'input'] }
  ],
  userCount: [
    { type: 'number', required: false, message: '用户数量不能为空', trigger: ['blur', 'input'] }
  ],
  usageStatus: [
    { required: true, message: '使用状态不能为空', trigger: ['blur', 'input'] }
  ],
  businessUser: [
    { required: true, message: '业务用户不能为空', trigger: ['blur', 'input'] }
  ],
  description: [
    { required: true, message: '描述不能为空', trigger: ['blur', 'input'] }
  ],
}

// 表单数据
const formData = reactive<SystemAssetForm>({
  assetNo: '',
  projectName: '',
  shortCode: '',
  businessDepartment: '',
  pmOwner: '',
  description: '',
  assetState: '',
  region: '',
  retireDate: null,
  onlineDate: null,
  mail: '',
  version: '',
  language: '',
  userCount: 0,
  usageStatus: '',
  businessUser: '',
  extraProperties: {}
})

// 服务器信息
const formServerInfo = reactive<ServerInfo>({
  platform: '',
  prodAppServer: '',
  prodDbName: '',
  prodDbServer: '',
  prodWebsite: '',
  qaAppServer: '',
  qaDbName: '',
  qaDbServer: '',
  qaWebsite: ''
});

const formOtherInfo = reactive<{[key: string]: any}>({});

// 选项数据
const regionOptions = ref<Array<{ label: string, value: string }>>([])
const businessDepartmentOptions = ref<Array<{ label: string, value: string }>>([])
const assetStateOptions = ref<Array<{ label: string, value: string }>>([])
const pmOwnerOptions = ref<Array<{ label: string, value: string }>>([])
const usageStatusOptions = ref<Array<{ label: string, value: string }>>([])

// 加载状态
const loadingRegions = ref(false)
const loadingBusinessDepartments = ref(false)
const loadingAssetStates = ref(false)
const loadingPmOwners = ref(false)
const loadingUsageStatuses = ref(false)

// 计算属性
const modalTitle = computed(() => {
  switch (props.mode) {
    case 'create':
      return '新建系统资产'
    case 'edit':
      return '编辑系统资产'
    case 'view':
    default:
      return '查看系统资产'
  }
})

const isEditing = computed(() => props.mode === 'edit' || props.mode === 'create')

// 初始化表单数据
const initFormData = () => {
  if (props.asset) {
    Object.assign(formData, {
      assetNo: props.asset.assetNo || '',
      projectName: props.asset.projectName || '',
      shortCode: props.asset.shortCode || '',
      businessDepartment: props.asset.businessDepartment || '',
      pmOwner: props.asset.pmOwner || '',
      description: props.asset.description || '',
      assetState: props.asset.assetState || '',
      region: props.asset.region || '',
      retireDate: props.asset.retireDate || null,
      onlineDate: props.asset.onlineDate || null,
      mail: props.asset.mail || '',
      version: props.asset.version || '',
      language: props.asset.language || '',
      userCount: props.asset.userCount || 0,
      usageStatus: props.asset.usageStatus || '',
      businessUser: props.asset.businessUser || '',
      extraProperties: props.asset.extraProperties || {}
    })

    // 回显服务器信息
    if (
      props.asset.extraProperties &&
      props.asset.extraProperties.serverInfo
    ) {
      Object.assign(formServerInfo, props.asset.extraProperties.serverInfo)
    } else {
      // 没有数据时重置
      Object.assign(formServerInfo, {
        prodAppServer: '',
        prodDbName: '',
        prodDbServer: '',
        prodWebsite: '',
        qaAppServer: '',
        qaDbName: '',
        qaDbServer: '',
        qaWebsite: '',
        platform: ''
      })
    }

    // 回显其他信息
    if (
      props.asset.extraProperties &&
      props.asset.extraProperties.otherInfo
    ) {
      Object.assign(formOtherInfo, props.asset.extraProperties.otherInfo)
    } else {
      // 没有数据时重置
      Object.assign(formOtherInfo, {})
    }

  } else {
    // 重置表单
    Object.assign(formData, {
      assetNo: '',
      projectName: '',
      shortCode: '',
      businessDepartment: '',
      pmOwner: '',
      description: '',
      assetState: '',
      region: '',
      retireDate: null,
      onlineDate: null,
      mail: '',
      version: '',
      language: '',
      userCount: 0,
      usageStatus: '',
      businessUser: '',
      extraProperties: {}
    })

    // 重置服务器信息
    Object.assign(formServerInfo, {
      prodAppServer: '',
      prodDbName: '',
      prodDbServer: '',
      prodWebsite: '',
      qaAppServer: '',
      qaDbName: '',
      qaDbServer: '',
      qaWebsite: '',
      platform: '',
    })

    // 重置其他信息
    Object.assign(formOtherInfo, {})
  }

  jsonError.value = ''
}

// 验证并更新额外属性
const validateAndUpdateExtraProperties = () => {
  try {
    const params = {
      ...formData.extraProperties
    };

    params.serverInfo = formServerInfo;
    params.otherInfo = formOtherInfo;

    formData.extraProperties = params;

    jsonError.value = ''
  } catch (error) {
    jsonError.value = 'JSON格式错误，请检查语法'
  }
}

// 事件处理
const handleSave = () => {
  formRef.value?.validate((errors) => {
    if (errors) {
      message.error('表单校验未通过，请检查输入项')
      return
    }
    // 校验通过后再校验JSON
    validateAndUpdateExtraProperties()
    if (jsonError.value) {
      message.error('请修正JSON格式错误')
      return
    }
    emit('save', {
      ...formData,
    })
  })
}

const handleCancel = () => {
  emit('cancel')
}

// 处理区域选择器点击事件
const handleRegionClick = async () => {
  if (regionOptions.value.length === 0 && !loadingRegions.value) {
    loadingRegions.value = true
    try {
      const { loadRegionOptions } = useSystemOptions()
      const regions = await loadRegionOptions()
      regionOptions.value = regions
    } catch (error) {
      console.error('获取区域选项失败:', error)
    } finally {
      loadingRegions.value = false
    }
  }
}

// 处理业务部门选择器点击事件
const handleBusinessDepartmentClick = async () => {
  if (businessDepartmentOptions.value.length === 0 && !loadingBusinessDepartments.value) {
    loadingBusinessDepartments.value = true
    try {
      const { loadBusinessDepartmentOptions } = useSystemOptions()
      const departments = await loadBusinessDepartmentOptions()
      businessDepartmentOptions.value = departments
    } catch (error) {
      console.error('获取业务部门选项失败:', error)
    } finally {
      loadingBusinessDepartments.value = false
    }
  }
}

// 处理资产状态选择器点击事件
const handleAssetStateClick = async () => {
  if (assetStateOptions.value.length === 0 && !loadingAssetStates.value) {
    loadingAssetStates.value = true
    try {
      const { loadAssetStateOptions } = useSystemOptions()
      const states = await loadAssetStateOptions()
      assetStateOptions.value = states
    } catch (error) {
      console.error('获取资产状态选项失败:', error)
    } finally {
      loadingAssetStates.value = false
    }
  }
}

// 处理PM负责人选择器点击事件
const handlePmOwnerClick = async () => {
  if (pmOwnerOptions.value.length === 0 && !loadingPmOwners.value) {
    loadingPmOwners.value = true
    try {
      const { loadPmOwnerOptions } = useSystemOptions()
      const users = await loadPmOwnerOptions()
      pmOwnerOptions.value = users
    } catch (error) {
      console.error('获取PM负责人选项失败:', error)
      message.error('获取PM负责人选项失败')
    } finally {
      loadingPmOwners.value = false
    }
  }
}

// 处理使用状态选择器点击事件
const handleUsageStatusClick = async () => {
  if (usageStatusOptions.value.length === 0 && !loadingUsageStatuses.value) {
    loadingUsageStatuses.value = true
    try {
      const { loadUsageStatusOptions } = useSystemOptions()
      const statuses = await loadUsageStatusOptions()
      usageStatusOptions.value = statuses
    } catch (error) {
      console.error('获取使用状态选项失败:', error)
    } finally {
      loadingUsageStatuses.value = false
    }
  }
}

const handleEdit = () => {
  emit('edit')
}

// 监听props变化
watch(() => props.show, (show) => {
  if (show) {
    initFormData()
    activeMainTab.value = 'systemInfo'
  }
})

watch(() => props.asset, () => {
  if (props.show) {
    initFormData()
  }
})
</script>

<style lang="less" scoped>
.modal-content {
  padding: 0;
}

.tab-content {
  padding: 16px 0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.full-width {
  grid-column: 1 / -1;
}

.json-editor-container {
  .json-error {
    margin-top: 8px;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
