<script setup lang="ts">
defineProps<{
  title: string;
  showCreateButton?: boolean;
  buttonText?: string;
}>();
</script>

<template>
  <div class="page-container">
    <div class="page-header">
      <h2>{{ title }}</h2>
      <slot name="header-extra"> </slot>
    </div>

    <div class="page-content">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import "@/assets/styles/variables.less";

.page-container {
  padding: 24px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: @bg-color;
  overflow: hidden;
  box-sizing: border-box;
}

.page-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;

  h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
    color: #1f2d3d;
    position: relative;
    padding-left: 12px;
    line-height: 1.4;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 4px;
      bottom: 4px;
      width: 4px;
      background: var(--primary-color);
      border-radius: 2px;
    }
  }
}

.page-content {
  flex: 1;
  min-height: 0;
  overflow: auto;
  display: flex;
  flex-direction: column;
}
</style>
