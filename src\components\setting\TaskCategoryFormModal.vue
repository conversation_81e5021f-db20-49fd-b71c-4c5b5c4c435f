<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="modalTitle"
    style="width: 500px"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="分类代码" path="code">
        <n-input
          v-model:value="formData.code"
          placeholder="请输入分类代码"
          :disabled="mode === 'edit'"
        />
      </n-form-item>
      
      <n-form-item label="分类名称" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="请输入分类名称"
        />
      </n-form-item>
      
      <n-form-item label="描述">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入描述"
          :rows="3"
        />
      </n-form-item>
      
      <n-form-item label="排序号">
        <n-input-number
          v-model:value="formData.sortOrder"
          placeholder="请输入排序号"
          :min="0"
          style="width: 100%"
        />
      </n-form-item>
      
      <n-form-item label="启用状态" path="enabled">
        <n-switch v-model:value="formData.enabled" />
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ mode === 'create' ? '创建' : '更新' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { TaskCategoryApi, type TaskCategory, type CreateTaskCategoryParams, type UpdateTaskCategoryParams } from '@/apis/taskCategory'

interface Props {
  show: boolean
  mode: 'create' | 'edit'
  categoryData?: Partial<TaskCategory>
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  categoryData: () => ({})
})

const emit = defineEmits<Emits>()

const message = useMessage()
const formRef = ref<FormInst>()
const submitting = ref(false)

// 双向绑定显示状态
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 模态框标题
const modalTitle = computed(() => {
  return props.mode === 'create' ? '新增任务类型' : '编辑任务类型'
})

// 表单数据
const formData = reactive<Partial<TaskCategory>>({
  code: '',
  name: '',
  description: '',
  sortOrder: 0,
  enabled: true
})

// 表单验证规则
const rules: FormRules = {
  code: [
    { required: true, message: '请输入分类代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '分类代码只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ]
}

// 监听分类数据变化，更新表单
watch(() => props.categoryData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      code: newData.code || '',
      name: newData.name || '',
      description: newData.description || '',
      sortOrder: newData.sortOrder || 0,
      enabled: newData.enabled !== undefined ? newData.enabled : true
    })
  }
}, { immediate: true })

// 监听模态框显示状态，重置表单
watch(() => props.show, (show) => {
  if (show && props.mode === 'create') {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    code: '',
    name: '',
    description: '',
    sortOrder: 0,
    enabled: true
  })
  formRef.value?.restoreValidation()
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const validationResult = await formRef.value?.validate()
    if (validationResult && Array.isArray(validationResult) && validationResult.length > 0) {
      // 验证失败，显示第一个错误信息
      const firstError = validationResult[0]
      if (firstError && firstError.message) {
        message.error(firstError.message)
      } else {
        message.error('表单验证失败，请检查输入内容')
      }
      return
    }

    submitting.value = true

    if (props.mode === 'create') {
      const createData: CreateTaskCategoryParams = {
        code: formData.code!,
        name: formData.name!,
        description: formData.description,
        sortOrder: formData.sortOrder || 0,
        enabled: formData.enabled!
      }
      await TaskCategoryApi.createTaskCategory(createData)
      message.success('创建成功')
    } else {
      const updateData: UpdateTaskCategoryParams = {
        id: props.categoryData!.id!,
        code: formData.code!,
        name: formData.name!,
        description: formData.description,
        sortOrder: formData.sortOrder || 0,
        enabled: formData.enabled!
      }
      await TaskCategoryApi.updateTaskCategory(updateData)
      message.success('更新成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)

    // 更详细的错误处理
    if (error && typeof error === 'object') {
      if ('response' in error && error.response) {
        const response = error.response as any
        if (response.data && response.data.message) {
          message.error(response.data.message)
        } else if (response.status === 400) {
          message.error('请求参数错误，请检查输入内容')
        } else if (response.status === 500) {
          message.error('服务器内部错误，请稍后重试')
        } else {
          message.error(`请求失败: ${response.status}`)
        }
      } else if ('message' in error) {
        message.error(error.message as string)
      } else {
        message.error('操作失败，请稍后重试')
      }
    } else {
      message.error('操作失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

// 取消
const handleCancel = () => {
  showModal.value = false
}
</script>
