/* ===== 基础样式重置 ===== */
/* 与 global.less 协同工作，避免重复定义 */

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--text-color);
  background: var(--bg-color);
  transition:
    color var(--transition-duration),
    background-color var(--transition-duration);
  line-height: 1.5;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    'Noto Sans',
    sans-serif;
  font-size: 14px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接基础样式 */
a {
  text-decoration: none;
  color: var(--primary-color);
  transition: color var(--transition-duration);
}

a:hover {
  color: var(--primary-color);
  opacity: 0.8;
}
