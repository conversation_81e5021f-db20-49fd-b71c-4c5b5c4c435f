// 任务状态
export type TaskStatus = 'open' | 'inprogress' | 'toverify' | 'close'

// 项目详情页面任务状态类型（中文显示）
export type ProjectDetailTaskStatus = '未开始' | '进行中' | '已完成' | '已延期' | '已取消' | '待验证'

// 任务接口定义
export interface Task {
  id: number // 序号
  taskNo: string // 任务编号
  title: string // 标题
  issueType: string // 问题类型
  assignee: string // 指派人
  assigneeAvatar?: string // 指派人头像
  reporter: string // 报告人
  reporterAvatar?: string // 报告人头像
  description?: string // 描述
  status: TaskStatus // 状态
  plannedStartTime?: string // 计划开始时间
  plannedEndTime?: string // 计划结束时间
  actualStartTime?: string // 实际开始时间
  actualEndTime?: string // 实际结束时间
  duration?: number // 时长(小时)
  priority: string // 优先级
  projectId?: number
  projectName?: string
  stageName?: string // 阶段名称
}

// 项目详情页面任务接口（简化版本，用于项目详情页面显示）
export interface ProjectDetailTask {
  id: number
  name: string
  assignee: string
  status: ProjectDetailTaskStatus
  priority: string
  dueDate: string
  avatar?: string
}

// 任务问题类型配置
export const TASK_ISSUE_TYPE_CONFIG: Record<string, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
  '任务': { text: '任务', type: 'default' },
  'QA问题': { text: 'QA问题', type: 'error' },
  'QA变更': { text: 'QA变更', type: 'warning' },
  'UAT问题': { text: 'UAT问题', type: 'error' },
  'UAT变更': { text: 'UAT变更', type: 'warning' },
  'MA变更': { text: 'MA变更', type: 'info' },
  'MA问题': { text: 'MA问题', type: 'error' }
}

// 任务状态配置
export const TASK_STATUS_CONFIG: Record<TaskStatus, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
  'open': { text: '待办', type: 'default' },
  'inprogress': { text: '进行中', type: 'primary' },
  'toverify': { text: '待验证', type: 'warning' },
  'close': { text: '已完成', type: 'success' }
}

// 任务优先级配置
export const TASK_PRIORITY_CONFIG: Record<string, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
  'Low': { text: 'Low', type: 'info' },
  'Medium': { text: 'Medium', type: 'success' },
  'High': { text: 'High', type: 'warning' },
  'Urgent': { text: 'Urgent', type: 'error' }
}

// 任务状态选项
export const TASK_STATUS_OPTIONS = [
  { label: '待办', value: 'open' as TaskStatus },
  { label: '进行中', value: 'inprogress' as TaskStatus },
  { label: '待验证', value: 'toverify' as TaskStatus },
  { label: '已完成', value: 'close' as TaskStatus }
]

// 获取问题类型显示信息
export const getIssueTypeInfo = (issueType: string) => {
  return TASK_ISSUE_TYPE_CONFIG[issueType] || { text: issueType, type: 'default' as const }
}

// 获取状态显示信息
export const getStatusInfo = (status: TaskStatus) => {
  return TASK_STATUS_CONFIG[status] || { text: status, type: 'default' as const }
}

// 获取优先级显示信息
export const getPriorityInfo = (priority: string) => {
  return TASK_PRIORITY_CONFIG[priority] || { text: priority, type: 'default' as const }
}

import { formatDate as formatDateTime, DATE_FORMATS } from '@/utils/dateTime'

// 格式化日期
export const formatDate = (dateStr?: string) => {
  if (!dateStr) return '-'
  return formatDateTime(dateStr, DATE_FORMATS.DATE) || '-'
}

// 任务操作类型
export interface TaskOperation {
  operation: string
  name: string
  targetStatus: string
  description?: string
}

// 任务状态流转路径
export interface TaskTransitions {
  [operation: string]: string // operation -> targetStatus
}

// 看板列配置
export interface KanbanColumn {
  key: string
  title: string
  status: TaskStatus
  color: string
  allowDrop?: boolean // 是否允许拖拽到此列
}

// 看板列配置
export const KANBAN_COLUMNS: KanbanColumn[] = [
  { key: 'open', title: '待办', status: 'open' as TaskStatus, color: '#d9d9d9' },
  { key: 'inprogress', title: '进行中', status: 'inprogress' as TaskStatus, color: '#1890ff' },
  { key: 'toverify', title: '待验证', status: 'toverify' as TaskStatus, color: '#fa8c16' },
  { key: 'close', title: '已完成', status: 'close' as TaskStatus, color: '#52c41a' }
]
