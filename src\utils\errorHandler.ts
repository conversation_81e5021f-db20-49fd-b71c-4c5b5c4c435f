// 统一错误处理工具
import { createDiscreteApi } from 'naive-ui'

// 创建独立的消息 API 实例
let messageApi: ReturnType<typeof createDiscreteApi>['message'] | null = null

// 初始化消息 API
function initMessageApi() {
  if (typeof window !== 'undefined' && !messageApi) {
    try {
      const { message } = createDiscreteApi(['message'])
      messageApi = message
    } catch (error) {
      console.warn('Failed to initialize message API:', error)
    }
  }
}

// 全局消息提示函数
export function showMessage(type: 'success' | 'error' | 'warning' | 'info', content: string) {
  if (!messageApi) {
    initMessageApi()
  }

  if (messageApi) {
    messageApi[type](content)
  } else {
    // 降级到控制台输出
    console.log(`[${type.toUpperCase()}] ${content}`)
  }
}

/**
 * 处理 API 错误并返回用户友好的错误消息
 */
export function handleApiError(error: any, defaultMessage: string): string {
  // 400 错误 - 请求参数错误
  if (error.status === 400) {
    return error.message || '请求参数错误，请检查输入信息'
  }

  // 401 错误 - 未授权
  if (error.status === 401) {
    return '登录已过期，请重新登录'
  }

  // 403 错误 - 权限不足
  if (error.status === 403) {
    return '权限不足，无法执行此操作'
  }

  // 404 错误 - 资源不存在
  if (error.status === 404) {
    return '请求的资源不存在'
  }

  // 409 错误 - 冲突
  if (error.status === 409) {
    return error.message || '数据冲突，请刷新后重试'
  }

  // 422 错误 - 数据验证失败
  if (error.status === 422) {
    return error.message || '数据验证失败，请检查输入信息'
  }

  // 500 错误 - 服务器内部错误
  if (error.status === 500) {
    return '服务器内部错误，请稍后重试'
  }

  // 502/503/504 错误 - 服务不可用
  if (error.status >= 502 && error.status <= 504) {
    return '服务暂时不可用，请稍后重试'
  }

  // 网络错误
  if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
    return '网络连接失败，请检查网络连接'
  }

  // 返回默认错误消息或服务器返回的消息
  return error.message || defaultMessage
}

/**
 * 创建安全的消息提示函数 (已废弃，请使用 showMessage)
 * @deprecated 请直接使用 showMessage 函数
 */
export function createSafeMessage() {
  return showMessage
}
