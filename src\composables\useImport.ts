import { ref } from 'vue'
import { createDiscreteApi } from 'naive-ui'

export interface ImportResult {
  total: number
  success: number
  failed: number
  skipped?: number
  errors?: Array<{
    row: number
    field: string
    message: string
  }>
}

export interface ImportData {
  file: File
  mode: 'incremental' | 'overwrite'
  projectId?: number
  projectName?: string
}

export function useImport() {
  // 使用离散API创建消息实例，避免在组件外部使用useMessage的问题
  const { message } = createDiscreteApi(['message'])
  const importing = ref(false)
  const importProgress = ref(0)
  const importResult = ref<ImportResult | null>(null)

  let progressInterval: number | null = null

  // 开始导入
  const startImport = () => {
    importing.value = true
    importProgress.value = 0
    importResult.value = null
  }

  // 开始SSE导入
  const startSSEImport = () => {
    importing.value = true
    importProgress.value = 0
    importResult.value = null

    // 清除之前的定时器
    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
  }

  // 处理导入成功
  const handleImportSuccess = (result: ImportResult) => {
    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
    
    importing.value = false
    importProgress.value = 100

    // 确保必要字段存在，如果不存在则设置默认值
    const normalizedResult = {
      total: result.total || 0,
      success: result.success || 0,
      failed: result.failed || 0,
      skipped: result.skipped || 0,
      errors: result.errors || []
    }

    importResult.value = normalizedResult

    // 显示结果消息
    if (normalizedResult.failed === 0) {
      message.success(`导入成功！共导入 ${normalizedResult.success} 条记录`)
      return 'success'
    } else if (normalizedResult.success > 0) {
      message.warning(`部分导入成功！成功 ${normalizedResult.success} 条，失败 ${normalizedResult.failed} 条`)
      return 'partial'
    } else {
      message.error('导入失败！请检查文件格式和数据')
      return 'failed'
    }
  }

  // 处理导入失败
  const handleImportError = (error: any) => {
    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
    
    importing.value = false
    importProgress.value = 0
    importResult.value = null

    console.error('导入失败:', error)
    message.error('导入失败，请稍后重试')
  }

  // 重置状态
  const resetImport = () => {
    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
    
    importing.value = false
    importProgress.value = 0
    importResult.value = null
  }

  // SSE导入进度处理
  const handleSSEProgress = (data: any) => {
    if (data.processed && data.total) {
      importProgress.value = Math.round((data.processed / data.total) * 100)
    }
  }

  // SSE导入完成处理
  const handleSSEComplete = (data: any): 'success' | 'partial' | 'failed' => {
    console.log('handleSSEComplete被调用，数据:', data)

    importing.value = false
    importProgress.value = 100

    const normalizedResult: ImportResult = {
      total: data.total || 0,
      success: data.success || 0,
      failed: data.failed || 0,
      skipped: data.skipped || 0,
      errors: data.errors || []
    }

    console.log('标准化结果:', normalizedResult)
    importResult.value = normalizedResult

    // 显示结果消息
    console.log('准备显示消息，normalizedResult:', normalizedResult)
    console.log('normalizedResult类型:', typeof normalizedResult)
    console.log('normalizedResult.failed:', normalizedResult?.failed)
    console.log('normalizedResult.success:', normalizedResult?.success)
    console.log('message对象:', message)

    try {
      if (!message) {
        console.error('message对象未初始化')
        return 'failed'
      }

      if (normalizedResult && normalizedResult.failed === 0) {
        console.log('显示成功消息')
        console.log('准备调用message.success，参数:', `导入成功！共导入 ${normalizedResult.success} 条记录`)
        message.success(`导入成功！共导入 ${normalizedResult.success} 条记录`)
        return 'success'
      } else if (normalizedResult && normalizedResult.success > 0) {
        console.log('显示部分成功消息')
        message.warning(`部分导入成功！成功 ${normalizedResult.success} 条，失败 ${normalizedResult.failed} 条`)
        return 'partial'
      } else {
        console.log('显示失败消息')
        message.error('导入失败！请检查文件格式和数据')
        return 'failed'
      }
    } catch (error) {
      console.error('显示消息时发生错误:', error)
      console.error('错误堆栈:', error instanceof Error ? error.stack : 'No stack trace')
      return 'failed'
    }
  }

  // SSE导入错误处理
  const handleSSEError = (error: any) => {
    importing.value = false
    importProgress.value = 0
    importResult.value = null

    console.error('SSE导入失败:', error)

    // 安全地获取错误消息
    let errorMessage = '导入失败，请稍后重试'
    if (error) {
      if (typeof error === 'string') {
        errorMessage = error
      } else if (error.message) {
        errorMessage = error.message
      } else if (error.data && error.data.message) {
        errorMessage = error.data.message
      } else if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message
      }
    }

    if (message) {
      message.error(errorMessage)
    } else {
      console.error('无法显示错误消息，message对象未初始化:', errorMessage)
    }
  }

  return {
    importing,
    importProgress,
    importResult,
    startImport,
    startSSEImport,
    handleImportSuccess,
    handleImportError,
    handleSSEProgress,
    handleSSEComplete,
    handleSSEError,
    resetImport
  }
}
