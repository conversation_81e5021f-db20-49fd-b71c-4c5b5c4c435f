package com.vwatj.ppms.agent.llm;

import com.alibaba.fastjson2.JSONObject;
import com.vwatj.ppms.dto.ChatRequestDTO;
import com.vwatj.ppms.dto.ChatResponseDTO;
import com.vwatj.ppms.entity.Agent;

/**
 * LLM提供商抽象接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public abstract class LLMProvider {

    /**
     * 获取提供商名称
     */
    public abstract String getProviderName();

    /**
     * 检查是否支持该提供商
     */
    public abstract boolean supports(String provider);

    /**
     * 发送聊天请求
     *
     * @param agent 代理配置
     * @param request 聊天请求
     * @return 聊天响应
     */
    public abstract ChatResponseDTO chat(Agent agent, ChatRequestDTO request);

    /**
     * 发送流式聊天请求
     *
     * @param agent 代理配置
     * @param request 聊天请求
     * @return 流式响应
     */
    public abstract String streamChat(Agent agent, ChatRequestDTO request);

    /**
     * 验证配置是否有效
     *
     * @param agent 代理配置
     * @return 是否有效
     */
    public abstract boolean validateConfig(Agent agent);

    /**
     * 构建完整的提示词
     *
     * @param agent 代理配置
     * @param request 聊天请求
     * @return 完整提示词
     */
    protected String buildFullPrompt(Agent agent, ChatRequestDTO request) {
        StringBuilder prompt = new StringBuilder();
        
        // 添加系统提示词
        if (agent.getSystemPrompt() != null && !agent.getSystemPrompt().isEmpty()) {
            prompt.append("System: ").append(agent.getSystemPrompt()).append("\n\n");
        }
        
        // 添加上下文信息
        String contextInfo = buildContextInfo(request);
        if (!contextInfo.isEmpty()) {
            prompt.append("Context: ").append(contextInfo).append("\n\n");
        }
        
        // 添加对话历史
        if (request.getHistory() != null && !request.getHistory().isEmpty()) {
            prompt.append("History:\n");
            for (ChatRequestDTO.ChatMessageDTO msg : request.getHistory()) {
                prompt.append(msg.getRole()).append(": ").append(msg.getContent()).append("\n");
            }
            prompt.append("\n");
        }
        
        // 添加用户消息
        String userPrompt = buildUserPrompt(agent, request);
        prompt.append("User: ").append(userPrompt);
        
        return prompt.toString();
    }

    /**
     * 构建用户提示词
     */
    protected String buildUserPrompt(Agent agent, ChatRequestDTO request) {
        String template = agent.getUserPromptTemplate();
        if (template == null || template.isEmpty()) {
            return request.getMessage();
        }
        
        // 简单的模板替换
        return template
                .replace("{message}", request.getMessage())
                .replace("{projectId}", request.getProjectId() != null ? request.getProjectId().toString() : "")
                .replace("{taskId}", request.getTaskId() != null ? request.getTaskId().toString() : "")
                .replace("{userId}", request.getUserId() != null ? request.getUserId() : "");
    }

    /**
     * 构建上下文信息
     */
    protected String buildContextInfo(ChatRequestDTO request) {
        StringBuilder context = new StringBuilder();
        
        if (request.getUserId() != null) {
            context.append("User ID: ").append(request.getUserId()).append("\n");
        }
        
        if (request.getProjectId() != null) {
            context.append("Project ID: ").append(request.getProjectId()).append("\n");
        }
        
        if (request.getTaskId() != null) {
            context.append("Task ID: ").append(request.getTaskId()).append("\n");
        }
        
        if (request.getContext() != null && !request.getContext().isEmpty()) {
            context.append("Additional Context: ").append(request.getContext()).append("\n");
        }
        
        return context.toString();
    }

    /**
     * 解析LLM参数
     */
    protected LLMParams parseLLMParams(Agent agent) {
        JSONObject paramsJson = agent.getLlmParams();
        if (paramsJson == null || paramsJson.isEmpty()) {
            return new LLMParams();
        }
        
        try {
            // 这里可以使用JSON解析库，简化起见使用默认值
            LLMParams params = new LLMParams();
            params.setTemperature(0.7f);
            params.setMaxTokens(4000);
            params.setTopP(1.0f);
            return params;
        } catch (Exception e) {
            return new LLMParams();
        }
    }

    /**
     * LLM参数配置类
     */
    public static class LLMParams {
        private float temperature = 0.7f;
        private int maxTokens = 4000;
        private float topP = 1.0f;
        private int topK = 50;
        private String[] stopSequences;

        // getters and setters
        public float getTemperature() { return temperature; }
        public void setTemperature(float temperature) { this.temperature = temperature; }
        
        public int getMaxTokens() { return maxTokens; }
        public void setMaxTokens(int maxTokens) { this.maxTokens = maxTokens; }
        
        public float getTopP() { return topP; }
        public void setTopP(float topP) { this.topP = topP; }
        
        public int getTopK() { return topK; }
        public void setTopK(int topK) { this.topK = topK; }
        
        public String[] getStopSequences() { return stopSequences; }
        public void setStopSequences(String[] stopSequences) { this.stopSequences = stopSequences; }
    }
}
