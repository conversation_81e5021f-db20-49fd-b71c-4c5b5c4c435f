// 项目相关工具函数

import type { Project, ProjectStatus } from '@/types'

/**
 * 项目状态配置
 */
export const PROJECT_STATUS_CONFIG = {
  'request collect': {
    text: '需求收集',
    type: 'default' as const,
    color: '#d9d9d9',
    bgColor: '#fafafa'
  },
  'planning': {
    text: '规划中',
    type: 'warning' as const,
    color: '#faad14',
    bgColor: '#fffbe6'
  },
  'tab': {
    text: 'TAB评审',
    type: 'info' as const,
    color: '#1890ff',
    bgColor: '#e6f7ff'
  },
  'development': {
    text: '开发中',
    type: 'primary' as const,
    color: '#2d8cf0',
    bgColor: '#e6f7ff'
  },
  'inner test': {
    text: '内测',
    type: 'info' as const,
    color: '#1890ff',
    bgColor: '#e6f7ff'
  },
  'uat': {
    text: 'UAT测试',
    type: 'info' as const,
    color: '#1890ff',
    bgColor: '#e6f7ff'
  },
  'cab': {
    text: 'CAB评审',
    type: 'info' as const,
    color: '#1890ff',
    bgColor: '#e6f7ff'
  },
  'go live': {
    text: '已上线',
    type: 'success' as const,
    color: '#52c41a',
    bgColor: '#f6ffed'
  }
} as const

/**
 * 获取项目状态信息
 */
export function getProjectStatusInfo(status: string) {
  return PROJECT_STATUS_CONFIG[status as keyof typeof PROJECT_STATUS_CONFIG] || {
    text: status,
    type: 'default' as const,
    color: '#d9d9d9',
    bgColor: '#fafafa'
  }
}

/**
 * 计算项目基于时间的进度百分比
 * @param project 项目对象
 * @returns 进度百分比 (0-100)
 */
export function calculateTimeProgress(project: Project): number {
  // 如果没有开始时间或结束时间，返回0
  if (!project.startDate || !project.endDate) {
    return 0
  }

  try {
    // 转换为数字时间戳
    let startTime: number
    let endTime: number

    if (typeof project.startDate === 'string') {
      startTime = new Date(project.startDate).getTime()
    } else {
      startTime = Number(project.startDate)
    }

    if (typeof project.endDate === 'string') {
      endTime = new Date(project.endDate).getTime()
    } else {
      endTime = Number(project.endDate)
    }

    // 检查转换后的时间戳是否有效
    if (isNaN(startTime) || isNaN(endTime)) {
      console.warn('Invalid timestamp in calculateTimeProgress:', { startTime, endTime, project })
      return 0
    }

    const now = Date.now()

    // 如果当前时间在开始时间之前，进度为0
    if (now < startTime) {
      return 0
    }

    // 如果当前时间在结束时间之后，进度为100
    if (now > endTime) {
      return 100
    }

    // 计算时间进度百分比
    const totalDuration = endTime - startTime
    const elapsedTime = now - startTime
    const timeProgress = Math.round((elapsedTime / totalDuration) * 100)

    return Math.max(0, Math.min(100, timeProgress))
  } catch (error) {
    console.error('Error in calculateTimeProgress:', error, project)
    return 0
  }
}

/**
 * 获取项目的实际进度（包含状态修正）
 * @param project 项目对象
 * @returns 实际进度百分比 (0-100)
 */
export function getProjectProgress(project: Project): number {
  const timeProgress = calculateTimeProgress(project)

  // 根据项目状态调整进度
  switch (project.status) {
    case 'request collect':
      return Math.min(timeProgress, 5)
    case 'go live':
      return 100
    case 'planning':
      return Math.min(timeProgress, 10)
    case 'tab':
      return Math.min(timeProgress, 15)
    case 'development':
      return Math.min(timeProgress, 80)
    case 'inner test':
    case 'uat':
    case 'cab':
      return timeProgress
    default:
      return timeProgress
  }
}

/**
 * 判断项目是否延期
 * @param project 项目对象
 * @returns 是否延期
 */
export function isProjectDelayed(project: Project): boolean {
  if (!project.endDate || project.status === 'go live') {
    return false
  }

  const now = Date.now()
  return now > project.endDate
}

/**
 * 获取项目剩余天数
 * @param project 项目对象
 * @returns 剩余天数，负数表示已延期
 */
export function getProjectRemainingDays(project: Project): number {
  if (!project.endDate) {
    return 0
  }

  const now = Date.now()
  const remainingTime = project.endDate - now
  return Math.ceil(remainingTime / (24 * 60 * 60 * 1000))
}

/**
 * 格式化项目持续时间
 * @param project 项目对象
 * @returns 格式化的持续时间字符串
 */
export function formatProjectDuration(project: Project): string {
  if (!project.startDate || !project.endDate) {
    return '未设置'
  }

  const duration = project.endDate - project.startDate
  const days = Math.ceil(duration / (24 * 60 * 60 * 1000))

  if (days < 30) {
    return `${days}天`
  } else if (days < 365) {
    const months = Math.ceil(days / 30)
    return `${months}个月`
  } else {
    const years = Math.floor(days / 365)
    const remainingMonths = Math.ceil((days % 365) / 30)
    return remainingMonths > 0 ? `${years}年${remainingMonths}个月` : `${years}年`
  }
}

/**
 * 获取项目状态的显示文本
 * @param status 项目状态
 * @returns 状态显示文本
 */
export function getProjectStatusText(status: ProjectStatus): string {
  return getProjectStatusInfo(status).text
}

/**
 * 获取项目状态标签的类型
 * @param status 项目状态
 * @returns 标签类型
 */
export function getProjectStatusType(status: string) {
  return getProjectStatusInfo(status).type
}

/**
 * 获取进度条颜色
 * @param progress 进度百分比
 * @returns 颜色值
 */
export function getProgressColor(progress: number): string {
  if (progress >= 100) return '#19be6b'  // 绿色 - 已完成
  if (progress >= 80) return '#52c41a'   // 浅绿色 - 接近完成
  if (progress >= 60) return '#2d8cf0'   // 蓝色 - 进行中
  if (progress >= 40) return '#ff9900'   // 橙色 - 进度一般
  return '#faad14'                       // 黄色 - 刚开始
}

/**
 * 创建带有计算进度的项目对象
 * @param projectData 原始项目数据
 * @returns 包含计算进度的项目对象
 */
export function createProjectWithProgress(projectData: Omit<Project, 'progress'>): Project {
  const project = projectData as Project
  
  // 使用 Object.defineProperty 定义只读的 progress 属性
  Object.defineProperty(project, 'progress', {
    get() {
      return getProjectProgress(this)
    },
    enumerable: true,
    configurable: false
  })

  return project
}

/**
 * 计算项目统计数据
 * @param projects 项目列表
 * @returns 统计数据
 */
export function calculateProjectStats(projects: Project[]) {
  const total = projects.length
  const inProgress = projects.filter(p =>
    ['development', 'inner test', 'uat', 'cab'].includes(p.status)
  ).length
  const completed = projects.filter(p => p.status === 'go live').length
  const delayed = projects.filter(p => isProjectDelayed(p)).length

  return {
    total,
    inProgress,
    completed,
    delayed
  }
}

/**
 * 格式化项目日期显示
 * @param timestamp 时间戳或日期字符串
 * @returns 格式化的日期字符串
 */
export function formatProjectDate(timestamp: number | string | null | undefined): string {
  if (!timestamp) return '--'

  try {
    let date: Date

    if (typeof timestamp === 'string') {
      // 处理字符串格式的日期
      date = new Date(timestamp)
    } else {
      // 处理数字时间戳
      date = new Date(Number(timestamp))
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('Invalid date in formatProjectDate:', timestamp)
      return '--'
    }

    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    console.error('Error formatting project date:', error, timestamp)
    return '--'
  }
}

/**
 * 格式化项目日期时间显示
 * @param timestamp 时间戳或日期字符串
 * @returns 格式化的日期时间字符串
 */
export function formatProjectDateTime(timestamp: number | string | null | undefined): string {
  if (!timestamp) return '--'

  try {
    let date: Date

    if (typeof timestamp === 'string') {
      // 处理字符串格式的日期
      date = new Date(timestamp)
    } else {
      // 处理数字时间戳
      date = new Date(Number(timestamp))
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('Invalid date in formatProjectDateTime:', timestamp)
      return '--'
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    console.error('Error formatting project datetime:', error, timestamp)
    return '--'
  }
}

/**
 * 格式化时间范围显示
 * @param startTime 开始时间戳
 * @param endTime 结束时间戳
 * @param prefix 前缀文本
 * @returns 格式化的时间范围字符串
 */
export function formatTimeRange(
  startTime: number | null | undefined,
  endTime: number | null | undefined,
  prefix: string = ''
): string | null {
  const start = formatProjectDate(startTime)
  const end = formatProjectDate(endTime)

  if (start === '--' && end === '--') {
    return null // 如果都没有时间，返回null表示不显示
  }

  const range = `${start} ~ ${end}`
  return prefix ? `${prefix}: ${range}` : range
}

/**
 * 计算阶段进度（用于项目流程）
 * @param stage 阶段对象
 * @returns 进度百分比
 */
export function calculateStageProgress(stage: any): number {
  switch (stage.status) {
    case 'pending':
      return 0
    case 'completed':
      return 100
    case 'active':
      // 基于当前日期在计划日期段的位置比例计算
      if (stage.plannedStartTime && stage.plannedEndTime) {
        const now = new Date().getTime()
        const startTime = new Date(stage.plannedStartTime).getTime()
        const endTime = new Date(stage.plannedEndTime).getTime()

        if (now <= startTime) {
          return 0
        } else if (now >= endTime) {
          return 99
        } else {
          const totalDuration = endTime - startTime
          const elapsedTime = now - startTime
          return Math.round((elapsedTime / totalDuration) * 99)
        }
      }
      return 50
    case 'skipped':
      return 0
    default:
      return stage.progress || 0
  }
}
