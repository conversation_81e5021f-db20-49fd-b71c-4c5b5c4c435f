package com.vwatj.ppms.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vwatj.ppms.common.BaseEntity;
import com.vwatj.ppms.enums.AgentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Agent实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent")
public class Agent extends BaseEntity {
    
    /**
     * Agent ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * Agent名称
     */
    private String name;
    
    /**
     * Agent描述
     */
    private String description;
    
    /**
     * Agent状态 (0-禁用, 1-启用)
     */
    private Integer status;
    
    /**
     * Agent类型
     */
    private AgentTypeEnum type;
    
    /**
     * Agent配置信息 (JSON格式)
     */
    private String config;

    /**
     * LLM提供商 (openai, deepseek, qwen, etc.)
     */
    private String llmProvider;

    /**
     * LLM模型名称
     */
    private String llmModel;

    /**
     * LLM API URL
     */
    private String llmApiUrl;

    /**
     * LLM API Key
     */
    private String llmApiKey;

    /**
     * LLM参数配置 (JSON格式: temperature, max_tokens, etc.)
     */
    private JSONObject llmParams;

    /**
     * 系统提示词
     */
    private String systemPrompt;

    /**
     * 用户提示词模板
     */
    private String userPromptTemplate;

    /**
     * 功能配置 (JSON格式: 启用的功能列表)
     */
    private String functionConfig;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 实现类名（自定义类型Agent使用）
     */
    private String implementationClass;
}
