<script setup lang="ts">
import PageLayout from "@/layouts/PageLayout.vue";
import ProjectFormModal from "@/components/project/ProjectFormModal.vue";
import ProjectGrid from "@/components/project/ProjectGrid.vue";
import { useProjectStore } from "@/stores/project";
import { useSystemAssetStore } from "@/stores/systemAsset";
import { calculateProjectStats } from "@/utils/project";
import { useDialog } from 'naive-ui'

const dialog = useDialog()

import type { Project, StatCard } from "@/types";

const router = useRouter();

// 使用项目 Store
const projectStore = useProjectStore();
const systemAssetStore = useSystemAssetStore();

// 模态框状态
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingProject = ref<Partial<Project>>({});

// 项目统计
const stats = ref<StatCard[]>([
  { title: "进行中", value: 0, color: "#2d8cf0", trend: "up", trendValue: 0 },
  { title: "已完成", value: 0, color: "#19be6b", trend: "up", trendValue: 0 },
  { title: "已延期", value: 0, color: "#ed4014", trend: "down", trendValue: 0 },
  {
    title: "总项目数",
    value: 0,
    color: "#ff9900",
    trend: "up",
    trendValue: 0,
  },
]);

const activeTab = ref("follow");

// 使用 Store 中的数据
const projectList = computed(() => projectStore.projects);
const starredProjects = computed(() => projectStore.starredProjects);
const inProgressProjects = computed(() => projectStore.inProgressProjects);
const completedProjects = computed(() => projectStore.completedProjects);
const archivedProjects = computed(() => projectStore.archivedProjects);
const loading = computed(() => projectStore.loading);

// 初始化数据
const initData = async () => {
  try {
    systemAssetStore.fetchAssets({ page:1, pageSize: 1000 }) // 获取所有资产用于显示名称
    // 并行加载项目和系统资产数据
    await projectStore.fetchProjects();
    updateStats();
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
};

// 更新统计数据
const updateStats = () => {
  const projects = projectStore.projects;
  const statsData = calculateProjectStats(projects);

  stats.value = [
    { title: "进行中", value: statsData.inProgress, color: "#2d8cf0", trend: "up", trendValue: 0 },
    { title: "已完成", value: statsData.completed, color: "#19be6b", trend: "up", trendValue: 0 },
    { title: "已延期", value: statsData.delayed, color: "#ed4014", trend: "down", trendValue: 0 },
    {
      title: "总项目数",
      value: statsData.total,
      color: "#ff9900",
      trend: "up",
      trendValue: 0,
    },
  ];
};

const handleCreateProject = () => {
  showCreateModal.value = true;
};

// 跳转到项目详情页
const goToProjectDetail = (projectId: string | number) => {
  router.push({ name: "ProjectDetail", params: { id: projectId } });
};

const toggleStar = async (id: string | number) => {
  try {
    await projectStore.toggleStar(id);
  } catch (error) {
    console.error('切换关注状态失败:', error);
  }
};

// 编辑项目
const handleEditProject = (projectId: string | number) => {
  const project = projectList.value.find((p) => p.id === projectId);
  if (project) {
    editingProject.value = { ...project };
    showEditModal.value = true;
  }
};

// 删除项目
const handleDeleteProject = async (projectId: string | number) => {
  const project = projectList.value.find((p) => p.id === projectId);
  if (project) {
    dialog.warning({
      title: '删除确认',
      content: `确定要删除项目"${project.name}"吗？此操作不可恢复。`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await projectStore.deleteProject(projectId);
          updateStats();
        } catch (error) {
          console.error('删除项目失败:', error);
        }
      }
    })
  }
};



// 处理创建项目成功
const handleCreateSuccess = async (projectData: Partial<Project>) => {
  try {
    await projectStore.createProject(projectData as any);
    updateStats();
    // 关闭模态框
    showCreateModal.value = false;
    activeTab.value = 'all';
  } catch (error) {
    console.error('创建项目失败:', error);
    // 如果创建失败，重新打开模态框让用户修改
    showCreateModal.value = true;
  }
};

// 处理编辑项目成功
const handleEditSuccess = async (projectData: Partial<Project>) => {
  try {
    await projectStore.updateProject({
      id: editingProject.value.id!,
      ...projectData
    } as any);
    updateStats();
    // 关闭模态框
    showEditModal.value = false;
  } catch (error) {
    console.error('更新项目失败:', error);
    // 如果更新失败，重新打开模态框让用户修改
    showEditModal.value = true;
  }
};

// 组件挂载时初始化数据
onMounted(() => {
  initData();
});
</script>

<template>
  <page-layout title="项目概览">
    <!-- 统计卡片 -->
    <n-grid :x-gap="16" :y-gap="16" :cols="4" class="stats-grid">
      <n-gi v-for="(stat, index) in stats" :key="index">
        <n-card>
          <n-statistic :label="stat.title" :value="stat.value">
            <template #suffix>
              <n-icon
                :color="stat.trend === 'up' ? '#19be6b' : '#ed4014'"
                size="16"
                style="margin-left: 4px"
              >
                <IconAntDesignArrowUpOutlined v-if="stat.trend === 'up'" />
                <IconAntDesignArrowDownOutlined v-else />
              </n-icon>
              <span
                :style="{ color: stat.trend === 'up' ? '#19be6b' : '#ed4014' }"
              >
                {{ stat.trendValue }}%
              </span>
            </template>
          </n-statistic>
          <n-progress
            :percentage="typeof stat.value === 'number' ? stat.value : 0"
            :show-indicator="false"
            :height="4"
            :color="stat.color"
            :rail-style="{ marginTop: '12px' }"
          />
        </n-card>
      </n-gi>
    </n-grid>

    <!-- 项目列表 -->
    <n-card class="project-list-card">
      <n-tabs v-model:value="activeTab" type="line" animated>
        <n-tab-pane name="follow" tab="已关注">
          <ProjectGrid
            :projects="starredProjects"
            @click="goToProjectDetail"
            @star="toggleStar"
            @edit="handleEditProject"
            @delete="handleDeleteProject"
            @create="handleCreateProject"
          />
        </n-tab-pane>
        <n-tab-pane name="in-progress" tab="进行中">
          <ProjectGrid
            v-if="inProgressProjects.length > 0"
            :projects="inProgressProjects"
            :show-create-card="false"
            @click="goToProjectDetail"
            @star="toggleStar"
            @edit="handleEditProject"
            @delete="handleDeleteProject"
          />
          <div v-else class="empty-tab">
            <n-text depth="3">暂无进行中的项目</n-text>
          </div>
        </n-tab-pane>
        <n-tab-pane name="completed" tab="已完成">
          <ProjectGrid
            v-if="completedProjects.length > 0"
            :projects="completedProjects"
            :show-create-card="false"
            @click="goToProjectDetail"
            @star="toggleStar"
            @edit="handleEditProject"
            @delete="handleDeleteProject"
          />
          <div v-else class="empty-tab">
            <n-text depth="3">暂无已完成的项目</n-text>
          </div>
        </n-tab-pane>
        <n-tab-pane name="archived" tab="已归档">
          <ProjectGrid
            v-if="archivedProjects.length > 0"
            :projects="archivedProjects"
            :show-create-card="false"
            @click="goToProjectDetail"
            @star="toggleStar"
            @edit="handleEditProject"
            @delete="handleDeleteProject"
          />
          <div v-else class="empty-tab">
            <n-text depth="3">暂无已归档的项目</n-text>
          </div>
        </n-tab-pane>
        <n-tab-pane name="all" tab="全部项目">
          <ProjectGrid
            :projects="projectList"
            @click="goToProjectDetail"
            @star="toggleStar"
            @edit="handleEditProject"
            @delete="handleDeleteProject"
            @create="handleCreateProject"
          />
        </n-tab-pane>
      </n-tabs>
    </n-card>

    <!-- 项目表单模态框 -->
    <ProjectFormModal
      v-model:show="showCreateModal"
      mode="create"
      @success="handleCreateSuccess"
    />

    <ProjectFormModal
      v-model:show="showEditModal"
      mode="edit"
      :project-data="editingProject"
      @success="handleEditSuccess"
    />
  </page-layout>
</template>



<style lang="less" scoped>
@import "@/assets/styles/variables.less";

.stats-grid {
  margin-bottom: 24px;
  width: 100%;

  :deep(.n-gi) {
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-4px);
    }
  }

  :deep(.n-card) {
    background: white;
    border: 1px solid @border-color;
    border-radius: @border-radius;
    transition: all var(--transition-duration);
    height: 100%;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border-color: @primary-color;
    }

    .n-card__content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: left;
    }

    .n-statistic {
      .n-statistic-value {
        font-size: 24px;
        font-weight: 600;
        text-align: left;
      }

      .n-statistic-label {
        font-size: 13px;
        color: #666;
        text-align: left;
      }
    }
  }
}

.project-list-card {
  background: white;
  border: 1px solid @border-color;
  border-radius: @border-radius;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;

  :global(.n-card__content) {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0;
    min-height: 0;
  }

  :global(.n-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :global(.n-tabs-nav) {
    padding: 0 24px;
    margin: 0;
    background: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    flex-shrink: 0;
  }

  :global(.n-tabs-nav .n-tabs-tab) {
    padding: 16px 0;
    margin: 0 16px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.2s;
  }

  :global(.n-tabs-nav .n-tabs-tab.n-tabs-tab--active) {
    color: var(--primary-color);
    font-weight: 600;
  }

  :global(.n-tabs-nav .n-tabs-tab:hover) {
    color: var(--primary-color);
  }

  :global(.n-tabs-ink-bar) {
    height: 3px;
    border-radius: 2px 2px 0 0;
  }

  :global(.n-tabs-content) {
    flex: 1;
    min-height: 0;
  }

  :global(.n-tab-pane) {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
  }
}




.empty-tab {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  gap: 16px;

  .n-icon {
    font-size: 48px;
    opacity: 0.6;
    margin-bottom: 8px;
  }

  .n-text {
    font-size: 14px;
  }

  .n-button {
    margin-top: 16px;
  }
}
</style>
