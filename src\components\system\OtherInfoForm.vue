<template>
  <n-form :model="localValue" :rules="rules" :disabled="!isEditing">
    <div class="form-grid">
      <n-form-item label="ASA" path="asa">
        <n-input v-model:value="localValue.asa" placeholder="请输入ASA" />
      </n-form-item>
      <n-form-item label="Business impact" path="businessImpact">
        <n-input v-model:value="localValue.businessImpact" placeholder="请输入Business impact" />
      </n-form-item>
      <n-form-item label="Critical" path="critical">
        <n-select v-model:value="localValue.critical" :options="[{ label: '是', value: 'yes' }, { label: '否', value: 'no' }]"
          placeholder="请选择Critical" />
      </n-form-item>
      <n-form-item label="CSL" path="csl">
        <n-input v-model:value="localValue.csl" placeholder="请输入CSL" />
      </n-form-item>
      <n-form-item label="Group" path="group">
        <n-input v-model:value="localValue.group" placeholder="请输入Group" />
      </n-form-item>
      <n-form-item label="External Network" path="externalNetwork">
        <n-input v-model:value="localValue.externalNetwork" placeholder="请输入External Network" />
      </n-form-item>
      <n-form-item label="Host Location" path="hostLocation">
        <n-input v-model:value="localValue.hostLocation" placeholder="请输入Host Location" />
      </n-form-item>
      <n-form-item label="IC" path="ic">
        <n-input v-model:value="localValue.ic" placeholder="请输入IC" />
      </n-form-item>
      <n-form-item label="Integrity" path="integrity">
        <n-input v-model:value="localValue.integrity" placeholder="请输入Integrity" />
      </n-form-item>
      <n-form-item label="Max Downtime" path="maxDowntime">
        <n-input v-model:value="localValue.maxDowntime" placeholder="请输入Max Downtime" />
      </n-form-item>
      <n-form-item label="RPO" path="rpo">
        <n-input v-model:value="localValue.rpo" placeholder="请输入RPO" />
      </n-form-item>
      <n-form-item label="RTO" path="rto">
        <n-input v-model:value="localValue.rto" placeholder="请输入RTO" />
      </n-form-item>
      <n-form-item label="SAP Connection" path="sapConnection">
        <n-input v-model:value="localValue.sapConnection" placeholder="请输入SAP Connection" />
      </n-form-item>
    </div>
  </n-form>
</template>

<script setup lang="ts">
// 类型导入（放在第三方库导入后）
import type { FormRules } from 'naive-ui'

// 1. 类型定义
interface Props {
  /** 表单数据，v-model 绑定 */
  modelValue: Record<string, any>
  /** 是否可编辑 */
  isEditing?: boolean
}

// 2. Props 和 Emits 定义
const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

// 3. 响应式数据（v-model 双向绑定）
const localValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 4. 服务器信息验证规则
const rules: FormRules = {
}
</script>

<style scoped>
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}
</style>