package com.vwatj.ppms.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 意图识别配置
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@Component
public class IntentRecognitionConfig {

    /**
     * 是否启用LLM意图识别
     */
    private boolean enableLlmRecognition = true;

    /**
     * LLM意图识别的最低置信度阈值
     */
    private double confidenceThreshold = 0.7;

    /**
     * 是否启用降级到硬编码规则
     */
    private boolean enableFallbackRules = true;

    /**
     * 意图识别超时时间（毫秒）
     */
    private long timeoutMs = 5000;

    /**
     * 是否记录详细的意图识别日志
     */
    private boolean enableDetailedLogging = true;

    /**
     * 支持的功能列表
     */
    private String[] supportedFunctions = {
        "project_management",
        "task_management"
    };

    /**
     * 检查是否应该使用LLM意图识别
     */
    public boolean shouldUseLlmRecognition() {
        return enableLlmRecognition;
    }

    /**
     * 检查置信度是否达到阈值
     */
    public boolean isConfidenceAboveThreshold(double confidence) {
        return confidence >= confidenceThreshold;
    }

    /**
     * 检查是否支持指定功能
     */
    public boolean isFunctionSupported(String functionName) {
        if (supportedFunctions == null || functionName == null) {
            return false;
        }
        
        for (String supportedFunction : supportedFunctions) {
            if (supportedFunction.equals(functionName)) {
                return true;
            }
        }
        
        return false;
    }
}
