package com.vwatj.ppms.agent.intent;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.vwatj.ppms.agent.config.IntentRecognitionConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 基于LLM的意图识别服务
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IntentRecognitionService {

    private final DeepSeekChatModel chatModel;
    private final IntentRecognitionConfig config;

    /**
     * 识别用户意图并提取参数
     */
    public IntentResult recognizeIntent(String userMessage) {
        try {
            log.info("开始LLM意图识别: {}", userMessage);

            String prompt = buildIntentRecognitionPrompt(userMessage);
            
            ChatClient chatClient = ChatClient.builder(chatModel).build();
            String response = chatClient.prompt()
                    .user(prompt)
                    .call()
                    .content();

            log.info("LLM意图识别响应: {}", response);

            return parseIntentResponse(response);

        } catch (Exception e) {
            log.error("LLM意图识别失败: {}", e.getMessage(), e);
            return createFallbackIntent();
        }
    }

    /**
     * 构建意图识别的提示词
     */
    private String buildIntentRecognitionPrompt(String userMessage) {
        return """
                你是一个项目管理系统的意图识别专家。请分析用户的输入，识别用户的意图并提取相关参数。
                
                支持的功能和意图：
                1. 项目管理 (project_management)
                   - 查询项目: query_project
                   - 获取项目统计: get_stats
                   - 分析项目状态: analyze_status
                
                2. 任务管理 (task_management)
                   - 查询任务: query_task
                   - 查询项目任务: query_project_tasks
                   - 获取我的任务: get_my_tasks
                   - 获取任务统计: get_stats
                
                参数提取规则：
                - projectId: 从"项目123"、"123号项目"、"项目ID为123"等格式提取
                - projectName: 从"电商系统"、"CRM平台"、"测试项目"等提取
                - taskId: 从"任务456"、"456号任务"等提取
                - taskName: 任务的具体名称
                - queryType: all, by_id, by_name, by_project, by_status, by_priority
                - status: todo, in_progress, completed, paused
                - priority: high, medium, low
                
                请以JSON格式返回结果，包含以下字段：
                {
                  "intent": "功能名称",
                  "action": "具体操作",
                  "confidence": 0.0-1.0,
                  "parameters": {
                    "key": "value"
                  },
                  "reasoning": "识别理由"
                }
                
                用户输入: """ + userMessage + """
                
                请分析并返回JSON结果：
                """;
    }

    /**
     * 解析LLM的意图识别响应
     */
    private IntentResult parseIntentResponse(String response) {
        try {
            // 提取JSON部分
            String jsonStr = extractJsonFromResponse(response);
            JSONObject json = JSON.parseObject(jsonStr);

            IntentResult result = new IntentResult();
            result.setIntent(json.getString("intent"));
            result.setAction(json.getString("action"));
            result.setConfidence(json.getDoubleValue("confidence"));
            result.setReasoning(json.getString("reasoning"));

            // 解析参数
            JSONObject params = json.getJSONObject("parameters");
            if (params != null) {
                Map<String, Object> parameters = new HashMap<>();
                for (String key : params.keySet()) {
                    parameters.put(key, params.get(key));
                }
                result.setParameters(parameters);
            }

            log.info("意图识别结果: intent={}, action={}, confidence={}", 
                    result.getIntent(), result.getAction(), result.getConfidence());

            return result;

        } catch (Exception e) {
            log.error("解析意图识别响应失败: {}", e.getMessage(), e);
            return createFallbackIntent();
        }
    }

    /**
     * 从响应中提取JSON字符串
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON开始和结束位置
        int start = response.indexOf("{");
        int end = response.lastIndexOf("}");
        
        if (start != -1 && end != -1 && end > start) {
            return response.substring(start, end + 1);
        }
        
        // 如果没有找到完整的JSON，尝试其他方式
        if (response.contains("```json")) {
            start = response.indexOf("```json") + 7;
            end = response.indexOf("```", start);
            if (end > start) {
                return response.substring(start, end).trim();
            }
        }
        
        throw new RuntimeException("无法从响应中提取JSON: " + response);
    }

    /**
     * 创建降级意图（当LLM识别失败时使用）
     */
    private IntentResult createFallbackIntent() {
        IntentResult result = new IntentResult();
        result.setIntent("unknown");
        result.setAction("fallback");
        result.setConfidence(0.0);
        result.setReasoning("LLM意图识别失败，使用降级处理");
        result.setParameters(new HashMap<>());
        return result;
    }

    /**
     * 验证意图识别结果的置信度
     */
    public boolean isHighConfidence(IntentResult result) {
        return result.getConfidence() >= config.getConfidenceThreshold();
    }

    /**
     * 验证意图识别结果是否有效
     */
    public boolean isValidIntent(IntentResult result) {
        return result.getIntent() != null && 
               !result.getIntent().equals("unknown") && 
               result.getAction() != null &&
               !result.getAction().equals("fallback");
    }
}
