package com.vwatj.ppms.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 聊天响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class ChatResponseDTO {

    /**
     * AI回复消息
     */
    private String message;

    /**
     * Agent ID
     */
    private Long agentId;

    /**
     * Agent名称
     */
    private String agentName;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 是否调用了业务功能
     */
    private Boolean functionCalled = false;

    /**
     * 调用的功能列表
     */
    private List<FunctionCallDTO> functionCalls;

    /**
     * 建议的后续操作
     */
    private List<SuggestedActionDTO> suggestedActions;

    /**
     * 额外的响应数据
     */
    private Map<String, Object> data;

    /**
     * 错误信息（如果有）
     */
    private String error;

    /**
     * 使用的Token数量
     */
    private Integer tokensUsed;

    /**
     * 响应耗时（毫秒）
     */
    private Long responseTime;

    private boolean success;

    /**
     * 功能调用信息DTO
     */
    @Data
    public static class FunctionCallDTO {
        private String functionName;
        private Map<String, Object> parameters;
        private Object result;
        private Boolean success;
        private String error;
    }

    /**
     * 建议操作DTO
     */
    @Data
    public static class SuggestedActionDTO {
        private String action;
        private String description;
        private Map<String, Object> parameters;
    }
}
