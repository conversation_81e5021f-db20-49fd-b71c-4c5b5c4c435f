import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'
import LoginView from '@/views/login.vue'
import NotFound from '@/views/404.vue'

// 项目相关页面
import ProjectOverview from '@/views/project/project_overview.vue'
import ProjectDetail from '@/views/project/project_detail.vue'

// 其他页面
import SystemSettings from '@/views/setting/system_settings.vue'
import AgentList from '@/views/agent/list.vue'
import AgentChat from '@/views/agent/chat.vue'
import TaskList from '@/views/task/list.vue'

// 路由元信息类型定义
declare module 'vue-router' {
  interface RouteMeta {
    title: string
    requiresAuth?: boolean
    keepAlive?: boolean
    // 菜单相关配置
    showInMenu?: boolean // 是否在菜单中显示
    menuIcon?: string // 菜单图标
    menuOrder?: number // 菜单排序
    isMainMenu?: boolean // 是否为主菜单
    isSubMenu?: boolean // 是否为子菜单
    parentMenu?: string // 父菜单key
  }
}

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: LoginView,
    meta: {
      title: '登录',
      requiresAuth: false
    },
    beforeEnter: (_to, _from, next) => {
      // 如果已经登录，重定向到首页
      const isAuthenticated = localStorage.getItem('token')
      if (isAuthenticated) {
        next('/')
      } else {
        next()
      }
    }
  },
  {
    path: '/',
    component: MainLayout,
    redirect: '/project/overview',
    meta: { title: '主页', requiresAuth: true },
    children: [
      // 项目相关路由
      {
        path: 'project',
        redirect: '/project/overview',
        meta: {
          title: '项目管理',
          showInMenu: true,
          isMainMenu: true,
          menuIcon: 'ProjectOutlined',
          menuOrder: 1
        },
        children: [
          {
            path: 'overview',
            name: 'ProjectOverview',
            component: ProjectOverview,
            meta: {
              title: '项目概览',
              keepAlive: true,
              showInMenu: true,
              isSubMenu: true,
              parentMenu: 'project'
            }
          },

          {
            path: 'detail/:id',
            name: 'ProjectDetail',
            component: ProjectDetail,
            meta: {
              title: '项目详情',
              keepAlive: false
            },
            props: true,
            redirect: { name: 'ProjectDetailOverview' },
            children: [
              {
                path: 'overview',
                name: 'ProjectDetailOverview',
                component: () => import('@/views/project/detail/overview.vue'),
                meta: {
                  title: '项目概览',
                  keepAlive: true,
                },
                props: true
              },
              {
                path: 'task',
                name: 'ProjectDetailTask',
                component: () => import('@/views/project/detail/task.vue'),
                meta: {
                  title: '项目任务',
                  keepAlive: true,
                },
                props: true
              },
              {
                path: 'process',
                name: 'ProjectDetailProcess',
                component: () => import('@/views/project/detail/process.vue'),
                meta: {
                  title: '项目流程',
                  keepAlive: true,
                },
                props: true
              },
              {
                path: 'document',
                name: 'ProjectDetailDocument',
                component: () => import('@/views/project/detail/document.vue'),
                meta: {
                  title: '项目文档',
                  keepAlive: true,
                },
                props: true
              },
              {
                path: '',
                name: 'ProjectDetailDefault',
                redirect: { name: 'ProjectDetailOverview' }
              }
            ]
          }
        ]
      },
      // 任务管理
      {
        path: 'task',
        name: 'Task',
        meta: {
          title: '任务管理',
          showInMenu: true,
          isMainMenu: true,
          menuIcon: 'OrderedListOutlined',
          menuOrder: 2
        },
        redirect: '/task/list',
        children: [
          {
            path: 'list',
            name: 'TaskList',
            component: TaskList,
            meta: {
              title: '任务列表',
              keepAlive: true,
              showInMenu: true,
              isSubMenu: true,
              parentMenu: 'task'
            }
          }
        ]
      },
      // Agent相关路由
      {
        path: 'agent',
        name: 'Agent',
        redirect: '/agent/list',
        meta: {
          title: 'AI助手',
          showInMenu: true,
          isMainMenu: true,
          menuIcon: 'RobotOutlined',
          menuOrder: 3
        },
        children: [
          {
            path: 'chat/:id',
            name: 'AgentChat',
            component: AgentChat,
            meta: {
              title: 'AI对话',
              showInMenu: false // 聊天页面不显示在菜单中
            },
            props: true
          },
          {
            path: 'list',
            name: 'AgentList',
            component: AgentList,
            meta: {
              title: 'AI助手列表',
              keepAlive: true,
              showInMenu: true,
              isSubMenu: true,
              parentMenu: 'agent'
            }
          }
        ]
      },
      // 系统相关路由
      {
        path: 'system',
        name: 'System',
        redirect: '/system/management',
        meta: {
          title: '系统',
          showInMenu: true,
          isMainMenu: true,
          menuIcon: 'DatabaseOutlined',
          menuOrder: 4
        },
        children: [
          {
            path: 'management',
            name: 'SystemManagement',
            component: () => import('@/views/system/management.vue'),
            meta: {
              title: '系统管理',
              keepAlive: true,
              showInMenu: true,
              isSubMenu: true,
              parentMenu: 'system'
            }
          }
        ]
      },
      // 设置相关路由
      {
        path: 'settings',
        name: 'Settings',
        redirect: '/settings/personal',
        meta: {
          title: '设置',
          showInMenu: true,
          isMainMenu: true,
          menuIcon: 'SettingOutlined',
          menuOrder: 5
        },
        children: [
          {
            path: 'personal',
            name: 'PersonalSettings',
            component: SystemSettings,
            meta: {
              title: '个人设置',
              showInMenu: true,
              isSubMenu: true,
              parentMenu: 'settings'
            }
          },
          {
            path: 'dict',
            name: 'DictSettings',
            component: () => import('@/views/setting/dict_settings.vue'),
            meta: {
              title: '字典设置',
              showInMenu: true,
              isSubMenu: true,
              parentMenu: 'settings'
            }
          },
          {
            path: 'process',
            name: 'ProcessSettings',
            component: () => import('@/views/setting/process_settings.vue'),
            meta: {
              title: '流程管理',
              showInMenu: true,
              isSubMenu: true,
              parentMenu: 'settings'
            }
          },
          {
            path: 'user',
            name: 'UserSettings',
            component: () => import('@/views/setting/user_settings.vue'),
            meta: {
              title: '用户管理',
              showInMenu: true,
              isSubMenu: true,
              parentMenu: 'settings'
            }
          }
        ]
      },
      // 重定向根路径
      {
        path: '',
        redirect: '/project/overview'
      }
    ]
  },
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '404 页面不存在',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, _from, savedPosition) {
    // 如果路由有 hash，滚动到对应元素
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    // 如果是从浏览器前进/后退按钮触发的导航，则恢复到之前的位置
    if (savedPosition) {
      return savedPosition
    }
    // 否则滚动到顶部
    return { top: 0, behavior: 'smooth' }
  }
})

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  try {
    // 设置页面标题
    document.title = to.meta.title ? `${to.meta.title} - PPMS` : 'PPMS'

    // 检查是否需要认证
    if (to.meta.requiresAuth) {
      const token = localStorage.getItem('token')
      if (!token) {
        console.warn('需要登录才能访问此页面')
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }

    // 如果已登录但访问登录页，重定向到首页
    const token = localStorage.getItem('token')
    if (to.name === 'Login' && token) {
      next('/')
      return
    }

    // 继续导航
    next()
  } catch (error: any) {
    console.error('路由守卫错误:', error)
    next(false)
  }
})

export default router
