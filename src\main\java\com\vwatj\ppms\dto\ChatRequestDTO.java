package com.vwatj.ppms.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 聊天请求DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class ChatRequestDTO {

    /**
     * Agent ID
     */
    @NotNull(message = "Agent ID不能为空")
    private Long agentId;

    /**
     * 用户消息
     */
    @NotBlank(message = "用户消息不能为空")
    private String message;

    /**
     * 会话ID（用于维持上下文）
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 项目ID（可选，用于项目相关的对话）
     */
    private Long projectId;

    /**
     * 任务ID（可选，用于任务相关的对话）
     */
    private Long taskId;

    /**
     * 聊天历史（可选）
     */
    private List<ChatMessageDTO> history;

    /**
     * 额外的上下文信息
     */
    private Map<String, Object> context;

    /**
     * 是否需要调用业务功能
     */
    private Boolean enableFunctionCalling = true;

    /**
     * 是否流式响应
     */
    private Boolean stream = false;

    /**
     * 聊天消息DTO
     */
    @Data
    public static class ChatMessageDTO {
        private String role; // user, assistant, system
        private String content;
        private Long timestamp;
    }
}
