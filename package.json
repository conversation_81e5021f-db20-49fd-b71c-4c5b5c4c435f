{"name": "ppms-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:atj-test": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@vicons/antd": "^0.13.0", "@vicons/fa": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^13.5.0", "axios": "^1.10.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "naive-ui": "^2.42.0", "pinia": "^3.0.1", "sse.js": "^2.6.0", "vfonts": "^0.0.3", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@iconify-json/ant-design": "^1.2.5", "@iconify/json": "^2.2.351", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@unocss/preset-attributify": "^66.2.3", "@unocss/preset-icons": "^66.2.3", "@unocss/preset-uno": "^66.2.3", "@unocss/reset": "^66.2.3", "@unocss/transformer-directives": "^66.2.3", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "less": "^4.3.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "unocss": "^66.2.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}