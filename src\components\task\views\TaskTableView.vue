<script setup lang="ts">
import { computed, h } from 'vue'
import type { Task } from '@/types/task'
import {
  TASK_STATUS_OPTIONS,
  getIssueTypeInfo,
  getStatusInfo,
  getPriorityInfo
} from '@/types/task'
import { formatDate, safeToTimestamp, DATE_FORMATS } from '@/utils/dateTime'

interface Props {
  tasks: Task[]
  loading?: boolean
  editable?: boolean
}

interface Emits {
  (e: 'edit', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'view', task: Task): void
  (e: 'fieldUpdate', task: Task, field: keyof Task, value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  editable: false
})

const emit = defineEmits<Emits>()



// 字段更新处理
const handleFieldUpdate = (task: Task, field: keyof Task, value: any) => {
  emit('fieldUpdate', task, field, value)
}

// 表格列配置
const tableColumns = computed(() => [
  {
    title: '序号',
    key: 'id',
    width: 80,
    render: (row: Task) => row.id
  },
  {
    title: '标题',
    key: 'title',
    width: 250,
    ellipsis: true,
    render: (row: Task) => {
      if (props.editable) {
        return h('n-input', {
          value: row.title,
          onUpdateValue: (value: string) => handleFieldUpdate(row, 'title', value)
        })
      } else {
        return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
          row.taskNo ? h('span', {
            style: 'background: #e6f7ff; color: #1890ff; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; flex-shrink: 0;'
          }, row.taskNo) : null,
          h('span', row.title)
        ])
      }
    }
  },
  {
    title: '问题类型',
    key: 'issueType',
    width: 100,
    render: (row: Task) => props.editable ?
      h('n-select', {
        value: row.issueType,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'issueType', value)
      }) :
      h('n-tag', {
        type: getIssueTypeInfo(row.issueType).type,
        size: 'small'
      }, { default: () => getIssueTypeInfo(row.issueType).text })
  },
  {
    title: '指派人',
    key: 'assignee',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-input', {
        value: row.assignee,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'assignee', value)
      }) : row.assignee
  },
  {
    title: '报告人',
    key: 'reporter',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-input', {
        value: row.reporter,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'reporter', value)
      }) : row.reporter
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: true,
    render: (row: Task) => {
      if (props.editable) {
        return h('n-input', {
          value: row.description || '',
          type: 'textarea',
          onUpdateValue: (value: string) => handleFieldUpdate(row, 'description', value)
        })
      } else {
        // 如果是HTML内容，创建一个简单的文本预览
        const textContent = row.description ?
          row.description.replace(/<[^>]*>/g, '').substring(0, 50) + (row.description.length > 50 ? '...' : '') :
          '-'
        return h('span', {
          title: row.description ? row.description.replace(/<[^>]*>/g, '') : ''
        }, textContent)
      }
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-select', {
        value: row.status,
        options: TASK_STATUS_OPTIONS,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'status', value)
      }) :
      h('n-tag', {
        type: getStatusInfo(row.status).type,
        size: 'small'
      }, { default: () => getStatusInfo(row.status).text })
  },
  {
    title: '计划开始时间',
    key: 'plannedStartTime',
    width: 140,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: safeToTimestamp(row.plannedStartTime),
        type: 'date',
        onUpdateValue: (value: number | null) => handleFieldUpdate(row, 'plannedStartTime', value ? new Date(value).toISOString().split('T')[0] : null)
      }) : formatDate(row.plannedStartTime, DATE_FORMATS.DATE)
  },
  {
    title: '计划结束时间',
    key: 'plannedEndTime',
    width: 140,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: safeToTimestamp(row.plannedEndTime),
        type: 'date',
        onUpdateValue: (value: number | null) => handleFieldUpdate(row, 'plannedEndTime', value ? new Date(value).toISOString().split('T')[0] : null)
      }) : formatDate(row.plannedEndTime, DATE_FORMATS.DATE)
  },
  {
    title: '实际开始时间',
    key: 'actualStartTime',
    width: 140,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: row.actualStartTime ? new Date(row.actualStartTime).getTime() : null,
        type: 'date',
        onUpdateValue: (value: number | null) => handleFieldUpdate(row, 'actualStartTime', value ? new Date(value).toISOString().split('T')[0] : null)
      }) : formatDate(row.actualStartTime)
  },
  {
    title: '实际结束时间',
    key: 'actualEndTime',
    width: 140,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: row.actualEndTime ? new Date(row.actualEndTime).getTime() : null,
        type: 'date',
        onUpdateValue: (value: number | null) => handleFieldUpdate(row, 'actualEndTime', value ? new Date(value).toISOString().split('T')[0] : null)
      }) : formatDate(row.actualEndTime)
  },
  {
    title: '时长(小时)',
    key: 'duration',
    width: 100,
    render: (row: Task) => props.editable ?
      h('n-input-number', {
        value: row.duration || 0,
        min: 0,
        onUpdateValue: (value: number) => handleFieldUpdate(row, 'duration', value)
      }) : (row.duration || '-')
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100,
    render: (row: Task) => props.editable ?
      h('n-select', {
        value: row.priority,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'priority', value)
      }) :
      h('n-tag', {
        type: getPriorityInfo(row.priority).type,
        size: 'small'
      }, { default: () => getPriorityInfo(row.priority).text })
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right' as const,
    render: (row: Task) => h('div', { class: 'flex gap-2' }, [
      h('n-button', {
        size: 'small',
        text: true,
        type: 'primary',
        onClick: () => emit('view', row)
      }, { default: () => '编辑' }),
      h('n-button', {
        size: 'small',
        text: true,
        type: 'error',
        onClick: () => emit('delete', row)
      }, { default: () => '删除' })
    ])
  }
])

// 事件处理函数已直接在render函数中使用emit
</script>

<template>
  <div class="table-view">
    <div v-if="tasks.length === 0" class="empty-state">
      <n-empty description="暂无任务" />
    </div>
    
    <n-data-table
      v-else
      :columns="tableColumns"
      :data="tasks"
      :bordered="false"
      :single-line="false"
      :row-key="(row: Task) => row.id"
      :loading="loading"
      class="task-table"
    />
  </div>
</template>

<style lang="less" scoped>
.table-view {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-table {
  min-height: 400px;
}

:deep(.n-data-table-th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 12px 16px;
}

:deep(.n-data-table-tr:hover) {
  background: #f5f5f5;
}
</style>
