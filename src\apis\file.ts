// 文件上传相关 API 接口
import { httpClient } from './index'
import type { ApiResponse } from '@/types'

/**
 * 文件上传响应数据
 */
export interface FileUploadResponse {
  fileName: string      // 缓存区文件名
  originalName: string  // 原始文件名
  url: string          // 缓存区访问URL
  size: number         // 文件大小
  type: string         // 文件类型
}

/**
 * 文件转存响应数据
 */
export interface FileTransferResponse {
  fileName: string     // 项目区文件名
  url: string         // 项目区访问URL
  path: string        // 文件存储路径
}

/**
 * 文件上传 API 类
 */
export class FileApi {
  private static readonly BASE_PATH = '/file'

  /**
   * 上传文件到缓存区
   * @param file 文件对象
   * @param onProgress 上传进度回调
   */
  static async uploadToCache(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<FileUploadResponse>> {
    const formData = new FormData()
    formData.append('file', file)

    const config: any = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }

    if (onProgress) {
      config.onUploadProgress = (progressEvent: any) => {
        if (progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    }

    return httpClient.post<FileUploadResponse>(`${this.BASE_PATH}/upload/cache`, formData, config)
  }
}

// 导出默认实例
export const fileApi = FileApi
