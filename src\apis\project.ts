// 项目相关 API 接口
import { httpClient, buildPaginationParams } from './index'
import type {
  Project,
  ProjectStatus,
  ProjectPriority,
  ApiResponse,
  PaginationParams,
  PaginationResponse
} from '@/types'

// 项目查询参数
export interface ProjectQueryParams extends PaginationParams {
  keyword?: string
  category?: string
  status?: ProjectStatus
  priority?: ProjectPriority
  manager?: string
  starred?: boolean
  archived?: boolean
  tags?: string[]
  startDate?: number
  endDate?: number
}

// 项目创建参数
export interface CreateProjectParams {
  name: string
  description?: string
  cover?: string
  version?: string
  manager: string
  category: string
  priority: ProjectPriority
  relatedAssetId?: string
  assetVersion?: string
  startDate: number | null
  endDate: number | null
  teamMembers?: string[]
  tags: string[]
}

// 项目更新参数
export interface UpdateProjectParams extends Partial<CreateProjectParams> {
  id: number | string
}

// 项目统计信息
export interface ProjectStats {
  total: number
  byStatus: Record<ProjectStatus, number>
  byCategory: Record<string, number>
  byPriority: Record<ProjectPriority, number>
  starred: number
  archived: number
}

// 项目 API 类
export class ProjectApi {
  private static readonly BASE_PATH = '/projects'

  /**
   * 获取项目列表
   */
  static async getProjects(params?: ProjectQueryParams): Promise<ApiResponse<PaginationResponse<Project>>> {
    const queryParams = params ? {
      ...buildPaginationParams(params),
      keyword: params.keyword,
      category: params.category,
      status: params.status,
      priority: params.priority,
      manager: params.manager,
      starred: params.starred,
      archived: params.archived,
      tags: params.tags?.join(','),
      startDate: params.startDate,
      endDate: params.endDate,
    } : undefined

    return httpClient.get<PaginationResponse<Project>>(this.BASE_PATH, queryParams)
  }

  /**
   * 获取项目详情
   */
  static async getProject(id: number | string): Promise<ApiResponse<Project>> {
    return httpClient.get<Project>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建项目
   */
  static async createProject(data: CreateProjectParams): Promise<ApiResponse<Project>> {
    return httpClient.post<Project>(this.BASE_PATH, data)
  }

  /**
   * 更新项目
   */
  static async updateProject(data: UpdateProjectParams): Promise<ApiResponse<Project>> {
    const { id, ...updateData } = data
    return httpClient.put<Project>(`${this.BASE_PATH}/${id}`, updateData)
  }

  /**
   * 删除项目
   */
  static async deleteProject(id: number | string): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 切换项目关注状态
   */
  static async toggleStar(id: number | string): Promise<ApiResponse<{ starred: boolean }>> {
    return httpClient.patch<{ starred: boolean }>(`${this.BASE_PATH}/${id}/star`)
  }

  /**
   * 归档/取消归档项目
   */
  static async toggleArchive(id: number | string): Promise<ApiResponse<{ archived: boolean }>> {
    return httpClient.patch<{ archived: boolean }>(`${this.BASE_PATH}/${id}/archive`)
  }

  /**
   * 更新项目状态
   */
  static async updateStatus(id: number | string, status: ProjectStatus): Promise<ApiResponse<Project>> {
    return httpClient.patch<Project>(`${this.BASE_PATH}/${id}/status`, { status })
  }

  /**
   * 获取项目统计信息
   */
  static async getProjectStats(): Promise<ApiResponse<ProjectStats>> {
    return httpClient.get<ProjectStats>(`${this.BASE_PATH}/stats`)
  }

  /**
   * 获取项目成员列表
   */
  static async getProjectMembers(id: number | string): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>(`${this.BASE_PATH}/${id}/members`)
  }

  /**
   * 更新项目成员
   */
  static async updateProjectMembers(id: number | string, members: string[]): Promise<ApiResponse<void>> {
    return httpClient.put<void>(`${this.BASE_PATH}/${id}/members`, { members })
  }

  /**
   * 上传项目封面
   */
  static async uploadCover(id: number | string, file: File): Promise<ApiResponse<{ coverUrl: string }>> {
    return httpClient.upload<{ coverUrl: string }>(`${this.BASE_PATH}/${id}/cover`, file)
  }

  /**
   * 复制项目
   */
  static async duplicateProject(id: number | string, name: string): Promise<ApiResponse<Project>> {
    return httpClient.post<Project>(`${this.BASE_PATH}/${id}/duplicate`, { name })
  }

  /**
   * 导出项目数据
   */
  static async exportProject(id: number | string, format: 'excel' | 'pdf' = 'excel'): Promise<ApiResponse<{ downloadUrl: string }>> {
    return httpClient.get<{ downloadUrl: string }>(`${this.BASE_PATH}/${id}/export`, { format })
  }

  /**
   * 获取项目进度信息
   */
  static async getProjectProgress(id: number | string): Promise<ApiResponse<{
    totalTasks: number
    completedTasks: number
    progress: number
    milestones: Array<{
      name: string
      date: number
      completed: boolean
    }>
  }>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/progress`)
  }

  /**
   * 获取项目详情概览信息
   */
  static async getProjectOverview(id: number | string): Promise<ApiResponse<{
    projectInfo: any
    taskStats: any
    taskList: any[]
    members: any[]
    processSteps: any[]
  }>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/overview`)
  }

  /**
   * 获取项目任务统计
   */
  static async getProjectTaskStats(id: number | string): Promise<ApiResponse<{
    total: number
    completed: number
    inProgress: number
    notStarted: number
    toVerify: number
  }>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/task-stats`)
  }

  /**
   * 获取项目任务列表
   */
  static async getProjectTasks(id: number | string): Promise<ApiResponse<any[]>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/tasks`)
  }

  /**
   * 获取项目文档列表
   */
  static async getProjectDocuments(id: number | string): Promise<ApiResponse<any[]>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/documents`)
  }

  /**
   * 获取项目流程信息
   */
  static async getProjectProcess(id: number | string): Promise<ApiResponse<{
    stages: any[]
    currentStage: string
    progress: number
  }>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/process`)
  }
}

// 导出默认实例
export const projectApi = ProjectApi
