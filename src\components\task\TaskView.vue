<script setup lang="ts">
import { ref, computed, watch, h, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import TaskListView from './views/TaskListView.vue'
import TaskTableView from './views/TaskTableView.vue'
import TaskKanbanView from './views/TaskKanbanView.vue'
import TaskFormModal from './TaskFormModal.vue'
import ImportModal from '@/components/common/ImportModal.vue'
import { useTaskViewStore, type TaskViewMode } from '@/stores/taskView'
import type { Task, TaskStatus } from '@/types/task'
import type { Project } from '@/types/project'
import { TASK_STATUS_OPTIONS } from '@/types/task'
import { TaskApi } from '@/apis/task'
import { ProjectApi } from '@/apis/project'
import { useImport } from '@/composables/useImport'
import { getCurrentUser } from '@/utils/user'

// 导出Task类型供其他组件使用
export type { Task }

interface Props {
  showProjectFilter?: boolean
  projectId?: number
  projectName?: string
  title?: string
  editable?: boolean
}

interface Emits {
  (e: 'update:projectId', projectId: number | undefined): void
}

const props = withDefaults(defineProps<Props>(), {
  showProjectFilter: false,
  title: '任务列表',
  editable: false
})

const emit = defineEmits<Emits>()
const message = useMessage()
const taskViewStore = useTaskViewStore()

// 任务数据
const tasks = ref<Task[]>([])
const loading = ref(false)

// 项目数据
const projects = ref<Project[]>([])
const projectsLoading = ref(false)

// 计算当前周的范围
const getCurrentWeekRange = (): [number, number] => {
  const now = new Date()
  const dayOfWeek = now.getDay() // 0 = Sunday, 1 = Monday, ...
  const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek // 计算到周一的偏移

  // 计算周一
  const monday = new Date(now)
  monday.setDate(now.getDate() + mondayOffset)
  monday.setHours(0, 0, 0, 0)

  // 计算周日
  const sunday = new Date(monday)
  sunday.setDate(monday.getDate() + 6)
  sunday.setHours(23, 59, 59, 999)

  return [monday.getTime(), sunday.getTime()]
}

// 筛选条件
const filters = ref({
  projectId: props.projectId || 'all', // 如果没有传入项目ID，默认为'all'（所有项目）
  status: undefined as string | undefined,
  priority: undefined as string | undefined,
  assignee: undefined as string | undefined,
  keyword: '',
  taskScope: 'all' as 'all' | 'my' | 'unassigned' | 'todo', // 任务范围：默认全部任务
  dateRange: null as [number, number] | null, // 时间范围
  weekRange: getCurrentWeekRange() as [number, number] | null, // 周范围：默认选中当前周
  statusList: [] as string[] // 多选状态
})



// 项目选项
const projectOptions = computed(() => {
  if (!projects.value) return []
  return [
    { label: '全部项目', value: 'all' },
    ...projects.value.map(project => ({
      label: project.name,
      value: project.id
    }))
  ]
})

// 状态选项
const statusOptions = computed(() => [
  { label: '全部状态', value: undefined },
  ...TASK_STATUS_OPTIONS
])

// 负责人选项
const assigneeOptions = computed(() => {
  const assignees = [...new Set(tasks.value.map(task => task.assignee))]
  return [
    { label: '全部负责人', value: undefined },
    ...assignees.map(assignee => ({
      label: assignee,
      value: assignee
    }))
  ]
})

// 任务范围选项
const taskScopeOptions = [
  { label: '全部任务', value: 'all' },
  { label: '我的任务', value: 'my' },
  { label: '未分配的任务', value: 'unassigned' },
  { label: '我的待办', value: 'todo' }
]

// 状态多选选项
const statusMultiOptions = computed(() =>
  TASK_STATUS_OPTIONS.map(option => ({
    label: option.label,
    value: option.value
  }))
)







// 周选择器的值（用于显示）- 默认显示当前周
const getCurrentWeekPickerValue = (): number => {
  const now = new Date()
  const dayOfWeek = now.getDay() // 0 = Sunday, 1 = Monday, ...
  const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek // 计算到周一的偏移

  // 计算周一
  const monday = new Date(now)
  monday.setDate(now.getDate() + mondayOffset)
  monday.setHours(0, 0, 0, 0)

  return monday.getTime()
}

const weekPickerValue = ref<number | null>(getCurrentWeekPickerValue())

// 处理周选择器变化
const handleWeekChange = (value: number | null) => {
  console.log('周选择器原始值:', value)

  if (value) {
    // Naive UI的周选择器返回的是该周第一天的时间戳
    const selectedDate = new Date(value)
    console.log('选择的日期:', selectedDate.toISOString())

    // 直接使用选择的日期作为周的开始，计算周的结束
    const weekStart = new Date(selectedDate)
    weekStart.setHours(0, 0, 0, 0)

    const weekEnd = new Date(weekStart)
    weekEnd.setDate(weekStart.getDate() + 6)
    weekEnd.setHours(23, 59, 59, 999)

    filters.value.weekRange = [weekStart.getTime(), weekEnd.getTime()]
  } else {
    filters.value.weekRange = null
    console.log('清除周范围筛选')
  }
}

// 过滤后的任务列表
const filteredTasks = computed(() => {
  const currentUser = getCurrentUser()

  return tasks.value.filter(task => {
    // 确保task对象存在
    if (!task) return false

    // 项目筛选
    if (filters.value.projectId && filters.value.projectId !== 'all' && task.projectId !== filters.value.projectId) return false

    // 任务范围筛选
    if (filters.value.taskScope !== 'all') {
      switch (filters.value.taskScope) {
        case 'my':
          if (task.assignee !== currentUser.name) return false
          break
        case 'unassigned':
          if (task.assignee) return false
          break
        case 'todo':
          if (task.assignee !== currentUser.name || task.status === 'close') return false
          break
      }
    }

    // 状态筛选（单选）
    if (filters.value.status && task.status !== filters.value.status) return false

    // 状态筛选（多选）
    if (filters.value.statusList.length > 0 && !filters.value.statusList.includes(task.status)) return false

    // 优先级筛选
    if (filters.value.priority && task.priority !== filters.value.priority) return false

    // 负责人筛选
    if (filters.value.assignee && task.assignee !== filters.value.assignee) return false

    // 时间范围筛选
    if (filters.value.dateRange) {
      const taskDate = task.plannedStartTime ? new Date(task.plannedStartTime).getTime() :
        task.actualStartTime ? new Date(task.actualStartTime).getTime() : null
      if (taskDate) {
        if (taskDate < filters.value.dateRange[0] || taskDate > filters.value.dateRange[1]) return false
      }
    }

    // 周范围筛选 - 由后端处理，前端不做筛选
    // 如果有周范围筛选，所有任务都通过前端筛选，具体逻辑由后端处理

    // 关键词搜索
    if (filters.value.keyword) {
      const keyword = filters.value.keyword.toLowerCase()
      return task.title?.toLowerCase().includes(keyword) ||
        task.description?.toLowerCase().includes(keyword) ||
        task.assignee?.toLowerCase().includes(keyword)
    }

    return true
  })
})

// 监听项目ID变化
watch(() => filters.value.projectId, (newProjectId) => {
  // 将'all'转换为undefined传递给父组件
  const actualProjectId = newProjectId === 'all' ? undefined : newProjectId as number
  emit('update:projectId', actualProjectId)
})

// 监听props.projectId变化
watch(() => props.projectId, (newProjectId) => {
  if (!props.showProjectFilter) {
    // 将undefined转换为'all'
    filters.value.projectId = newProjectId || 'all'
  }
}, { immediate: true })

// 重置筛选条件
const resetFilters = () => {
  const currentWeekRange = getCurrentWeekRange()
  filters.value = {
    projectId: 'all', // 默认选择所有项目
    status: undefined,
    priority: undefined,
    assignee: undefined,
    keyword: '',
    taskScope: 'all', // 默认选择全部任务
    dateRange: null,
    weekRange: currentWeekRange, // 默认选择当前周
    statusList: []
  }
  weekPickerValue.value = getCurrentWeekPickerValue() // 重置周选择器为当前周
  pagination.value.page = 1
}

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: computed(() => filteredTasks.value.length),
  onChange: (page: number) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  }
})

// 当前页的任务
const currentPageTasks = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredTasks.value.slice(start, end)
})

// 视图模式 - 使用Pinia store管理
const currentViewMode = computed({
  get: () => taskViewStore.viewMode,
  set: (value: TaskViewMode) => taskViewStore.setViewMode(value)
})

// 视图模式选项
const viewModeOptions = [
  { label: '列表视图', value: 'list' as TaskViewMode },
  { label: '表格视图', value: 'table' as TaskViewMode },
  { label: '看板视图', value: 'kanban' as TaskViewMode }
]

// 是否显示二级筛选
const showSecondaryFilters = ref(false)

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showImportModal = ref(false)
const editingTask = ref<Partial<Task>>({})

// 字段更新处理
const handleFieldUpdate = async (task: Task, field: keyof Task, value: any) => {
  if (props.editable) {
    try {
      const updatedTask = { ...task, [field]: value }
      // 构建符合 UpdateTaskParams 接口的数据
      const updateData = {
        id: updatedTask.id,
        taskNo: updatedTask.taskNo,
        title: updatedTask.title,
        description: updatedTask.description,
        issueType: updatedTask.issueType,
        assignee: updatedTask.assignee,
        reporter: updatedTask.reporter,
        status: updatedTask.status,
        priority: updatedTask.priority,
        projectId: updatedTask.projectId,
        // 时间字段需要转换为时间戳
        plannedStartTime: updatedTask.plannedStartTime ? new Date(updatedTask.plannedStartTime).getTime() : undefined,
        plannedEndTime: updatedTask.plannedEndTime ? new Date(updatedTask.plannedEndTime).getTime() : undefined,
        actualStartTime: updatedTask.actualStartTime ? new Date(updatedTask.actualStartTime).getTime() : undefined,
        actualEndTime: updatedTask.actualEndTime ? new Date(updatedTask.actualEndTime).getTime() : undefined,
        duration: updatedTask.duration
      }

      await TaskApi.updateTask(updateData)
      message.success('更新成功')
      await fetchTasks() // 刷新任务列表
    } catch (error: any) {
      console.error('更新失败:', error)
      message.error(error.message || '更新失败')
    }
  }
}

// 获取任务数据
const fetchTasks = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params = {
      page: 1,
      pageSize: 1000, // 获取大量任务
      projectId: filters.value.projectId === 'all' ? undefined : filters.value.projectId,
      keyword: filters.value.keyword,
      status: filters.value.status as TaskStatus | undefined,
      priority: filters.value.priority,
      assignee: filters.value.assignee,
      // 新增筛选参数
      taskScope: filters.value.taskScope,
      currentUser: getCurrentUser().name, // 获取当前用户
      statusList: filters.value.statusList.length > 0 ? filters.value.statusList as TaskStatus[] : undefined,
      dateRangeStart: filters.value.dateRange ? filters.value.dateRange[0] : undefined,
      dateRangeEnd: filters.value.dateRange ? filters.value.dateRange[1] : undefined,
      weekRangeStart: filters.value.weekRange ? filters.value.weekRange[0] : undefined,
      weekRangeEnd: filters.value.weekRange ? filters.value.weekRange[1] : undefined,
    }

    // 使用新的搜索接口
    const response = await TaskApi.searchTasks(params)

    if (response.data) {
      let taskList: Task[] = []

      // 处理响应格式
      if (response.data.data && Array.isArray(response.data.data)) {
        // 分页格式：PaginationResponse<Task>
        taskList = response.data.data
      } else if (Array.isArray(response.data)) {
        // 数组格式
        taskList = response.data
      } else {
        console.warn('未知的任务响应格式:', response.data)
        taskList = []
      }

      tasks.value = taskList
      console.log(`获取任务成功: ${taskList.length} 条任务`)
    }
  } catch (error: any) {
    console.error('获取任务列表失败:', error)
    message.error(error.message || '获取任务列表失败')
    tasks.value = []
  } finally {
    loading.value = false
  }
}

// 获取项目数据
const fetchProjects = async () => {
  try {
    projectsLoading.value = true

    const response = await ProjectApi.getProjects({
      page: 1,
      pageSize: 1000 // 获取大量项目
    })

    if (response.data) {
      let projectList: Project[] = []

      // 处理不同的响应格式
      if (response.data.data && Array.isArray(response.data.data)) {
        // 分页格式：PaginationResponse<Project>
        projectList = response.data.data
      } else if (Array.isArray(response.data)) {
        // 数组格式
        projectList = response.data
      } else {
        console.warn('未知的项目响应格式:', response.data)
        projectList = []
      }

      projects.value = projectList
    }
  } catch (error: any) {
    console.error('获取项目列表失败:', error)
    message.error(error.message || '获取项目列表失败')
  } finally {
    projectsLoading.value = false
  }
}

// 操作执行处理
const handleOperationExecute = async (task: Task, operation: string) => {
  try {
    await TaskApi.executeTaskOperation({
      id: task.id,
      operation
    })
    message.success('操作成功')
    await fetchTasks() // 刷新任务列表
  } catch (error: any) {
    console.error('操作失败:', error)
    message.error(error.message || '操作失败')
  }
}

// 事件处理
const handleCreate = () => {
  editingTask.value = {} // 清空编辑数据，确保是全新创建
  showCreateModal.value = true
}

const handleEdit = (task: Task) => {
  editingTask.value = { ...task }
  showEditModal.value = true
}

const handleCopy = (task: Task) => {
  // 复制任务，继承表单值但id设为null
  const copiedTask = {
    ...task,
    id: null as any, // 设置id为null，表示新建
    title: `${task.title} - 副本` // 在标题后添加"副本"标识
  }
  editingTask.value = copiedTask
  showCreateModal.value = true // 使用创建模式
}

const handleDelete = async (task: Task) => {
  try {
    await TaskApi.deleteTask(task.id)
    message.success('删除成功')
    await fetchTasks() // 刷新任务列表
  } catch (error: any) {
    console.error('删除失败:', error)
    message.error(error.message || '删除失败')
  }
}

const handleView = (task: Task) => {
  editingTask.value = { ...task }
  showEditModal.value = true
}

// 模态框事件处理
const handleModalSuccess = async (taskData: Partial<Task>) => {
  try {
    console.log('TaskView收到的表单数据:', taskData)

    if (showCreateModal.value) {
      // 构建符合 CreateTaskParams 接口的数据
      const createData = {
        title: taskData.title!,
        description: taskData.description,
        issueType: taskData.issueType!,
        assignee: taskData.assignee!,
        reporter: taskData.reporter!,
        status: taskData.status!,
        priority: taskData.priority!,
        projectId: taskData.projectId,
        plannedStartTime: taskData.plannedStartTime ? new Date(taskData.plannedStartTime).getTime() : undefined,
        plannedEndTime: taskData.plannedEndTime ? new Date(taskData.plannedEndTime).getTime() : undefined,
        duration: taskData.duration,
        stageName: taskData.stageName
      }
      console.log('发送到API的创建数据:', createData)
      await TaskApi.createTask(createData)
      message.success('创建成功')
    } else if (showEditModal.value) {
      // 构建符合 UpdateTaskParams 接口的数据
      const updateData = {
        id: taskData.id!,
        title: taskData.title,
        description: taskData.description,
        issueType: taskData.issueType,
        assignee: taskData.assignee,
        reporter: taskData.reporter,
        status: taskData.status,
        priority: taskData.priority,
        projectId: taskData.projectId,
        plannedStartTime: taskData.plannedStartTime ? new Date(taskData.plannedStartTime).getTime() : undefined,
        plannedEndTime: taskData.plannedEndTime ? new Date(taskData.plannedEndTime).getTime() : undefined,
        actualStartTime: taskData.actualStartTime ? new Date(taskData.actualStartTime).getTime() : undefined,
        actualEndTime: taskData.actualEndTime ? new Date(taskData.actualEndTime).getTime() : undefined,
        duration: taskData.duration,
        stageName: taskData.stageName
      }
      console.log('发送到API的更新数据:', updateData)
      await TaskApi.updateTask(updateData)
      message.success('更新成功')
    }
    await fetchTasks() // 刷新任务列表
  } catch (error: any) {
    console.error('操作失败:', error)
    message.error(error.message || '操作失败')
  }
}

// 导入导出处理
const handleImport = () => {
  showImportModal.value = true
}

const handleExport = async () => {
  try {
    // 构建查询参数，确保类型正确
    const queryParams = {
      keyword: filters.value.keyword,
      status: filters.value.status as TaskStatus | undefined,
      priority: filters.value.priority as string | undefined,
      assignee: filters.value.assignee,
      projectId: props.projectId,
      page: 1,
      pageSize: 1000 // 导出时使用大的页面大小
    }

    await TaskApi.exportTasks(queryParams, false)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请稍后重试')
  }
}

const handleDownloadTemplate = async () => {
  try {
    await TaskApi.exportTasks(undefined, true)
    message.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    message.error('下载模板失败，请稍后重试')
    throw error
  }
}

const handleImportData = async (data: { file: File, mode: 'incremental' | 'overwrite', projectId?: number, projectName?: string }) => {
  return new Promise((resolve) => {
    const { startSSEImport, handleSSEProgress, handleSSEComplete, handleSSEError } = useImport()

    startSSEImport()

    TaskApi.importTasks(
      data.file,
      data.mode,
      data.projectId,
      data.projectName,
      (progressData: any) => {
        console.log('任务导入进度:', progressData)
        handleSSEProgress(progressData)
      },
      (result: any) => {
        console.log('任务导入完成回调被调用:', result)
        handleSSEComplete(result)
        resolve(result)
      },
      (error: any) => {
        console.log('任务导入错误回调被调用:', error)
        handleSSEError(error)
        // 返回一个错误结果而不是抛出异常
        resolve({
          total: 0,
          success: 0,
          failed: 1,
          skipped: 0,
          errors: [{
            row: 0,
            field: '系统错误',
            message: error.message || '导入失败，请稍后重试'
          }]
        })
      }
    ).catch((error: any) => {
      console.error('导入失败:', error)
      // 返回一个错误结果而不是抛出异常
      resolve({
        total: 0,
        success: 0,
        failed: 1,
        skipped: 0,
        errors: [{
          row: 0,
          field: '系统错误',
          message: error instanceof Error ? error.message : '导入失败，请稍后重试'
        }]
      })
    })
  })
}

const handleImportSuccess = async () => {
  showImportModal.value = false
  await fetchTasks() // 刷新任务列表
  // 不需要显示消息，ImportModal已经处理了
}

// 分页前缀函数（类型安全）
const paginationPrefix = (info: {
  startIndex: number
  endIndex: number
  page: number
  pageSize: number
  pageCount: number
  itemCount: number | undefined
}) => `共 ${info.itemCount || 0} 条`

// 初始化数据
const initializeData = async () => {
  await Promise.all([
    fetchTasks(),
    fetchProjects()
  ])
}

// 监听筛选条件变化，重新获取数据
watch([
  () => filters.value.keyword,
  () => filters.value.status,
  () => filters.value.priority,
  () => filters.value.assignee,
  () => filters.value.projectId,
  () => filters.value.taskScope,
  () => filters.value.dateRange,
  () => filters.value.weekRange,
  () => filters.value.statusList
], () => {
  fetchTasks()
}, { deep: true })

// 组件挂载时初始化
onMounted(async () => {
  console.log('任务管理初始化:')
  console.log('- 传入的项目ID:', props.projectId)
  console.log('- 筛选的项目ID:', filters.value.projectId)
  console.log('- 项目选择状态:', filters.value.projectId === 'all' ? '所有项目' : `项目ID: ${filters.value.projectId}`)
  console.log('- 默认任务范围:', filters.value.taskScope)
  if (filters.value.weekRange) {
    console.log('- 默认周范围:', {
      start: new Date(filters.value.weekRange[0]).toISOString(),
      end: new Date(filters.value.weekRange[1]).toISOString()
    })
  }
  if (weekPickerValue.value) {
    console.log('- 周选择器显示值:', new Date(weekPickerValue.value).toISOString())
  }

  taskViewStore.loadViewMode()
  await initializeData()
})
</script>

<template>
  <div class="task-view">
    <!-- 固定顶部区域 -->
    <div class="task-view-header">
      <!-- 顶部筛选和操作区域 -->
      <div class="top-section">
        <n-card :bordered="false" size="small">
          <div class="top-content">
            <!-- 左侧：项目筛选、任务范围和周选择器 -->
            <div class="left-filters">
              <!-- 任务范围选择 -->
              <n-select v-model:value="filters.taskScope" :options="taskScopeOptions" placeholder="任务范围"
                style="width: 140px" size="small" />

              <!-- 项目筛选 -->
              <n-select v-if="showProjectFilter" v-model:value="filters.projectId" :options="projectOptions"
                placeholder="所有项目" clearable class="project-select" style="width: 160px" size="small" />

              <!-- 周选择器 -->
              <n-date-picker v-model:value="weekPickerValue" type="week" placeholder="选择周" clearable
                style="width: 140px" size="small" format="ww周" @update:value="handleWeekChange" />
            </div>

            <!-- 右侧：搜索、视图、操作按钮 -->
            <div class="right-actions">
              <!-- 搜索框 -->
              <n-input v-model:value="filters.keyword" placeholder="搜索 (Ctrl + S)" clearable class="search-input"
                style="width: 240px">
                <template #prefix>
                  <n-icon>
                    <component
                      :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'm19.6 21l-6.3-6.3q-.75.6-1.725.95Q10.6 16 9.5 16q-2.725 0-4.612-1.887Q3 12.225 3 9.5q0-2.725 1.888-4.613Q6.775 3 9.5 3t4.613 1.887Q16 6.775 16 9.5q0 1.1-.35 2.075q-.35.975-.95 1.725l6.3 6.3ZM9.5 14q1.875 0 3.188-1.312Q14 11.375 14 9.5q0-1.875-1.312-3.188Q11.375 5 9.5 5Q7.625 5 6.312 6.312Q5 7.625 5 9.5q0 1.875 1.312 3.188Q7.625 14 9.5 14Z' }))" />
                  </n-icon>
                </template>
              </n-input>

              <!-- 视图切换下拉选择器 -->
              <n-select v-model:value="currentViewMode" :options="viewModeOptions" size="small" style="width: 120px" />

              <!-- 高级筛选切换按钮 -->
              <n-button @click="showSecondaryFilters = !showSecondaryFilters" size="small"
                :type="showSecondaryFilters ? 'primary' : 'default'">
                <template #icon>
                  <n-icon>
                    <component
                      :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z' }))" />
                  </n-icon>
                </template>
                筛选
              </n-button>

              <!-- 重置按钮 -->
              <n-button @click="resetFilters" size="small">
                重置
              </n-button>

              <!-- 导入按钮 -->
              <n-button @click="handleImport" size="small">
                <template #icon>
                  <n-icon>
                    <component
                      :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z' }))" />
                  </n-icon>
                </template>
                导入
              </n-button>

              <!-- 导出按钮 -->
              <n-button @click="handleExport" size="small">
                <template #icon>
                  <n-icon>
                    <component
                      :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z' }))" />
                  </n-icon>
                </template>
                导出
              </n-button>

              <!-- 新建任务按钮 -->
              <n-button type="primary" @click="handleCreate" size="small">
                <template #icon>
                  <n-icon>
                    <component
                      :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z' }))" />
                  </n-icon>
                </template>
                新建任务
              </n-button>
            </div>
          </div>
        </n-card>
      </div>

      <!-- 二级筛选区域 -->
      <div v-if="showSecondaryFilters" class="secondary-filters">
        <n-card :bordered="false" size="small">
          <div class="secondary-content">
            <!-- 第一行筛选 -->
            <div class="filter-group">
              <n-select v-model:value="filters.status" :options="statusOptions" placeholder="单选状态" clearable
                class="filter-select" size="small" />

              <n-select v-model:value="filters.statusList" :options="statusMultiOptions" placeholder="多选状态" multiple
                clearable class="filter-select-wide" size="small" />

              <n-select v-model:value="filters.assignee" :options="assigneeOptions" placeholder="负责人" clearable
                class="filter-select" size="small" />
            </div>

            <!-- 第二行筛选 -->
            <div class="filter-group">
              <!-- 时间范围选择 -->
              <n-date-picker v-model:value="filters.dateRange" type="daterange" placeholder="选择时间范围" clearable
                class="filter-date-range" size="small" format="yyyy-MM-dd" />
            </div>
          </div>
        </n-card>
      </div>
    </div>

    <!-- 可滚动的内容区域 -->
    <div class="task-view-body">
      <n-scrollbar class="task-scrollbar">
        <div class="task-list-section">
          <n-card :bordered="false">
            <!-- 任务内容区域 -->
            <div class="task-content-area">
              <!-- 列表视图 -->
              <TaskListView v-if="currentViewMode === 'list'" :tasks="currentPageTasks" :loading="loading"
                :show-project-filter="showProjectFilter" @edit="handleEdit" @delete="handleDelete" @view="handleView" />

              <!-- 表格视图 -->
              <TaskTableView v-else-if="currentViewMode === 'table'" :tasks="currentPageTasks" :loading="loading"
                :editable="editable" @edit="handleEdit" @delete="handleDelete" @view="handleView"
                @field-update="handleFieldUpdate" />

              <!-- 看板视图 -->
              <TaskKanbanView v-else-if="currentViewMode === 'kanban'" :tasks="filteredTasks" :loading="loading"
                @edit="handleEdit" @copy="handleCopy" @delete="handleDelete" @view="handleView"
                @operation-execute="handleOperationExecute" @field-update="handleFieldUpdate" />
            </div>
          </n-card>
        </div>
      </n-scrollbar>
    </div>

    <!-- 固定底部分页区域 -->
    <div v-if="filteredTasks.length > 0 && currentViewMode !== 'kanban'" class="task-view-footer">
      <div class="pagination-wrapper">
        <n-pagination v-model:page="pagination.page" v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount" :page-sizes="pagination.pageSizes" show-size-picker show-quick-jumper
          :prefix="paginationPrefix" />
      </div>
    </div>

    <!-- 新建任务模态框 -->
    <TaskFormModal v-model:show="showCreateModal" mode="create" :project-id="projectId" :project-name="title"
      :task-data="editingTask" @success="handleModalSuccess" />

    <!-- 编辑任务模态框 -->
    <TaskFormModal v-model:show="showEditModal" mode="edit" :task-data="editingTask" @success="handleModalSuccess" />

    <!-- 导入模态框 -->
    <ImportModal v-model:show="showImportModal" title="导入任务" :show-project-selector="!projectId" :project-id="projectId"
      :project-name="projectName" :projects="projects" :on-download-template="handleDownloadTemplate"
      :on-import="handleImportData" @success="handleImportSuccess" @cancel="showImportModal = false" />
  </div>
</template>

<style lang="less" scoped>
.task-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 固定顶部区域 */
.task-view-header {
  flex-shrink: 0;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  z-index: 10;
}

.top-section {
  margin-bottom: 0;
}

.top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.left-filters {
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 二级筛选区域 */
.secondary-filters {
  border-top: 1px solid #f0f0f0;
}

.secondary-content {
  padding: 8px 0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-select {
  width: 120px;
}

.filter-select-wide {
  width: 200px;
}

.filter-date-range {
  width: 240px;
}

.filter-week {
  width: 160px;
}

.project-select {
  min-width: 200px;
}

/* 可滚动的主体区域 */
.task-view-body {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.task-scrollbar {
  height: 100%;
}

/* 任务列表区域 */
.task-list-section {
  padding: 16px;
}

.task-content-area {
  min-height: 400px;
  /* 确保有足够的最小高度 */
}

/* 固定底部分页区域 */
.task-view-footer {
  flex-shrink: 0;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 10;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
}

.task-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;

  .task-item:hover & {
    opacity: 1;
  }
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  display: flex;
  gap: 16px;
  align-items: center;
}

.assignee {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-name {
  font-size: 13px;
  color: #595959;
}

.project-name {
  font-size: 13px;
  color: #8c8c8c;
}

.task-status {
  display: flex;
  gap: 8px;
}

.task-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 12px;
  color: #595959;
  font-weight: 500;
}

.due-date {
  font-size: 12px;
  color: #8c8c8c;
}

:deep(.n-card) {
  border-radius: 8px;
}

:deep(.n-card__content) {
  padding: 16px;
}

:deep(.n-progress-rail) {
  background-color: #f5f5f5;
}



.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 16px 24px;
  background: #fff;
}
</style>
