// 文档相关类型定义

import type { ProjectStatus } from './project'

// 文档类型（根据文件后缀名）
export type DocumentFileType = 'PDF' | 'PPT' | 'WORD' | 'EXCEL' | 'TXT' | 'MD' | 'IMAGE' | 'VIDEO' | 'AUDIO' | 'ZIP' | 'OTHER'

// 文档类别（对应项目状态）
export type DocumentCategory = ProjectStatus

// 文档状态
export type DocumentStatus = 'draft' | 'review' | 'approved' | 'published' | 'archived'

// 文档接口定义
export interface Document {
  id: number
  projectId: number
  taskId?: number // 关联的任务ID
  processId?: number // 关联的流程ID
  
  // 基本信息
  title: string
  content?: string // 文档内容
  summary?: string // 文档摘要
  fileType: DocumentFileType // 文档类型（根据后缀名）
  category: DocumentCategory // 文档类别（对应项目状态）
  status: DocumentStatus
  
  // 文件信息
  fileName: string // 文件名
  fileSize: number // 文件大小（字节）
  fileExtension: string // 文件扩展名
  fileUrl: string // 文件下载链接
  thumbnailUrl?: string // 缩略图链接
  
  // 版本控制
  version: string // 版本号
  versionHistory?: Array<{
    version: string
    timestamp: number
    author: string
    changes: string
    fileUrl: string
  }>
  
  // 作者和权限
  author: number // 作者ID
  authorName: string // 作者姓名
  editors?: string[] // 编辑者ID列表
  viewers?: string[] // 查看者ID列表
  isPublic: boolean // 是否公开
  
  // 审核流程
  reviewer?: string // 审核人ID
  reviewerName?: string // 审核人姓名
  reviewComments?: string // 审核意见
  reviewDate?: number // 审核时间
  approver?: string // 批准人ID
  approverName?: string // 批准人姓名
  approvalDate?: number // 批准时间
  
  // 分类和标签
  tags: string[]
  keywords?: string[] // 关键词，用于搜索
  
  // 关联关系
  relatedDocuments?: string[] // 相关文档ID列表
  references?: string[] // 引用的外部资源
  
  // 统计信息
  viewCount: number // 查看次数
  downloadCount: number // 下载次数
  
  // 时间信息
  publishDate?: number // 发布时间
  expiryDate?: number // 过期时间
  
  // 扩展字段
  customFields?: Record<string, any>
  
  createdAt: number
  updatedAt: number
}

// 文档类型配置
export const DOCUMENT_FILE_TYPE_CONFIG: Record<DocumentFileType, { text: string; icon: string; color: string }> = {
  'PDF': { text: 'PDF文档', icon: 'i-mdi:file-pdf-box', color: '#ff4757' },
  'PPT': { text: 'PPT演示', icon: 'i-mdi:file-powerpoint-box', color: '#ff6b35' },
  'WORD': { text: 'Word文档', icon: 'i-mdi:file-word-box', color: '#2e86de' },
  'EXCEL': { text: 'Excel表格', icon: 'i-mdi:file-excel-box', color: '#10ac84' },
  'TXT': { text: '文本文件', icon: 'i-mdi:file-document-outline', color: '#747d8c' },
  'MD': { text: 'Markdown', icon: 'i-mdi:language-markdown', color: '#5f27cd' },
  'IMAGE': { text: '图片文件', icon: 'i-mdi:file-image', color: '#00d2d3' },
  'VIDEO': { text: '视频文件', icon: 'i-mdi:file-video', color: '#ff3838' },
  'AUDIO': { text: '音频文件', icon: 'i-mdi:file-music', color: '#ff9ff3' },
  'ZIP': { text: '压缩文件', icon: 'i-mdi:zip-box', color: '#ffa502' },
  'OTHER': { text: '其他文件', icon: 'i-mdi:file', color: '#57606f' }
}

// 文档状态配置
export const DOCUMENT_STATUS_CONFIG: Record<DocumentStatus, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
  'draft': { text: '草稿', type: 'default' },
  'review': { text: '审核中', type: 'warning' },
  'approved': { text: '已批准', type: 'info' },
  'published': { text: '已发布', type: 'success' },
  'archived': { text: '已归档', type: 'error' }
}

// 文档状态选项
export const DOCUMENT_STATUS_OPTIONS = [
  { label: '草稿', value: 'draft' as DocumentStatus },
  { label: '审核中', value: 'review' as DocumentStatus },
  { label: '已批准', value: 'approved' as DocumentStatus },
  { label: '已发布', value: 'published' as DocumentStatus },
  { label: '已归档', value: 'archived' as DocumentStatus }
]

// 根据文件扩展名获取文档类型
export const getDocumentFileType = (extension: string): DocumentFileType => {
  const ext = extension.toLowerCase().replace('.', '')
  
  if (['pdf'].includes(ext)) return 'PDF'
  if (['ppt', 'pptx'].includes(ext)) return 'PPT'
  if (['doc', 'docx'].includes(ext)) return 'WORD'
  if (['xls', 'xlsx'].includes(ext)) return 'EXCEL'
  if (['txt'].includes(ext)) return 'TXT'
  if (['md', 'markdown'].includes(ext)) return 'MD'
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) return 'IMAGE'
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) return 'VIDEO'
  if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(ext)) return 'AUDIO'
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) return 'ZIP'
  
  return 'OTHER'
}

// 获取文档类型显示信息
export const getDocumentFileTypeInfo = (fileType: DocumentFileType) => {
  return DOCUMENT_FILE_TYPE_CONFIG[fileType] || DOCUMENT_FILE_TYPE_CONFIG['OTHER']
}

// 获取文档状态显示信息
export const getDocumentStatusInfo = (status: DocumentStatus) => {
  return DOCUMENT_STATUS_CONFIG[status] || { text: status, type: 'default' as const }
}

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 文档统计信息
export interface DocumentStats {
  projectId: number
  
  // 文档统计
  totalDocuments: number
  draftDocuments: number
  reviewDocuments: number
  approvedDocuments: number
  publishedDocuments: number
  archivedDocuments: number
  
  // 文件类型统计
  fileTypeStats: Record<DocumentFileType, number>
  
  // 类别统计
  categoryStats: Record<DocumentCategory, number>
  
  // 存储统计
  totalFileSize: number // 总文件大小（字节）
  averageFileSize: number // 平均文件大小（字节）
  
  // 活动统计
  totalViews: number
  totalDownloads: number
  
  // 更新时间
  lastUpdated: number
}

// API 响应类型
export interface DocumentListResponse {
  data: Document[]
  total: number
  page: number
  pageSize: number
}

export interface DocumentUploadResponse {
  id: number
  fileName: string
  fileSize: number
  fileUrl: string
  thumbnailUrl?: string
}
