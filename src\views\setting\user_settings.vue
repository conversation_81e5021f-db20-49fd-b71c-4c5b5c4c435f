<template>
  <PageLayout title="用户管理">
    <template #header-extra>
      <n-button type="primary" @click="handleCreate">
        <template #icon>
          <n-icon><PlusOutlined /></n-icon>
        </template>
        新增用户
      </n-button>
    </template>

    <!-- 搜索区域 -->
    <n-card class="mb-4">
      <n-space :size="16" :wrap="false">
        <n-input
          v-model:value="queryParams.keyword"
          placeholder="用户名/姓名/邮箱"
          clearable
          style="width: 200px"
        />
        <n-select
          v-model:value="queryParams.role"
          placeholder="角色"
          :options="roleOptions"
          clearable
          style="width: 120px"
        />
        <n-input
          v-model:value="queryParams.department"
          placeholder="部门"
          clearable
          style="width: 150px"
        />
        <n-select
          v-model:value="queryParams.isActive"
          placeholder="状态"
          :options="statusOptions"
          clearable
          style="width: 100px"
        />
        <n-button type="primary" @click="handleSearch">搜索</n-button>
        <n-button @click="handleReset">重置</n-button>
      </n-space>
    </n-card>

    <!-- 数据表格 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="{
          page: queryParams.page,
          pageSize: queryParams.pageSize,
          itemCount: total,
          showSizePicker: true,
          pageSizes: [10, 20, 50],
          onUpdatePage: handlePageChange,
          onUpdatePageSize: handlePageSizeChange
        }"
        :scroll-x="950"
      />
    </n-card>

    <!-- 新增用户模态框 -->
    <n-modal
      v-model:show="showCreateModal"
      preset="dialog"
      title="新增用户"
      style="width: 600px"
      :mask-closable="false"
    >
      <n-form :model="createForm" label-placement="left" label-width="100px">
        <n-form-item label="用户名" required>
          <n-input v-model:value="createForm.username" placeholder="请输入用户名" />
        </n-form-item>
        <n-form-item label="邮箱" required>
          <n-input v-model:value="createForm.email" placeholder="请输入邮箱" />
        </n-form-item>
        <n-form-item label="密码" required>
          <n-input
            v-model:value="createForm.password"
            type="password"
            placeholder="请输入密码"
            show-password-on="click"
          />
        </n-form-item>
        <n-form-item label="姓名" required>
          <n-input v-model:value="createForm.fullName" placeholder="请输入姓名" />
        </n-form-item>
        <n-form-item label="角色" required>
          <n-select
            v-model:value="createForm.role"
            placeholder="请选择角色"
            :options="roleOptions"
          />
        </n-form-item>
        <n-form-item label="部门">
          <n-input v-model:value="createForm.department" placeholder="请输入部门" />
        </n-form-item>
        <n-form-item label="职位">
          <n-input v-model:value="createForm.position" placeholder="请输入职位" />
        </n-form-item>
        <n-form-item label="手机号">
          <n-input v-model:value="createForm.phone" placeholder="请输入手机号" />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showCreateModal = false">取消</n-button>
          <n-button type="primary" @click="handleCreateSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 编辑用户模态框 -->
    <n-modal
      v-model:show="showEditModal"
      preset="dialog"
      title="编辑用户"
      style="width: 600px"
      :mask-closable="false"
    >
      <n-form :model="editForm" label-placement="left" label-width="100px">
        <n-form-item label="用户名">
          <n-input v-model:value="editForm.username" disabled />
        </n-form-item>
        <n-form-item label="邮箱" required>
          <n-input v-model:value="editForm.email" placeholder="请输入邮箱" />
        </n-form-item>
        <n-form-item label="姓名" required>
          <n-input v-model:value="editForm.fullName" placeholder="请输入姓名" />
        </n-form-item>
        <n-form-item label="角色" required>
          <n-select
            v-model:value="editForm.role"
            placeholder="请选择角色"
            :options="roleOptions"
          />
        </n-form-item>
        <n-form-item label="部门">
          <n-input v-model:value="editForm.department" placeholder="请输入部门" />
        </n-form-item>
        <n-form-item label="职位">
          <n-input v-model:value="editForm.position" placeholder="请输入职位" />
        </n-form-item>
        <n-form-item label="手机号">
          <n-input v-model:value="editForm.phone" placeholder="请输入手机号" />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="handleEditSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 重置密码模态框 -->
    <n-modal
      v-model:show="showResetPasswordModal"
      preset="dialog"
      title="重置密码"
      style="width: 500px"
      :mask-closable="false"
    >
      <n-form :model="resetPasswordForm" label-placement="left" label-width="100px">
        <n-form-item label="用户">
          <n-text>{{ currentUser?.fullName }} ({{ currentUser?.username }})</n-text>
        </n-form-item>
        <n-form-item label="新密码" required>
          <n-input
            v-model:value="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password-on="click"
          />
        </n-form-item>
        <n-form-item label="确认密码" required>
          <n-input
            v-model:value="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password-on="click"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showResetPasswordModal = false">取消</n-button>
          <n-button type="primary" @click="handleResetPasswordSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import {
  NCard,
  NDataTable,
  NButton,
  NSpace,
  NInput,
  NSelect,
  NModal,
  NForm,
  NFormItem,
  NText,
  NIcon,
  NTag,
  NPopconfirm,
  NTooltip,
  useMessage,
  type DataTableColumns,
  type SelectOption
} from 'naive-ui'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
  CheckCircleOutlined,
  StopOutlined
} from '@vicons/antd'
import PageLayout from '@/layouts/PageLayout.vue'
import { userApi } from '@/apis/user'
import type { User, UserRole } from '@/types'
import type {
  UserQueryParams,
  CreateUserParams,
  UpdateUserParams,
  ResetPasswordParams
} from '@/apis/user'

// hooks
const message = useMessage()

// 响应式数据
const loading = ref(false)
const data = ref<User[]>([])
const total = ref(0)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showResetPasswordModal = ref(false)
const currentUser = ref<User | null>(null)

// 查询参数
const queryParams = reactive<UserQueryParams>({
  page: 1,
  pageSize: 10,
  keyword: '',
  role: undefined,
  department: '',
  isActive: undefined
})

// 表单数据
const createForm = reactive<CreateUserParams>({
  username: '',
  email: '',
  password: '',
  fullName: '',
  role: 'viewer',
  department: '',
  position: '',
  phone: ''
})

const editForm = reactive<UpdateUserParams & { username?: string }>({
  id: '',
  username: '',
  email: '',
  fullName: '',
  role: 'viewer',
  department: '',
  position: '',
  phone: ''
})

const resetPasswordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 选项数据
const roleOptions: SelectOption[] = [
  { label: '管理员', value: 'admin' },
  { label: '经理', value: 'manager' },
  { label: '开发者', value: 'developer' },
  { label: '设计师', value: 'designer' },
  { label: '测试员', value: 'tester' },
  { label: '查看者', value: 'viewer' }
]

const statusOptions: SelectOption[] = [
  { label: '全部', value: undefined },
  { label: '启用', value: true },
  { label: '禁用', value: false }
]

// 生命周期
onMounted(() => {
  loadData()
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await userApi.getUsers(queryParams)
    data.value = response.data.data
    total.value = response.data.total
  } catch (error) {
    console.error('加载用户列表失败:', error)
    message.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(queryParams, {
    page: 1,
    pageSize: 10,
    keyword: '',
    role: undefined,
    department: '',
    isActive: undefined
  })
  loadData()
}

const handleCreate = () => {
  Object.assign(createForm, {
    username: '',
    email: '',
    password: '',
    fullName: '',
    role: 'viewer',
    department: '',
    position: '',
    phone: ''
  })
  showCreateModal.value = true
}

const handleEdit = (row: User) => {
  currentUser.value = row
  Object.assign(editForm, {
    id: row.id.toString(),
    username: row.username,
    email: row.email,
    fullName: row.fullName,
    role: row.role,
    department: row.department || '',
    position: row.position || '',
    phone: row.phone || ''
  })
  showEditModal.value = true
}

const handleResetPassword = (row: User) => {
  currentUser.value = row
  Object.assign(resetPasswordForm, {
    newPassword: '',
    confirmPassword: ''
  })
  showResetPasswordModal.value = true
}

const handleToggleStatus = async (row: User) => {
  try {
    await userApi.toggleUserStatus(row.id.toString())
    message.success(`用户${row.isActive ? '禁用' : '启用'}成功`)
    loadData()
  } catch (error) {
    console.error('切换用户状态失败:', error)
    message.error('切换用户状态失败')
  }
}

const handleDelete = async (row: User) => {
  try {
    await userApi.deleteUser(row.id.toString())
    message.success('用户删除成功')
    loadData()
  } catch (error) {
    console.error('删除用户失败:', error)
    message.error('删除用户失败')
  }
}

// 表单提交
const handleCreateSubmit = async () => {
  if (!createForm.username || !createForm.email || !createForm.password || !createForm.fullName) {
    message.error('请填写完整信息')
    return
  }

  try {
    await userApi.createUser(createForm)
    message.success('用户创建成功')
    showCreateModal.value = false
    loadData()
  } catch (error) {
    console.error('创建用户失败:', error)
    message.error('创建用户失败')
  }
}

const handleEditSubmit = async () => {
  if (!editForm.email || !editForm.fullName) {
    message.error('请填写完整信息')
    return
  }

  try {
    await userApi.updateUser(editForm)
    message.success('用户更新成功')
    showEditModal.value = false
    loadData()
  } catch (error) {
    console.error('更新用户失败:', error)
    message.error('更新用户失败')
  }
}

const handleResetPasswordSubmit = async () => {
  if (!resetPasswordForm.newPassword || !resetPasswordForm.confirmPassword) {
    message.error('请填写完整密码信息')
    return
  }

  if (resetPasswordForm.newPassword !== resetPasswordForm.confirmPassword) {
    message.error('两次输入的密码不一致')
    return
  }

  if (!currentUser.value) {
    message.error('用户信息错误')
    return
  }

  try {
    await userApi.resetUserPassword({
      userId: currentUser.value.id.toString(),
      newPassword: resetPasswordForm.newPassword
    })
    message.success('密码重置成功')
    showResetPasswordModal.value = false
  } catch (error) {
    console.error('重置密码失败:', error)
    message.error('重置密码失败')
  }
}

// 分页处理
const handlePageChange = (page: number) => {
  queryParams.page = page
  loadData()
}

const handlePageSizeChange = (pageSize: number) => {
  queryParams.pageSize = pageSize
  queryParams.page = 1
  loadData()
}

// 获取角色标签类型
const getRoleTagType = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return 'error'
    case 'manager':
      return 'warning'
    case 'developer':
      return 'info'
    case 'designer':
      return 'success'
    case 'tester':
      return 'default'
    case 'viewer':
      return 'default'
    default:
      return 'default'
  }
}

// 获取角色文本
const getRoleText = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'manager':
      return '经理'
    case 'developer':
      return '开发者'
    case 'designer':
      return '设计师'
    case 'tester':
      return '测试员'
    case 'viewer':
      return '查看者'
    default:
      return '未知'
  }
}

// 表格列配置
const columns: DataTableColumns<User> = [
  {
    title: '用户名',
    key: 'username',
    width: 100,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '姓名',
    key: 'fullName',
    width: 100,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '邮箱',
    key: 'email',
    width: 160,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '角色',
    key: 'role',
    width: 80,
    align: 'center',
    render: (row) => {
      return h(NTag, {
        type: getRoleTagType(row.role)
      }, {
        default: () => getRoleText(row.role)
      })
    }
  },
  {
    title: '部门',
    key: 'department',
    width: 100,
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      return row.department || '-'
    }
  },
  {
    title: '状态',
    key: 'isActive',
    width: 70,
    align: 'center',
    render: (row) => {
      return h(NTag, {
        type: row.isActive ? 'success' : 'error'
      }, {
        default: () => row.isActive ? '启用' : '禁用'
      })
    }
  },
  {
    title: '最后登录',
    key: 'lastLoginAt',
    width: 140,
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      return row.lastLoginAt ? new Date(row.lastLoginAt).toLocaleString() : '-'
    }
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 140,
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      return new Date(row.createdAt).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 160,
    align: 'center',
    fixed: 'right',
    render: (row) => {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'info',
              ghost: true,
              onClick: () => handleEdit(row)
            }, {
              default: () => h(NIcon, null, { default: () => h(EditOutlined) })
            }),
            default: () => '编辑'
          }),
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'warning',
              ghost: true,
              onClick: () => handleResetPassword(row)
            }, {
              default: () => h(NIcon, null, { default: () => h(KeyOutlined) })
            }),
            default: () => '重置密码'
          }),
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: row.isActive ? 'error' : 'success',
              ghost: true,
              onClick: () => handleToggleStatus(row)
            }, {
              default: () => h(NIcon, null, {
                default: () => row.isActive ? h(StopOutlined) : h(CheckCircleOutlined)
              })
            }),
            default: () => row.isActive ? '禁用' : '启用'
          }),
          h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row)
          }, {
            trigger: () => h(NTooltip, { trigger: 'hover' }, {
              trigger: () => h(NButton, {
                size: 'small',
                type: 'error',
                ghost: true
              }, {
                default: () => h(NIcon, null, { default: () => h(DeleteOutlined) })
              }),
              default: () => '删除'
            }),
            default: () => '确定删除此用户吗？'
          })
        ]
      })
    }
  }
]
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>
