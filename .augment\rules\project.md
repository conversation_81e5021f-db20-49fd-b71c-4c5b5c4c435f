---
type: "manual"
description: "Example description"
---
# PPMS 项目代码规范
## 1. 项目结构规范
### 1.1 目录结构
```
src/
├── apis/           # API 接口层
├── assets/         # 静态资源
│   └── styles/     # 样式文件（统一使用 Less）
├── components/     # 公共组件
├── composables/    # 组合式函数
├── layouts/        # 布局组件
├── router/         # 路由配置
├── stores/         # Pinia 状态管理
├── types/          # TypeScript 类型定义
├── utils/          # 工具函数
└── views/          # 页面组件
```
### 1.2 文件命名规范
- **页面文件**：snake_case（如 `project_overview.vue`）
- **组件文件**：PascalCase（如 `ProjectCard.vue`）
- **工具文件**：camelCase（如 `errorHandler.ts`）
- **类型文件**：camelCase（如 `project.ts`）
- **Store 文件**：camelCase（如 `user.ts`）
- **样式文件**：统一使用 `.less` 扩展名
## 2. Vue 组件编写规范
### 2.1 Vue 文件结构顺序（强制规范）
**Vue 文件必须严格按以下顺序组织，不允许任何例外：**

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入语句（按分组排序）
// 2. 接口定义（Props, Emits）
// 3. Props 和 Emits 定义
// 4. 响应式数据
// 5. 计算属性
// 6. 方法定义
// 7. 生命周期钩子
</script>

<style scoped lang="less">
/* 样式内容 */
</style>
```

**🚨 强制要求：**
- `<template>` 必须在最前面
- `<script>` 必须在中间
- `<style>` 必须在最后面
- 任何违反此顺序的代码都必须修正
### 2.2 Props 和 Emits 定义
```typescript
interface Props {
  title: string
  status: 'pending' | 'current' | 'completed'
  data?: FormData & { submittedAt?: string }
  readonly?: boolean
}
interface Emits {
  (e: 'fieldUpdate', field: string, value: any): void
  (e: 'statusChange', status: string): void
  (e: 'createItem', item: any): void
}
const props = withDefaults(defineProps<Props>(), {
  readonly: false
})
const emit = defineEmits<Emits>()
```
### 2.3 组件标签命名规范
**自定义组件标签使用 PascalCase：**
```vue
<template>
  <!-- ✅ 正确：自定义组件使用 PascalCase -->
  <TaskListView
    :tasks="tasks"
    @field-update="handleFieldUpdate"
    @status-change="handleStatusChange"
  />
  <ErrorBoundary>
    <GlobalLoading />
  </ErrorBoundary>

  <!-- ✅ 正确：Naive UI 组件保持原有命名 -->
  <n-config-provider>
    <n-loading-bar-provider>
      <!-- ... -->
    </n-loading-bar-provider>
  </n-config-provider>

  <!-- ✅ 正确：HTML 属性使用 kebab-case -->
  <div
    :data-test-id="testId"
    :aria-label="label"
    class="custom-class"
  >
    <!-- Vue 指令保持原有格式 -->
    <input
      v-model="value"
      :disabled="isDisabled"
      @input="handleInput"
    />
  </div>
</template>
```

### 2.4 事件命名规范
```vue
<template>
  <!-- ✅ 正确：模板中事件使用 kebab-case -->
  <CustomComponent
    @field-update="handleFieldUpdate"
    @status-change="handleStatusChange"
    @create-item="handleCreateItem"
  />
</template>

<script setup lang="ts">
// ✅ 正确：定义中事件使用 camelCase
interface Emits {
  (e: 'fieldUpdate', field: string, value: any): void
  (e: 'statusChange', status: string): void
  (e: 'createItem', item: any): void
}
</script>
```
## 3. TypeScript 类型定义规范
### 3.1 类型组织
- 在 `src/types/` 目录下按功能模块组织
- 使用 `index.ts` 统一导出
- 接口命名使用 PascalCase
- 类型别名使用 PascalCase
### 3.2 类型定义示例
```typescript
// 基础接口
export interface Project {
  id: number
  name: string
  description?: string
  status: ProjectStatus
  // ...
}
// 类型别名
export type ProjectStatus = 'request collect' | 'planning' | 'development'
// API 参数类型
export interface CreateProjectParams {
  name: string
  description?: string
  // ...
}
// 常量定义使用 UPPER_SNAKE_CASE
export const PROJECT_STATUS_OPTIONS = [
  { label: '需求收集', value: 'request collect' },
  { label: '规划中', value: 'planning' }
] as const
```
## 4. API 层规范
### 4.1 API 组织结构
```typescript
export class ProjectApi {
  private static readonly BASE_PATH = '/projects'
  /**
   * 获取项目列表
   */
  static async getProjects(params?: ProjectQueryParams): Promise<ApiResponse<PaginationResponse<Project>>> {
    const queryParams = params ? {
      ...buildPaginationParams(params),
      keyword: params.keyword,
      // ...
    } : undefined
    return httpClient.get<PaginationResponse<Project>>(this.BASE_PATH, queryParams)
  }
}
```
### 4.2 错误处理
- 统一使用 `ApiError` 类
- 在 `utils/errorHandler.ts` 中统一处理错误消息
- 使用 `useApi` composable 进行 API 调用
## 5. 状态管理规范
### 5.1 Pinia Store 结构
```typescript
export const useProjectStore = defineStore('project', () => {
  // 状态
  const projects = ref<Project[]>([])
  const loading = ref(false)
  
  // 计算属性
  const projectCount = computed(() => projects.value.length)
  
  // 方法
  const fetchProjects = async () => {
    // 实现
  }
  
  return {
    // 状态
    projects,
    loading,
    // 计算属性
    projectCount,
    // 方法
    fetchProjects
  }
})
```
## 6. 样式规范
### 6.1 样式组织
- **统一使用 Less**：所有样式文件使用 `.less` 扩展名
- **UnoCSS 优先**：优先使用 UnoCSS 原子化 CSS
- **Less 变量**：定义在 `assets/styles/variables.less`
- **全局样式**：在 `assets/styles/global.less`
- **组件样式**：使用 `scoped lang="less"`
### 6.2 CSS 变量命名
```less
:root {
  --primary-color: #18a058;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --bg-color: #ffffff;
  --border-color: #e0e0e6;
}
```
### 6.3 UnoCSS 快捷类
```typescript
shortcuts: {
  'flex-center': 'flex justify-center items-center',
  'flex-between': 'flex justify-between items-center',
  'text-ellipsis': 'overflow-hidden text-ellipsis whitespace-nowrap',
}
```
## 7. 导入语句规范
### 7.1 导入语句规范（基于 Auto Import 配置）

**🚨 重要：项目已配置 Auto Import，以下内容无需手动导入：**
- **Vue API**: `ref`, `reactive`, `computed`, `watch`, `onMounted`, `onUnmounted` 等
- **Vue Router**: `useRouter`, `useRoute` 等
- **Naive UI Hooks**: `useMessage`, `useDialog`, `useNotification`, `useLoadingBar`
- **图标**: 所有 `@vicons/antd` 图标（使用 `Icon` 前缀，如 `IconUserOutlined`）

**✅ 正确的导入顺序：**
```typescript
// 1. Node.js 内置模块
import { fileURLToPath, URL } from 'node:url'

// 2. 第三方库组件（未被 auto-import 的）
import { NForm, NFormItem, NInput, NButton } from 'naive-ui'
import { defineConfig } from 'vite'

// 3. 类型导入（使用 import type）
import type { ApiResponse, PaginationParams } from '@/types'
import type { FormInst, FormRules } from 'naive-ui'

// 4. 项目内部模块
import { httpClient } from '@/apis'
import { useUserStore } from '@/stores/user'
import CustomComponent from '@/components/CustomComponent.vue'

// 5. 样式文件
import './styles/index.less'
```

**❌ 错误示例（冗余导入）：**
```typescript
// ❌ 这些由 auto-import 处理，不要手动导入
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import { UserOutlined } from '@vicons/antd'
```

**✅ 正确示例（优化后）：**
```typescript
// 第三方库组件
import { NForm, NFormItem } from 'naive-ui'
import type { FormInst } from 'naive-ui'

// 项目内部模块
import { useUserStore } from '@/stores/user'

// 注意：ref, computed, onMounted, useRouter, useMessage, IconUserOutlined 等
// 由 auto-import 自动处理，可直接使用
const count = ref(0)
const router = useRouter()
const message = useMessage()
```
### 7.2 导入路径规范
- 基于文件名实际情况，保持大小写一致
- 使用 `@/` 别名引用 src 目录
- 相对路径导入放在最后
## 8. 代码风格规范
### 8.1 引号和分号
- **统一使用单引号** `'` 
- **语句末尾不使用分号**（除非必要）
### 8.2 命名规范总览
- **变量和函数**：camelCase（如 `userName`, `handleClick`）
- **常量**：UPPER_SNAKE_CASE（如 `API_BASE_URL`, `MAX_RETRY_COUNT`）
- **类和接口**：PascalCase（如 `User`, `ApiResponse`）
- **组件标签**：PascalCase（如 `<ErrorBoundary>`、`<TaskView>`）
- **事件名**：
  - 模板中：kebab-case（如 `@field-update`）
  - 定义中：camelCase（如 `fieldUpdate`）
- **文件命名**：
  - 页面文件：snake_case（如 `project_overview.vue`）
  - 组件文件：PascalCase（如 `ProjectCard.vue`）
  - 工具文件：camelCase（如 `errorHandler.ts`）
### 8.3 注释规范
- 使用中文注释
- 重要的业务逻辑必须添加注释
- API 接口添加 JSDoc 注释
```typescript
/**
 * 获取项目列表
 * @param params 查询参数
 * @returns 项目列表响应
 */
static async getProjects(params?: ProjectQueryParams): Promise<ApiResponse<PaginationResponse<Project>>> {
  // 实现逻辑
}
```
## 9. 图标使用规范
### 9.1 图标命名
- 使用 `Icon` 前缀
- 来源于 `@vicons/antd`
- 自动导入配置
### 9.2 使用方式
```vue
<template>
  <n-icon>
    <icon-ant-design-project-outlined />
  </n-icon>
</template>
```
## 10. 路由规范
### 10.1 路由定义
```typescript
const routes: Array<RouteRecordRaw> = [
  {
    path: '/project/detail/:id',
    name: 'ProjectDetail',
    component: ProjectDetail,
    meta: { 
      title: '项目详情',
      requiresAuth: true,
      keepAlive: false
    }
  }
]
```
## 11. 代码质量规范
### 11.1 文件大小限制
- 单个文件不超过 300 行
- 单个函数不超过 50 行
- 合理使用 composables 抽取逻辑
### 11.2 错误处理
- 统一使用 try-catch 处理异步操作
- 提供用户友好的错误提示
- 记录详细的错误日志

