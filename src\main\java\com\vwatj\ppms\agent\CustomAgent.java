package com.vwatj.ppms.agent;

import com.vwatj.ppms.dto.ChatRequestDTO;
import com.vwatj.ppms.dto.ChatResponseDTO;
import com.vwatj.ppms.entity.Agent;

import java.util.List;
import java.util.Map;

/**
 * 自定义Agent抽象接口
 * 提供业务功能集成的抽象方法
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface CustomAgent {
    
    /**
     * 获取Agent类型
     */
    String getAgentType();
    
    /**
     * 获取Agent名称
     */
    String getAgentName();
    
    /**
     * 获取Agent描述
     */
    String getAgentDescription();
    
    /**
     * 初始化Agent
     * 
     * @param agent Agent配置
     */
    void initialize(Agent agent);
    
    /**
     * 处理聊天请求
     * 
     * @param request 聊天请求
     * @return 聊天响应
     */
    ChatResponseDTO chat(ChatRequestDTO request);
    
    /**
     * 处理流式聊天请求
     * 
     * @param request 聊天请求
     * @return 流式响应内容
     */
    String streamChat(ChatRequestDTO request);
    
    /**
     * 获取支持的业务功能列表
     * 
     * @return 功能名称列表
     */
    List<String> getSupportedFunctions();
    
    /**
     * 执行业务功能
     * 
     * @param functionName 功能名称
     * @param parameters 参数
     * @return 执行结果
     */
    Object executeFunction(String functionName, Map<String, Object> parameters);
    
    /**
     * 验证Agent配置
     * 
     * @param agent Agent配置
     * @return 是否有效
     */
    boolean validateConfig(Agent agent);
    
    /**
     * 获取Agent状态
     * 
     * @return 状态信息
     */
    Map<String, Object> getStatus();
    
    /**
     * 销毁Agent资源
     */
    void destroy();
}
