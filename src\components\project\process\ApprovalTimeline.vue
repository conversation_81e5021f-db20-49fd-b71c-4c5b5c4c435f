<template>
  <div class="approval-timeline">
    <n-card :bordered="false" class="shadow-sm">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium">{{ stage.label }} - 流程管理</h3>
          <n-tag :type="statusType" size="small" round>
            {{ statusText }}
          </n-tag>
        </div>
      </template>

      <!-- 横向时间线 -->
      <div class="timeline-container">
        <div class="timeline-wrapper">
          <!-- 时间线背景线 -->
          <div class="timeline-line"></div>

          <!-- 时间线节点 -->
          <div class="timeline-nodes">
            <div
              v-for="(node, index) in safeNodes"
              :key="node.key || index"
              class="timeline-node"
              :class="getNodeClass(node.status)"
              @click="handleNodeClick(node, index)"
            >
              <!-- 节点圆圈 -->
              <div class="node-circle">
                <n-icon :component="getNodeIcon(node.type)" :size="16" />
              </div>

              <!-- 节点标题 -->
              <div class="node-title">{{ node.title || '未知步骤' }}</div>

              <!-- 节点状态 -->
              <div class="node-status">
                <n-tag :type="getStatusType(node.status)" size="small">
                  {{ getStatusText(node.status) }}
                </n-tag>
              </div>

              <!-- 节点时间 -->
              <div class="node-time" v-if="node.completedAt">
                {{ formatDate(node.completedAt) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 当前节点内容 -->
      <div class="current-node-content">
        <n-divider />

        <div v-if="currentNode">
          <component
            :is="getNodeComponent(currentNode.type)"
            :title="currentNode.title"
            :status="currentNode.status"
            :data="currentNode.data"
            :readonly="currentNode.status === 'completed'"
            :default-assignee="getDefaultAssignee()"
            :previous-node-data="getPreviousNodeData()"
            :previous-node-type="getPreviousNodeType()"
            :previous-node-title="getPreviousNodeTitle()"
            @submit="handleNodeSubmit"
            @edit="handleNodeEdit"
            @view-previous="handleViewPrevious"
            @edit-previous="handleEditPrevious"
          />
        </div>

        <!-- 表单未配置占位图 -->
        <div v-else class="form-not-configured">
          <n-empty description="表单未配置" class="py-16">
            <template #icon>
              <n-icon :size="80" class="text-gray-300">
                <FormOutlined />
              </n-icon>
            </template>
            <template #extra>
              <div class="text-gray-500 text-sm mt-4">
                请联系管理员配置此阶段的表单流程
              </div>
            </template>
          </n-empty>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  NCard, NIcon, NTag, NDivider, NEmpty, useMessage
} from 'naive-ui'
import {
  UserOutlined, FormOutlined, CheckCircleOutlined, CloudUploadOutlined
} from '@vicons/antd'
import type { ProcessNode, NodeType, ProcessForm } from '@/types/project_process'

// 导入节点组件
import DocumentUploadNode from './nodes/DocumentUploadNode.vue'
import AssignPersonNode from './nodes/AssignPersonNode.vue'
import FormNode from './nodes/FormNode.vue'
import CompleteNode from './nodes/CompleteNode.vue'

const props = defineProps<{
  stage: any
  forms?: ProcessForm[]
}>()

const emit = defineEmits(['submit', 'node-update'])

const message = useMessage()
const currentNodeIndex = ref(0)

// 安全的节点数组
const safeNodes = computed(() => {
  try {
    if (!props.stage?.nodes || !Array.isArray(props.stage.nodes)) {
      return []
    }
    return props.stage.nodes.filter((node: any) => node && typeof node === 'object')
  } catch (error) {
    console.error('获取节点数据失败:', error)
    return []
  }
})

// 当前节点
const currentNode = computed(() => {
  try {
    if (!props.stage?.nodes || !Array.isArray(props.stage.nodes) || props.stage.nodes.length === 0) {
      return null
    }

    const currentNodeFromStatus = props.stage.nodes.find((node: any) => node && node.status === 'current')
    if (currentNodeFromStatus) {
      return currentNodeFromStatus
    }

    // 如果没有current状态的节点，返回第一个pending节点
    const pendingNode = props.stage.nodes.find((node: any) => node && node.status === 'pending')
    return pendingNode || props.stage.nodes[0] || null
  } catch (error) {
    console.error('计算当前节点失败:', error)
    return null
  }
})

// 状态映射
const statusMap = {
  wait: { type: 'default', text: '未开始' },
  process: { type: 'info', text: '进行中' },
  finish: { type: 'success', text: '已完成' },
  error: { type: 'error', text: '异常' },
  warning: { type: 'warning', text: '警告' }
}

const statusType = computed((): 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary' => {
  if (!props.stage) return 'default'
  return (statusMap as any)[props.stage.status || 'wait']?.type || 'default'
})

const statusText = computed(() => {
  if (!props.stage) return '未知'
  const status = props.stage.status || 'wait'
  return statusMap[status as keyof typeof statusMap]?.text || '未知'
})

// 获取节点图标
const getNodeIcon = (nodeType: NodeType) => {
  switch (nodeType) {
    case 'upload_document': return CloudUploadOutlined
    case 'assign_person': return UserOutlined
    case 'form': return FormOutlined
    case 'complete': return CheckCircleOutlined
    default: return FormOutlined
  }
}

// 获取节点组件
const getNodeComponent = (nodeType: NodeType) => {
  switch (nodeType) {
    case 'upload_document': return DocumentUploadNode
    case 'assign_person': return AssignPersonNode
    case 'form': return FormNode
    case 'complete': return CompleteNode
    default: return FormNode
  }
}

// 获取节点样式类
const getNodeClass = (status: string) => {
  return {
    'node-current': status === 'current',
    'node-completed': status === 'completed',
    'node-pending': status === 'pending'
  }
}

// 获取状态类型
const getStatusType = (status: string): 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary' => {
  switch (status) {
    case 'current': return 'info'
    case 'completed': return 'success'
    case 'pending': return 'default'
    default: return 'default'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'current': return '进行中'
    case 'completed': return '已完成'
    case 'pending': return '待处理'
    default: return '未知'
  }
}

// 获取默认负责人（项目PM）
const getDefaultAssignee = () => {
  // 这里可以从项目信息中获取PM信息
  return 'zhangsan' // 默认PM
}

// 获取上一个节点的数据
const getPreviousNodeData = () => {
  if (!props.stage?.nodes || !currentNode.value) return null

  const currentIndex = props.stage.nodes.findIndex((node: ProcessNode) => node === currentNode.value)
  if (currentIndex > 0) {
    return props.stage.nodes[currentIndex - 1].data
  }
  return null
}

// 获取上一个节点的类型
const getPreviousNodeType = () => {
  if (!props.stage?.nodes || !currentNode.value) return null

  const currentIndex = props.stage.nodes.findIndex((node: ProcessNode) => node === currentNode.value)
  if (currentIndex > 0) {
    return props.stage.nodes[currentIndex - 1].type
  }
  return null
}

// 获取上一个节点的标题
const getPreviousNodeTitle = () => {
  if (!props.stage?.nodes || !currentNode.value) return ''

  const currentIndex = props.stage.nodes.findIndex((node: ProcessNode) => node === currentNode.value)
  if (currentIndex > 0) {
    return props.stage.nodes[currentIndex - 1].title
  }
  return ''
}

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理节点点击
const handleNodeClick = (node: ProcessNode, index: number) => {
  if (node.status === 'completed' || node.status === 'current') {
    currentNodeIndex.value = index
  }
}

// 处理节点提交
const handleNodeSubmit = (data: any) => {
  if (!currentNode.value || !props.stage?.nodes) return

  // 更新当前节点数据和状态
  currentNode.value.data = data
  currentNode.value.status = 'completed'
  currentNode.value.completedAt = new Date().toISOString()

  // 激活下一个节点
  const currentIndex = props.stage.nodes.findIndex((node: ProcessNode) => node === currentNode.value)
  if (currentIndex >= 0 && currentIndex < props.stage.nodes.length - 1) {
    props.stage.nodes[currentIndex + 1].status = 'current'
  }

  // 更新阶段进度
  const completedNodes = props.stage.nodes.filter((node: ProcessNode) => node.status === 'completed').length
  props.stage.progress = Math.round((completedNodes / props.stage.nodes.length) * 100)

  // 如果所有节点都完成，更新阶段状态
  if (completedNodes === props.stage.nodes.length) {
    props.stage.status = 'finish'
  } else {
    props.stage.status = 'process'
  }

  emit('node-update', {
    stageKey: props.stage.key,
    nodeKey: currentNode.value.key,
    data
  })

  message.success('节点提交成功')
}

// 处理节点编辑
const handleNodeEdit = () => {
  if (currentNode.value) {
    currentNode.value.status = 'current'
  }
}

// 处理查看上一节点
const handleViewPrevious = () => {
  // 这里可以实现查看上一节点的逻辑
  message.info('查看上一节点数据')
}

// 处理编辑上一节点
const handleEditPrevious = () => {
  if (!props.stage?.nodes || !currentNode.value) return

  const currentIndex = props.stage.nodes.findIndex((node: ProcessNode) => node === currentNode.value)
  if (currentIndex > 0) {
    const previousNode = props.stage.nodes[currentIndex - 1]
    previousNode.status = 'current'
    currentNode.value.status = 'pending'
  }
}
</script>

<style scoped>
.approval-timeline {
  width: 100%;
  height: 100%;
}

/* 时间线容器 */
.timeline-container {
  margin: 24px 0;
  padding: 20px 0;
}

.timeline-wrapper {
  position: relative;
  width: 100%;
}

/* 时间线背景线 */
.timeline-line {
  position: absolute;
  top: 32px;
  left: 32px;
  right: 32px;
  height: 2px;
  background: linear-gradient(to right, #e5e7eb 0%, #e5e7eb 100%);
  z-index: 1;
}

/* 时间线节点容器 */
.timeline-nodes {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

/* 时间线节点 */
.timeline-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

/* 节点圆圈 */
.node-circle {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border: 3px solid #e5e7eb;
  color: #9ca3af;
  transition: all 0.3s ease;
  margin-bottom: 12px;
}

/* 节点状态样式 */
.node-current .node-circle {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.node-completed .node-circle {
  background: #dcfce7;
  border-color: #22c55e;
  color: #22c55e;
}

.node-pending .node-circle {
  background: #f3f4f6;
  border-color: #e5e7eb;
  color: #9ca3af;
}

/* 节点标题 */
.node-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  text-align: center;
}

/* 节点状态标签 */
.node-status {
  margin-bottom: 4px;
}

/* 节点时间 */
.node-time {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

/* 表单区域 */
.step-form {
  margin-top: 24px;
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

/* 完成状态 */
.completion-info {
  text-align: center;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-nodes {
    flex-direction: column;
    gap: 24px;
  }

  .timeline-line {
    display: none;
  }

  .timeline-node {
    flex-direction: row;
    text-align: left;
    align-items: flex-start;
  }

  .node-circle {
    margin-right: 16px;
    margin-bottom: 0;
    width: 48px;
    height: 48px;
    flex-shrink: 0;
  }

  .node-title {
    text-align: left;
    margin-bottom: 4px;
  }
}

/* 暗黑模式适配 */
.dark .timeline-line {
  background: linear-gradient(to right, #374151 0%, #374151 100%);
}

.dark .node-circle {
  background: #374151;
  border-color: #4b5563;
  color: #9ca3af;
}

.dark .node-current .node-circle {
  background: #1e3a8a;
  border-color: #3b82f6;
  color: #60a5fa;
}

.dark .node-completed .node-circle {
  background: #14532d;
  border-color: #22c55e;
  color: #4ade80;
}

.dark .node-title {
  color: #f3f4f6;
}

.dark .form-title {
  color: #f3f4f6;
  border-bottom-color: #374151;
}
</style>
