// 第三方库
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import naive from 'naive-ui'

// 项目内部模块
import App from './App.vue'
import router from './router'

// 样式文件
// import '@unocss/reset/tailwind.css'
import 'uno.css'
import 'vfonts/Lato.css'
import 'vfonts/FiraCode.css'

// 开发环境下导入测试工具
if (import.meta.env.DEV) {
  import('./utils/agent-test').then(({ AgentTester }) => {
    (window as any).AgentTester = AgentTester
    console.log('🔧 开发模式：Agent测试工具已加载，使用 AgentTester.runFullTest() 进行测试')
  })
}

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(naive)

app.mount('#app')
