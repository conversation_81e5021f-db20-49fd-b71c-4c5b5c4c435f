<template>
  <n-config-provider
    :theme-overrides="themeOverrides"
    class="app-container"
  >
    <n-loading-bar-provider>
      <n-message-provider>
        <n-dialog-provider>
          <ErrorBoundary>
            <RouterView />
          </ErrorBoundary>

          <!-- 全局加载指示器 -->
          <GlobalLoading />
        </n-dialog-provider>
      </n-message-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
// 第三方库组件（未被 auto-import 的）
import {
  NLoadingBarProvider,
  NMessageProvider,
  NDialogProvider,
  NConfigProvider,
  type GlobalThemeOverrides
} from 'naive-ui'

// 项目内部模块
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import ErrorBoundary from '@/components/common/ErrorBoundary.vue'
import GlobalLoading from '@/components/common/GlobalLoading.vue'

// 样式文件
import '@/assets/styles/index.less'

// 主题配置
const themeStore = useThemeStore()
const themeOverrides = themeStore.themeOverrides

// 用户认证初始化
const userStore = useUserStore()
// onMounted 由 auto-import 自动处理，无需手动导入
onMounted(async () => {
  try {
    await userStore.initializeAuth()
  } catch (error) {
    console.error('用户认证初始化失败:', error)
  }
})
</script>

<style scoped>
.app-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}
</style>