<template>
  <div class="agent-chat h-full flex flex-col">
    <!-- 聊天头部 -->
    <n-card :bordered="false" class="border-b border-gray-100 dark:border-gray-700 rounded-none" size="small">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <n-avatar
            round
            :size="36"
            class="mr-3"
          >
            <template #default>{{ agentInfo?.name?.charAt(0) || 'A' }}</template>
          </n-avatar>
          <div>
            <div class="font-medium text-base">{{ agentInfo?.name || 'AI 助手' }}</div>
            <div class="text-xs text-gray-500">
              {{ agentStatus === 'online' ? '在线' : '离线' }}
              <n-badge :type="agentStatus === 'online' ? 'success' : 'default'" dot class="ml-1" />
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button text @click="showAgentFunctions">
                <n-icon :size="18">
                  <SettingOutlined />
                </n-icon>
              </n-button>
            </template>
            Agent功能
          </n-tooltip>
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-switch
                v-model:value="useStreamMode"
                size="small"
                :disabled="isStreaming"
              >
                <template #checked>流式</template>
                <template #unchecked>普通</template>
              </n-switch>
            </template>
            {{ useStreamMode ? '流式聊天模式' : '普通聊天模式' }}
          </n-tooltip>
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button text @click="toggleFullscreen">
                <n-icon :size="18">
                  <FullscreenOutlined v-if="!isFullscreen" />
                  <FullscreenExitOutlined v-else />
                </n-icon>
              </n-button>
            </template>
            {{ isFullscreen ? '退出全屏' : '全屏' }}
          </n-tooltip>
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button text @click="clearMessages">
                <n-icon :size="18">
                  <DeleteOutlined />
                </n-icon>
              </n-button>
            </template>
            清空对话
          </n-tooltip>
        </div>
      </div>
    </n-card>

    <!-- 聊天消息区域 -->
    <div ref="messageContainer" class="flex-1 overflow-y-auto p-4 space-y-4">
      <template v-if="messages.length > 0">
        <div
          v-for="(message, index) in messages"
          :key="index"
          :class="[
            'flex',
            message.role === 'user' ? 'justify-end' : 'justify-start'
          ]"
        >
          <div
            :class="[
              'max-w-3/4 rounded-lg p-3',
              message.role === 'user'
                ? 'bg-blue-500 text-white rounded-br-none'
                : 'bg-gray-100 dark:bg-gray-700 rounded-bl-none'
            ]"
          >
            <div class="whitespace-pre-wrap">{{ message.content }}</div>
            <div class="text-xs mt-1 opacity-70 text-right">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
      </template>
      <div v-else class="h-full flex items-center justify-center text-gray-400">
        <n-empty description="开始与AI助手对话" />
      </div>

      <!-- 加载状态 -->
      <div v-if="loading && !isStreaming" class="flex justify-start">
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 rounded-bl-none">
          <n-space align="center">
            <n-spin size="small" />
            <span>思考中...</span>
          </n-space>
        </div>
      </div>

      <!-- 流式输入状态 -->
      <div v-if="isStreaming" class="flex justify-start">
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 rounded-bl-none">
          <n-space align="center">
            <n-spin size="small" />
            <span>正在接收回复...</span>
          </n-space>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div v-if="messages.length === 1" class="px-4 pb-2">
      <div class="text-sm text-gray-500 mb-2">快速操作：</div>
      <div class="flex flex-wrap gap-2">
        <n-button size="small" @click="sendQuickMessage('你好，请介绍一下你的功能')">
          功能介绍
        </n-button>
        <n-button size="small" @click="sendQuickMessage('帮我查看当前项目状态')">
          项目状态
        </n-button>
        <n-button size="small" @click="sendQuickMessage('显示我的待办任务')">
          待办任务
        </n-button>
        <n-button size="small" @click="sendQuickMessage('生成项目报告')">
          生成报告
        </n-button>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="border-t border-gray-100 dark:border-gray-700 p-4">
      <div class="flex items-center mb-2 text-xs text-gray-500">
        <span>{{ useStreamMode ? '🔄 流式模式' : '📝 普通模式' }}</span>
        <span v-if="isStreaming" class="ml-2 text-blue-500">正在接收回复...</span>
      </div>
      <n-input
        v-model:value="inputMessage"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 4 }"
        :placeholder="useStreamMode ? '输入消息（流式回复）...' : '输入消息（一次性回复）...'"
        :disabled="loading || isStreaming"
        @keydown.enter.exact.prevent="sendMessage"
      >
        <template #suffix>
          <n-button
            type="primary"
            text
            :disabled="!inputMessage.trim() || loading || isStreaming"
            @click="sendMessage"
          >
            <template #icon>
              <n-icon><SendOutlined /></n-icon>
            </template>
          </n-button>
        </template>
      </n-input>
    </div>

    <!-- Agent功能弹窗 -->
    <n-modal v-model:show="showFunctionsModal" title="Agent功能列表" preset="dialog">
      <div class="space-y-4">
        <div v-if="agentFunctions.length === 0" class="text-center text-gray-500">
          该Agent暂无可用功能
        </div>
        <div v-else>
          <div class="text-sm text-gray-600 mb-3">该Agent支持以下功能：</div>
          <div class="space-y-2">
            <n-tag
              v-for="func in agentFunctions"
              :key="func"
              type="info"
              size="medium"
              round
              class="mr-2 mb-2"
            >
              {{ func }}
            </n-tag>
          </div>
        </div>
      </div>
      <template #action>
        <n-button @click="showFunctionsModal = false">关闭</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import {
  SendOutlined,
  DeleteOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SettingOutlined
} from '@vicons/antd'
import { AgentApi } from '@/apis'
import type { Agent } from '@/types'
import type { ChatMessage } from '@/apis/chat'

const route = useRoute()
const message = useMessage()
const dialog = useDialog()

// Agent信息
const agentId = computed(() => Number(route.params.id))
const agentInfo = ref<Agent | null>(null)
const agentStatus = ref<'online' | 'offline'>('offline')

// 消息相关
const inputMessage = ref('')
const messages = ref<ChatMessage[]>([])
const sessionId = ref<string>('')

// 加载状态
const loading = ref(false)
const messageContainer = ref<HTMLElement | null>(null)

// 全屏状态
const isFullscreen = ref(false)

// Agent功能相关
const agentFunctions = ref<string[]>([])
const showFunctionsModal = ref(false)

// 流式聊天相关
const currentStreamMessage = ref('')
const isStreaming = ref(false)
const streamEventSource = ref<EventSource | null>(null)
const useStreamMode = ref(true) // 默认使用流式模式

// 发送消息（非流式，备用方法）
const sendMessageNonStream = async () => {
  const content = inputMessage.value.trim()
  if (!content || loading.value || !agentInfo.value) return

  // 添加用户消息
  const userMessage: ChatMessage = {
    role: 'user',
    content,
    timestamp: Date.now()
  }
  messages.value.push(userMessage)
  inputMessage.value = ''

  // 滚动到底部
  scrollToBottom()

  // 设置加载状态
  loading.value = true

  try {
    // 调用普通聊天API
    const chatRequest = {
      agentId: agentInfo.value.id,
      message: content,
      sessionId: sessionId.value || `session-${Date.now()}`,
      userId: 'current-user',
      history: messages.value.slice(-10),
      enableFunctionCalling: true
    }

    const response = await AgentApi.chat(chatRequest)

    if (response.code === 200) {
      // 更新会话ID
      if (response.data.sessionId) {
        sessionId.value = response.data.sessionId
      }

      // 添加AI回复
      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: response.data.message,
        timestamp: response.data.timestamp || Date.now()
      }
      messages.value.push(aiMessage)

      // 滚动到底部
      scrollToBottom()
    } else {
      message.error(response.message || '发送失败')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    message.error('发送失败，请重试')
  } finally {
    loading.value = false
  }
}

// 发送快速消息
const sendQuickMessage = (content: string) => {
  inputMessage.value = content
  sendMessage()
}

// 发送消息（根据模式选择流式或非流式）
const sendMessage = async () => {
  if (useStreamMode.value) {
    await sendMessageStream()
  } else {
    await sendMessageNonStream()
  }
}

// 发送消息（流式）
const sendMessageStream = async () => {
  const content = inputMessage.value.trim()
  if (!content || loading.value || !agentInfo.value) return

  // 添加用户消息
  const userMessage: ChatMessage = {
    role: 'user',
    content,
    timestamp: Date.now()
  }
  messages.value.push(userMessage)
  inputMessage.value = ''

  // 滚动到底部
  scrollToBottom()

  // 设置加载状态
  loading.value = true
  isStreaming.value = true
  currentStreamMessage.value = ''

  // 创建AI消息占位符
  const aiMessage: ChatMessage = {
    role: 'assistant',
    content: '',
    timestamp: Date.now()
  }
  messages.value.push(aiMessage)
  const aiMessageIndex = messages.value.length - 1

  try {
    // 调用流式聊天API
    const chatRequest = {
      agentId: agentInfo.value.id,
      message: content,
      sessionId: sessionId.value || `session-${Date.now()}`,
      userId: 'current-user', // TODO: 从用户状态获取
      history: messages.value.slice(-12, -1), // 发送最近10条消息作为上下文，排除刚添加的空消息
      enableFunctionCalling: true
    }

    // 使用流式聊天
    await AgentApi.streamChat(
      chatRequest,
      // onMessage: 接收到消息块时的回调
      (chunk: string) => {
        currentStreamMessage.value += chunk
        messages.value[aiMessageIndex].content = currentStreamMessage.value
        scrollToBottom()
      },
      // onComplete: 流式传输完成时的回调
      () => {
        isStreaming.value = false
        loading.value = false
        // 更新会话ID（如果需要）
        if (!sessionId.value) {
          sessionId.value = `session-${Date.now()}`
        }
        console.log('流式聊天完成')
      },
      // onError: 发生错误时的回调
      (error: Error) => {
        console.error('流式聊天错误:', error)
        isStreaming.value = false
        loading.value = false
        message.error('流式聊天失败，尝试切换到普通模式')
        // 移除失败的消息
        messages.value.splice(aiMessageIndex, 1)
        // 自动切换到非流式模式并重试
        useStreamMode.value = false
        setTimeout(() => sendMessageNonStream(), 1000)
      }
    )
  } catch (error) {
    console.error('发送消息失败:', error)
    isStreaming.value = false
    loading.value = false
    message.error('发送失败，请重试')
    // 移除失败的消息
    messages.value.splice(aiMessageIndex, 1)
  }
}

// 清空消息
const clearMessages = () => {
  dialog.warning({
    title: '确认清空',
    content: '确定要清空当前对话记录吗？此操作不可恢复。',
    positiveText: '清空',
    negativeText: '取消',
    onPositiveClick: () => {
      messages.value = []
      message.success('已清空对话记录')
    }
  })
}

// 显示Agent功能
const showAgentFunctions = async () => {
  if (!agentInfo.value) return

  try {
    const response = await AgentApi.getAgentFunctions(agentInfo.value.id)
    if (response.code === 200) {
      agentFunctions.value = response.data
      showFunctionsModal.value = true
    } else {
      message.error('获取Agent功能失败')
    }
  } catch (error) {
    console.error('获取Agent功能失败:', error)
    message.error('获取Agent功能失败')
  }
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }
}

// 监听全屏变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

// 加载Agent信息
const loadAgentInfo = async () => {
  try {
    const response = await AgentApi.getAgent(agentId.value)
    if (response.code === 200) {
      agentInfo.value = response.data
      agentStatus.value = response.data.status === 1 ? 'online' : 'offline'
    } else {
      message.error('加载Agent信息失败')
    }
  } catch (error) {
    console.error('加载Agent信息失败:', error)
    message.error('加载Agent信息失败')
  }
}

// 加载对话历史
const loadChatHistory = () => {
  // 这里可以加载本地存储的对话历史
  const savedMessages = localStorage.getItem(`chat_${agentId.value}`)
  if (savedMessages) {
    try {
      messages.value = JSON.parse(savedMessages)
    } catch (error) {
      console.error('加载对话历史失败:', error)
    }
  }
}

// 保存对话历史
const saveChatHistory = () => {
  if (messages.value.length > 0) {
    localStorage.setItem(`chat_${agentId.value}`, JSON.stringify(messages.value))
  }
}

// 组件挂载时
onMounted(async () => {
  // 加载Agent信息
  await loadAgentInfo()

  // 加载对话历史
  loadChatHistory()
  document.addEventListener('fullscreenchange', handleFullscreenChange)

  // 添加一个欢迎消息
  if (messages.value.length === 0) {
    messages.value.push({
      role: 'assistant',
      content: `你好！我是${agentInfo.value?.name || 'AI助手'}，有什么可以帮您的吗？`,
      timestamp: Date.now()
    })
  }

  // 滚动到底部
  scrollToBottom()
})

// 组件卸载时
onUnmounted(() => {
  saveChatHistory()
  document.removeEventListener('fullscreenchange', handleFullscreenChange)

  // 清理流式连接
  if (streamEventSource.value) {
    streamEventSource.value.close()
    streamEventSource.value = null
  }
})

// 监听路由变化
watch(() => route.params.id, async (newId) => {
  if (newId) {
    // 保存当前对话
    saveChatHistory()
    // 重置会话ID
    sessionId.value = ''
    // 加载新Agent信息
    await loadAgentInfo()
    // 加载新对话
    loadChatHistory()
  }
})
</script>

<style scoped>
.agent-chat {
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
}

.message-container {
  scroll-behavior: smooth;
}

/* 自定义滚动条 */
:deep(::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 暗黑模式滚动条 */n-dark :deep(::-webkit-scrollbar-thumb) {
  background: #4b5563;
}

n-dark :deep(::-webkit-scrollbar-thumb:hover) {
  background: #6b7280;
}

/* 输入框样式 */
:deep(.n-input) {
  border-radius: 8px;
}

:deep(.n-input__border) {
  border-color: #e5e7eb;
}

:deep(.n-input--focus .n-input__border) {
  border-color: var(--n-color-focus);
  box-shadow: 0 0 0 2px var(--n-box-shadow-focus);
}

/* 暗黑模式输入框 */n-dark :deep(.n-input__border) {
  border-color: #374151;
  background-color: #1f2937;
}

/* 按钮悬停效果 */
:deep(.n-button) {
  transition: all 0.2s;
}

:deep(.n-button--primary-type) {
  background-color: var(--n-color);
}

:deep(.n-button--primary-type:hover) {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 消息气泡动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.message-enter-active) {
  animation: fadeIn 0.3s ease-out;
}

:deep(.message-leave-active) {
  animation: fadeIn 0.3s reverse;
}
</style>
