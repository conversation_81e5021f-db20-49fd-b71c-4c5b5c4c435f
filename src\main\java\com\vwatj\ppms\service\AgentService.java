package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.agent.CustomAgent;
import com.vwatj.ppms.dto.*;
import com.vwatj.ppms.entity.Agent;

import java.util.List;
import java.util.Map;

/**
 * 统一Agent服务接口
 * 合并了原AgentService、CustomAgentService、UnifiedAgentService和ChatService的功能
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface AgentService extends IService<Agent> {
    
    /**
     * 分页查询Agent
     */
    PageResult<Agent> getAgentPage(AgentQueryDTO queryDTO);
    
    /**
     * 创建Agent（支持通用和预设类型）
     */
    Agent createAgent(CreateAgentDTO createAgentDTO);

    /**
     * 更新Agent
     */
    Agent updateAgent(UpdateAgentDTO updateAgentDTO);
    
    /**
     * 删除Agent
     */
    void deleteAgent(Long id);
    
    /**
     * 根据名称查询Agent
     */
    Agent getAgentByName(String name);
    
    /**
     * 切换Agent状态
     */
    void toggleAgentStatus(Long id);

    // ==================== Agent管理相关方法 ====================

    /**
     * 初始化Agent（支持所有类型）
     *
     * @param agentId Agent ID
     */
    void initializeAgent(Long agentId);

    /**
     * 处理Agent聊天请求（支持所有类型）
     *
     * @param request 聊天请求
     * @return 聊天响应
     */
    ChatResponseDTO chat(ChatRequestDTO request);

    /**
     * 处理Agent流式聊天请求（支持所有类型）
     *
     * @param request 聊天请求
     * @return 流式响应内容
     */
    String streamChat(ChatRequestDTO request);

    /**
     * 获取Agent支持的功能列表
     *
     * @param agentId Agent ID
     * @return 功能列表
     */
    List<String> getSupportedFunctions(Long agentId);

    /**
     * 执行Agent功能
     *
     * @param agentId Agent ID
     * @param functionName 功能名称
     * @param parameters 参数
     * @return 执行结果
     */
    Object executeFunction(Long agentId, String functionName, Map<String, Object> parameters);

    /**
     * 验证Agent配置
     *
     * @param agentId Agent ID
     * @return 是否有效
     */
    boolean validateAgentConfig(Long agentId);

    /**
     * 获取Agent状态
     *
     * @param agentId Agent ID
     * @return 状态信息
     */
    Map<String, Object> getAgentStatus(Long agentId);

    /**
     * 销毁Agent实例
     *
     * @param agentId Agent ID
     */
    void destroyAgent(Long agentId);

    /**
     * 获取所有可用的业务功能
     *
     * @return 功能列表
     */
    List<String> getAllAvailableFunctions();

    /**
     * 获取业务功能的Schema定义
     *
     * @return 功能Schema映射
     */
    Map<String, Map<String, Object>> getFunctionSchemas();

    // ==================== 底层Agent管理方法 ====================

    /**
     * 获取Agent实例
     * 通过反射创建或获取缓存的Agent实例
     *
     * @param agent Agent配置
     * @return Agent实例
     */
    CustomAgent getAgentInstance(Agent agent);

    /**
     * 获取Agent支持的功能列表（原ChatService方法）
     *
     * @param agentId Agent ID
     * @return 功能列表
     */
    String[] getAgentFunctions(Long agentId);
}
