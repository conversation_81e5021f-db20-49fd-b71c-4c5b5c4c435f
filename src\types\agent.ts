// Agent相关类型定义

export interface Agent {
  id: number
  name: string
  description?: string
  status: number // 0-禁用, 1-启用
  type?: string
  config?: string
  llmProvider?: string // LLM提供商 (openai, deepseek, qwen, etc.)
  llmModel?: string // LLM模型名称
  llmApiUrl?: string // LLM API URL
  llmApiKey?: string // LLM API Key
  llmParams?: string // LLM参数配置 (JSON格式)
  systemPrompt?: string // 系统提示词
  userPromptTemplate?: string // 用户提示词模板
  functionConfig?: string // 功能配置 (JSON格式)
  creatorId?: number
  creatorName?: string
  createdAt: string
  updatedAt: string
}

// Agent查询参数
export interface AgentQueryParams {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  keyword?: string
  status?: number
  type?: string
  creatorId?: number
}

// Agent创建参数
export interface CreateAgentParams {
  name: string
  description?: string
  status: number
  type?: string
  config?: string
  llmProvider: string // LLM提供商 (必填)
  llmModel: string // LLM模型名称 (必填)
  llmApiUrl: string // LLM API URL (必填)
  llmApiKey: string // LLM API Key (必填)
  llmParams?: string // LLM参数配置 (JSON格式)
  systemPrompt?: string // 系统提示词
  userPromptTemplate?: string // 用户提示词模板
  functionConfig?: string // 功能配置 (JSON格式)
}

// Agent更新参数
export interface UpdateAgentParams {
  id: number
  name?: string
  description?: string
  status?: number
  type?: string
  config?: string
  llmProvider?: string // LLM提供商
  llmModel?: string // LLM模型名称
  llmApiUrl?: string // LLM API URL
  llmApiKey?: string // LLM API Key
  llmParams?: string // LLM参数配置 (JSON格式)
  systemPrompt?: string // 系统提示词
  userPromptTemplate?: string // 用户提示词模板
  functionConfig?: string // 功能配置 (JSON格式)
}

// Agent状态信息
export interface AgentStatus {
  id: number
  name: string
  status: number // 0-禁用, 1-启用
  isOnline: boolean
  lastActiveTime?: string
  configValid: boolean
  supportedFunctions: string[]
  llmProvider?: string
  llmModel?: string
  errorMessage?: string
}

// 功能执行参数
export interface FunctionExecuteParams {
  agentId: number
  functionName: string
  parameters: Record<string, any>
}

// 功能Schema定义
export interface FunctionSchema {
  name: string
  description: string
  parameters: {
    type: string
    properties: Record<string, {
      type: string
      description: string
      required?: boolean
      enum?: string[]
    }>
    required?: string[]
  }
}

// Agent初始化参数
export interface AgentInitializeParams {
  agentId: number
  forceReinit?: boolean
}

// Agent类型选项
export const AGENT_TYPES = [
  { label: '客服Agent', value: 'customer_service' },
  { label: '代码审查Agent', value: 'code_review' },
  { label: '文档生成Agent', value: 'documentation' },
  { label: '测试Agent', value: 'testing' },
  { label: '数据分析Agent', value: 'data_analysis' },
  { label: '其他', value: 'other' }
] as const

// Agent状态选项
export const AGENT_STATUS_OPTIONS = [
  { label: '已启用', value: 1 },
  { label: '已禁用', value: 0 }
] as const
