<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import {
  NButton,
  NBreadcrumb,
  NBreadcrumbItem,
  NTag,
  NTabs,
  NTabPane,
  NSpace,
  NAvatar,
} from 'naive-ui';
import { ArrowLeftOutlined } from '@vicons/antd';
import type { ProjectInfo, ProjectDetailTask } from '@/types';
import { ProjectApi } from '@/apis/project';
import { getProjectStatusInfo } from '@/utils/project';

const route = useRoute();
const router = useRouter();
const message = useMessage();

// 项目信息
const projectInfo = ref<ProjectInfo>({
  id: Number(route.params.id),
  name: '加载中...',
  version: '1.0.0',
  description: '项目描述',
  status: '进行中',
  progress: 0,
  startDate: '',
  endDate: '',
  manager: '',
  relatedAssetId: 0,
  assetVersion: '',
  assetNo: '',
  assetName: '',
});

// 加载状态
const loading = ref(false);

// 获取项目详情数据
const fetchProjectDetail = async () => {
  try {
    loading.value = true;
    const projectId = Number(route.params.id);

    // 获取项目基本信息
    const projectResponse = await ProjectApi.getProject(projectId);
    if (projectResponse.data) {
      const project = projectResponse.data;
      projectInfo.value = {
        id: project.id || projectId,
        name: project.name || '未命名项目',
        version: project.version || '1.0.0',
        description: project.description || '',
        status: project.status || '进行中',
        progress: project.progress || 0,
        startDate: project.startDate ? new Date(project.startDate).toISOString().split('T')[0] : '',
        endDate: project.endDate ? new Date(project.endDate).toISOString().split('T')[0] : '',
        manager: project.manager || '',
        relatedAssetId: project.relatedAssetId || 0,
        assetVersion: project.assetVersion || '',
        assetNo: project.assetNo || '',
        assetName: project.assetName || '',
      };
    }

    // 获取项目概览信息
    try {
      const overviewResponse = await ProjectApi.getProjectOverview(projectId);
      if (overviewResponse.data) {
        // 更新任务统计和任务列表
        if (overviewResponse.data.taskStats) {
          // 直接赋值，不再使用 computed
          taskStats.value = overviewResponse.data.taskStats;

          const completed = overviewResponse.data.taskStats.completed;
          const total = overviewResponse.data.taskStats.total;
          taskStats.value.completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;
        }
        if (overviewResponse.data.taskList) {
          taskList.value.splice(0, taskList.value.length, ...overviewResponse.data.taskList);
        }

        if (overviewResponse.data.processSteps) {
          processSteps.value = overviewResponse.data.processSteps;
        }
      }
    } catch (overviewError) {
      console.warn('获取项目概览信息失败:', overviewError);
      // 概览信息获取失败不影响主要功能，只记录警告
    }
  } catch (error: any) {
    console.error('获取项目详情失败:', error);
    message.error(error.message || '获取项目详情失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchProjectDetail();
});

// 任务数据
const taskList = ref<ProjectDetailTask[]>([
  {
    id: 1,
    name: '项目初始化',
    assignee: '张三',
    status: '已完成',
    priority: '高',
    dueDate: '2023-01-15',
    avatar: '',
  },
  {
    id: 2,
    name: '需求分析',
    assignee: '李四',
    status: '进行中',
    priority: '中',
    dueDate: '2023-02-15',
    avatar: '',
  },
  {
    id: 3,
    name: 'UI设计',
    assignee: '王五',
    status: '未开始',
    priority: '中',
    dueDate: '2023-03-01',
    avatar: '',
  },
  {
    id: 4,
    name: '后端开发',
    assignee: '赵六',
    status: '进行中',
    priority: '高',
    dueDate: '2023-02-01',
    avatar: '',
  },
  {
    id: 5,
    name: '前端开发',
    assignee: '钱七',
    status: '未开始',
    priority: '中',
    dueDate: '2023-03-15',
    avatar: '',
  },
  {
    id: 6,
    name: '测试',
    assignee: '孙八',
    status: '未开始',
    priority: '低',
    dueDate: '2023-04-01',
    avatar: '',
  },
  {
    id: 7,
    name: '部署上线',
    assignee: '周九',
    status: '未开始',
    priority: '高',
    dueDate: '2023-04-15',
    avatar: '',
  },
]);

// 任务统计
const taskStats = ref({
  total: 0,
  completed: 0,
  inProgress: 0,
  notStarted: 0,
  toVerify: 0,
  completionRate: 0,
});

// 项目流程
const processSteps = ref<any[]>();

// 标签页配置
const tabs = [
  { name: 'overview', label: '概览' },
  { name: 'process', label: '流程' },
  { name: 'task', label: '任务' },
  { name: 'document', label: '文档' },
];

// 从路由名中提取标签页名称
const getTabNameFromRoute = (routeName: string | symbol | undefined | null): string => {
  if (!routeName) return 'overview';
  const name = routeName.toString();
  if (!name.startsWith('ProjectDetail')) return 'overview';
  // 将 'ProjectDetailOverview' 转为 'overview', 'ProjectDetailTask' 转为 'task' 等
  return name.replace('ProjectDetail', '').toLowerCase();
};

// 当前激活的标签页
const activeTab = ref(getTabNameFromRoute(route.name));

// 监听路由变化更新激活的标签页
watch(
  () => route.name,
  (newName) => {
    activeTab.value = getTabNameFromRoute(newName);
  },
  { immediate: true },
);

// 处理标签页切换
const handleTabChange = (tab: string) => {
  activeTab.value = tab;
  router.push({
    name: `ProjectDetail${tab.charAt(0).toUpperCase() + tab.slice(1)}`,
    params: { id: route.params.id },
  });
};

// 处理返回
const handleBack = () => {
  router.push({ name: 'ProjectOverview' });
};
</script>

<template>
  <div class="project-detail-container">
    <div class="project-header">
      <div class="header-content">
        <div class="header-left">
          <n-button text @click="handleBack" class="back-btn">
            <template #icon>
              <n-icon><arrow-left-outlined /></n-icon>
            </template>
            返回
          </n-button>
          <h2 class="project-title">{{ projectInfo.name }}</h2>
          <div class="project-tags">
            <n-tag :bordered="false" type="info" size="small" class="project-tag">
              项目编号：{{ projectInfo.id || '--' }}
            </n-tag>
            <n-tag 
              :bordered="false" 
              :type="getProjectStatusInfo(projectInfo.status).type" 
              size="small"
              class="project-tag"
            >
              {{ getProjectStatusInfo(projectInfo.status).text }}
            </n-tag>
          </div>
        </div>
        
        <n-tabs
          v-model:value="activeTab"
          type="line"
          animated
          @update:value="handleTabChange"
          class="header-tabs"
        >
          <n-tab
            v-for="tab in tabs"
            :key="tab.name"
            :name="tab.name"
            :tab="tab.label"
          />
        </n-tabs>
      </div>
    </div>

    <div class="page-content">

      <!-- 子路由视图 -->
      <router-view 
        :project-info="projectInfo" 
        :task-stats="taskStats"
        :task-list="taskList"
        :process-steps="processSteps"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';
/* 项目详情容器 */
.project-detail-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  overflow: auto;
  background-color: @bg-color;
  margin: 0;
  padding: 0;
}

/* 头部样式 */
.project-header {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
}

.project-header .header-content {
  width: 100%;
  margin: 0;
  padding: 0 1.5rem;
}

.project-header .header-left {
  display: flex;
  align-items: center;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.project-header .back-btn {
  margin-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #4b5563;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.project-header .back-btn:hover {
  color: #2563eb;
}

.project-header .project-title {
  margin: 0;
  margin-right: 1.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
  color: #111827;
}

.project-header .project-tags {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-header .project-tag {
  margin: 0;
}

/* 标签页样式 */
.project-header .header-tabs :deep(.n-tabs-nav) {
  margin: 0;
  padding: 0;
}

.project-header .header-tabs :deep(.n-tabs-nav) .n-tabs-tab {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s;
  cursor: pointer;
}

.project-header .header-tabs :deep(.n-tabs-nav) .n-tabs-tab:hover {
  color: #2563eb;
  background-color: #f9fafb;
}

.project-header .header-tabs :deep(.n-tabs-nav) .n-tabs-tab.n-tabs-tab--active {
  font-weight: 500;
  color: #2563eb;
}

.project-header .header-tabs :deep(.n-tabs-nav) .n-tabs-tab.n-tabs-tab--active .n-tabs-tab__label::after {
  background-color: #2563eb;
}

/* 页面内容区域 */
.project-content {
  flex: 1;
  padding: 1.5rem;
  width: 100%;
  margin: 0;
  border-radius: 4px;
  margin-top: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  overflow: auto;
}
</style>
