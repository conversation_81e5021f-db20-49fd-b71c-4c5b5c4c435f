package com.vwatj.ppms.agent.function;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.service.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目管理功能实现
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProjectManagementFunction implements BusinessFunction {
    
    private final ProjectService projectService;
    
    @Override
    public String getFunctionName() {
        return "project_management";
    }
    
    @Override
    public String getFunctionDescription() {
        return "项目管理功能，支持查询项目信息、获取项目统计、分析项目状态等";
    }
    
    @Override
    public Map<String, Object> getParameterSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        // action参数
        Map<String, Object> actionParam = new HashMap<>();
        actionParam.put("type", "string");
        actionParam.put("description", "操作类型：query_project, get_stats, analyze_status");
        actionParam.put("enum", List.of("query_project", "get_stats", "analyze_status"));
        properties.put("action", actionParam);
        
        // projectId参数
        Map<String, Object> projectIdParam = new HashMap<>();
        projectIdParam.put("type", "integer");
        projectIdParam.put("description", "项目ID（查询特定项目时使用）");
        properties.put("projectId", projectIdParam);
        
        // projectName参数
        Map<String, Object> projectNameParam = new HashMap<>();
        projectNameParam.put("type", "string");
        projectNameParam.put("description", "项目名称（按名称查询时使用）");
        properties.put("projectName", projectNameParam);

        // queryType参数
        Map<String, Object> queryTypeParam = new HashMap<>();
        queryTypeParam.put("type", "string");
        queryTypeParam.put("description", "查询类型：all, by_id, by_name, by_status, by_time");
        queryTypeParam.put("enum", List.of("all", "by_id", "by_name", "by_status", "by_time"));
        properties.put("queryType", queryTypeParam);

        // status参数
        Map<String, Object> statusParam = new HashMap<>();
        statusParam.put("type", "string");
        statusParam.put("description", "项目状态：active, completed, paused, cancelled");
        statusParam.put("enum", List.of("active", "completed", "paused", "cancelled"));
        properties.put("status", statusParam);

        // timeRange参数
        Map<String, Object> timeRangeParam = new HashMap<>();
        timeRangeParam.put("type", "string");
        timeRangeParam.put("description", "时间范围：today, this_week, this_month, recent");
        timeRangeParam.put("enum", List.of("today", "this_week", "this_month", "recent"));
        properties.put("timeRange", timeRangeParam);

        schema.put("properties", properties);
        schema.put("required", List.of("action"));
        
        return schema;
    }
    
    @Override
    public Object execute(Map<String, Object> parameters) {
        try {
            String action = (String) parameters.get("action");
            log.info("执行项目管理功能: action={}", action);
            
            switch (action) {
                case "query_project":
                    return queryProject(parameters);
                case "get_stats":
                    return getProjectStats();
                case "analyze_status":
                    return analyzeProjectStatus(parameters);
                default:
                    return Map.of("error", "不支持的操作类型: " + action);
            }
        } catch (Exception e) {
            log.error("执行项目管理功能失败", e);
            return Map.of("error", "执行失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean validateParameters(Map<String, Object> parameters) {
        if (parameters == null || !parameters.containsKey("action")) {
            return false;
        }
        
        String action = (String) parameters.get("action");
        if (action == null || action.trim().isEmpty()) {
            return false;
        }
        
        // 验证特定操作的参数
        switch (action) {
            case "query_project":
                // query_project操作总是有效的，因为支持查询全部
                return true;
            case "get_stats":
            case "analyze_status":
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 查询项目信息
     */
    private Object queryProject(Map<String, Object> parameters) {
        try {
            String queryType = (String) parameters.getOrDefault("queryType", "all");
            log.info("查询项目: queryType={}, parameters={}", queryType, parameters);

            List<Project> projects;
            String message;

            switch (queryType) {
                case "by_id":
                    projects = queryProjectById(parameters);
                    message = "按ID查询项目完成";
                    break;
                case "by_name":
                    projects = queryProjectByName(parameters);
                    message = "按名称查询项目完成";
                    break;
                case "by_status":
                    projects = queryProjectByStatus(parameters);
                    message = "按状态查询项目完成";
                    break;
                case "all":
                default:
                    projects = queryAllProjects();
                    message = "查询全部项目完成";
                    break;
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("projects", projects);
            result.put("total", projects.size());
            result.put("queryType", queryType);
            result.put("message", message);

            // 添加简单的统计信息
            if (projects.size() > 0) {
                Map<String, Long> statusCount = projects.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                        p -> p.getStatus() != null ? p.getStatus().toString() : "unknown",
                        java.util.stream.Collectors.counting()
                    ));
                result.put("statusCount", statusCount);
            }

            return result;
        } catch (Exception e) {
            log.error("查询项目失败", e);
            return Map.of("success", false, "message", "查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取项目统计信息
     */
    private Object getProjectStats() {
        try {
            Map<String, Object> stats = projectService.getProjectStats();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("stats", stats);
            result.put("message", "获取统计信息成功");
            
            return result;
        } catch (Exception e) {
            log.error("获取项目统计失败", e);
            return Map.of("success", false, "message", "获取统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析项目状态
     */
    private Object analyzeProjectStatus(Map<String, Object> parameters) {
        try {
            // 获取所有项目的统计信息
            Map<String, Object> stats = projectService.getProjectStats();
            
            // 简单的状态分析
            Map<String, Object> analysis = new HashMap<>();
            analysis.put("总项目数", stats.get("totalProjects"));
            analysis.put("进行中项目", stats.get("activeProjects"));
            analysis.put("已完成项目", stats.get("completedProjects"));
            analysis.put("风险项目", stats.get("riskProjects"));
            
            // 添加分析建议
            StringBuilder suggestions = new StringBuilder();
            Integer activeProjects = (Integer) stats.getOrDefault("activeProjects", 0);
            Integer riskProjects = (Integer) stats.getOrDefault("riskProjects", 0);
            
            if (riskProjects > 0) {
                suggestions.append("发现 ").append(riskProjects).append(" 个风险项目，建议及时关注和处理。");
            }
            if (activeProjects > 10) {
                suggestions.append("当前进行中项目较多，建议合理分配资源。");
            }
            
            analysis.put("建议", suggestions.toString());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("analysis", analysis);
            result.put("message", "项目状态分析完成");
            
            return result;
        } catch (Exception e) {
            log.error("分析项目状态失败", e);
            return Map.of("success", false, "message", "分析失败: " + e.getMessage());
        }
    }

    /**
     * 查询全部项目
     */
    private List<Project> queryAllProjects() {
        log.info("查询全部项目");
        return projectService.list();
    }

    /**
     * 按ID查询项目
     */
    private List<Project> queryProjectById(Map<String, Object> parameters) {
        Long projectId = Long.valueOf(parameters.get("projectId").toString());
        log.info("按ID查询项目: projectId={}", projectId);

        Project project = projectService.getById(projectId);
        if (project != null) {
            return List.of(project);
        }
        return List.of();
    }

    /**
     * 按名称查询项目
     */
    private List<Project> queryProjectByName(Map<String, Object> parameters) {
        String projectName = (String) parameters.get("projectName");
        log.info("按名称查询项目: projectName={}", projectName);

        // 支持模糊查询
        LambdaQueryWrapper<Project> qw = new LambdaQueryWrapper<Project>().like(Project::getName, "%" + projectName + "%")
                .or().like(Project::getDescription, "%" + projectName + "%");
        return projectService.list(qw);
    }

    /**
     * 按状态查询项目
     */
    private List<Project> queryProjectByStatus(Map<String, Object> parameters) {
        String status = (String) parameters.get("status");
        log.info("按状态查询项目: status={}", status);

        // 将状态字符串转换为对应的状态值
        LambdaQueryWrapper<Project> qw = new LambdaQueryWrapper<Project>().like(Project::getStatus, "%" + status + "%");
        return projectService.list(qw);
    }

}
