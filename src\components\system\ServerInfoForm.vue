<template>
  <n-form :model="localValue" :rules="rules" :disabled="!isEditing">
    <n-divider title-placement="left">通用信息</n-divider>
    <div class="form-grid">
      <n-form-item label="环境平台" path="platform">
        <n-input v-model:value="localValue.platform" placeholder="请输入环境平台" />
      </n-form-item>
    </div>

    <n-divider title-placement="left">生产环境信息</n-divider>
    <div class="form-grid">
      <n-form-item label="数据库名称" path="prodDbName">
        <n-input v-model:value="localValue.prodDbName" placeholder="请输入数据库名称" />
      </n-form-item>
      <n-form-item label="数据库服务器" path="prodDbServer">
        <n-input v-model:value="localValue.prodDbServer" placeholder="请输入数据库服务器" />
      </n-form-item>
      <n-form-item label="应用服务器" path="prodAppServer">
        <n-input v-model:value="localValue.prodAppServer" placeholder="请输入应用服务器" />
      </n-form-item>
      <n-form-item label="网站地址" path="prodWebsite">
        <n-input v-model:value="localValue.prodWebsite" placeholder="请输入网站地址" />
      </n-form-item>
      <n-form-item label="账号信息" path="prodAccountList">
        <n-dynamic-input
          v-model:value="localValue.prodAccountList"
          preset="pair"
          key-placeholder="账号"
          value-placeholder="密码"
          :disabled="!isEditing"
        />
      </n-form-item>
    </div>
    <n-divider title-placement="left">测试环境信息</n-divider>
    <div class="form-grid">
      <n-form-item label="数据库名称" path="qaDbName">
        <n-input v-model:value="localValue.qaDbName" placeholder="请输入数据库名称" />
      </n-form-item>
      <n-form-item label="数据库服务器" path="qaDbServer">
        <n-input v-model:value="localValue.qaDbServer" placeholder="请输入数据库服务器" />
      </n-form-item>
      <n-form-item label="应用服务器" path="qaAppServer">
        <n-input v-model:value="localValue.qaAppServer" placeholder="请输入应用服务器" />
      </n-form-item>
      <n-form-item label="网站地址" path="qaWebsite">
        <n-input v-model:value="localValue.qaWebsite" placeholder="请输入网站地址" />
      </n-form-item>
      <n-form-item label="账号信息" path="qaAccountList">
        <n-dynamic-input
          v-model:value="localValue.qaAccountList"
          preset="pair"
          key-placeholder="账号"
          value-placeholder="密码"
          :disabled="!isEditing"
        />
      </n-form-item>
    </div>
  </n-form>
</template>

<script setup lang="ts">
// 类型导入（放在第三方库导入后）
import type { FormRules } from 'naive-ui'

// 1. 类型定义
interface Props {
  /** 表单数据，v-model 绑定 */
  modelValue: Record<string, any>
  /** 是否可编辑 */
  isEditing?: boolean
}

// 2. Props 和 Emits 定义
const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

// 3. 响应式数据（v-model 双向绑定）
const localValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 4. 服务器信息验证规则
const rules: FormRules = {
  // 服务器信息验证规则
  prodAppServer: [
    { required: false, message: '应用服务器不能为空', trigger: ['blur', 'input'] }
  ],
  prodDbName: [
    { required: false, message: '数据库名称不能为空', trigger: ['blur', 'input'] }
  ],
  prodDbServer: [
    { required: false, message: '数据库服务器不能为空', trigger: ['blur', 'input'] }
  ],
  prodWebsite: [
    { required: false, message: '网站地址不能为空', trigger: ['blur', 'input'] }
  ],
  qaAppServer: [
    { required: false, message: '应用服务器不能为空', trigger: ['blur', 'input'] }
  ],
  qaDbName: [
    { required: false, message: '数据库名称不能为空', trigger: ['blur', 'input'] }
  ],
  qaDbServer: [
    { required: false, message: '数据库服务器不能为空', trigger: ['blur', 'input'] }
  ],
  platform: [
    { required: false, message: '平台不能为空', trigger: ['blur', 'input'] }
  ],
  qaWebsite: [
    { required: false, message: '网站地址不能为空', trigger: ['blur', 'input'] }
  ]
}
</script>

<style scoped>
.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}
</style>