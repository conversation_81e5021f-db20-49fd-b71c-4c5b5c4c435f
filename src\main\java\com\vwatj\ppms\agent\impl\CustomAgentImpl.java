package com.vwatj.ppms.agent.impl;

import com.alibaba.fastjson2.JSON;
import com.vwatj.ppms.agent.CustomAgent;
import com.vwatj.ppms.agent.function.BusinessFunctionRegistry;
import com.vwatj.ppms.agent.intent.IntentRecognitionService;
import com.vwatj.ppms.agent.intent.IntentResult;
import com.vwatj.ppms.dto.ChatRequestDTO;
import com.vwatj.ppms.dto.ChatResponseDTO;
import com.vwatj.ppms.entity.Agent;
import com.vwatj.ppms.enums.AgentTypeEnum;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.ai.deepseek.DeepSeekChatOptions;
import org.springframework.ai.deepseek.api.DeepSeekApi;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义Agent实现类
 * 基于Spring AI集成DeepSeek V3和业务功能
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Component
public class CustomAgentImpl implements CustomAgent {

    @Resource
    private DeepSeekChatModel chatModel;

    @Resource
    private BusinessFunctionRegistry functionRegistry;

    @Resource
    private IntentRecognitionService intentRecognitionService;

    // Agent实例缓存
    private final Map<Long, AgentInstance> agentInstances = new ConcurrentHashMap<>();

    @Override
    public String getAgentType() {
        return AgentTypeEnum.PRESET.getCode();
    }

    @Override
    public String getAgentName() {
        return "智能业务助手";
    }

    @Override
    public String getAgentDescription() {
        return "基于Spring AI和DeepSeek V3的智能业务助手，集成项目管理、任务管理等业务功能";
    }

    @Override
    public void initialize(Agent agent) {
        try {
            log.info("初始化自定义Agent: agentId={}, name={}", agent.getId(), agent.getName());

            // 创建聊天客户端
            ChatClient chatClient = ChatClient.builder(chatModel).build();

            // 创建Agent实例
            AgentInstance instance = new AgentInstance();
            instance.agent = agent;
            instance.chatClient = chatClient;
            instance.chatModel = chatModel;
            instance.initialized = true;
            instance.initTime = LocalDateTime.now();

            agentInstances.put(agent.getId(), instance);

            log.info("自定义Agent初始化成功: agentId={}", agent.getId());

        } catch (Exception e) {
            log.error("初始化自定义Agent失败: agentId={}", agent.getId(), e);
            throw new RuntimeException("初始化Agent失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ChatResponseDTO chat(ChatRequestDTO request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("处理自定义Agent聊天请求: agentId={}, sessionId={}",
                    request.getAgentId(), request.getSessionId());

            AgentInstance instance = agentInstances.get(request.getAgentId());
            if (instance == null || !instance.initialized) {
                return buildErrorResponse(request, "Agent未初始化", startTime);
            }

            // 构建消息列表
            List<Message> messages = buildMessages(instance.agent, request);

            // 创建Prompt
            Prompt prompt = new Prompt(messages);

            // 调用聊天模型
            ChatResponse chatResponse = instance.chatModel.call(prompt);

            // 构建响应
            ChatResponseDTO response = buildSuccessResponse(request, chatResponse, startTime);

            // 处理Function Calling
            if (request.getEnableFunctionCalling()) {
                handleFunctionCalling(instance.agent, request, response);
            }

            log.info("自定义Agent聊天请求处理完成: agentId={}, responseTime={}ms",
                    request.getAgentId(), response.getResponseTime());

            return response;

        } catch (Exception e) {
            log.error("处理自定义Agent聊天请求失败: agentId={}", request.getAgentId(), e);
            return buildErrorResponse(request, "处理请求失败: " + e.getMessage(), startTime);
        }
    }

    @Override
    public String streamChat(ChatRequestDTO request) {
        try {
            log.info("处理自定义Agent流式聊天请求: agentId={}, sessionId={}, enableFunctionCalling={}",
                    request.getAgentId(), request.getSessionId(), request.getEnableFunctionCalling());

            AgentInstance instance = agentInstances.get(request.getAgentId());
            if (instance == null || !instance.initialized) {
                return "错误：Agent未初始化";
            }

            // 构建消息列表
            List<Message> messages = buildMessages(instance.agent, request);

            // 使用ChatClient进行流式调用
            String response = instance.chatClient.prompt()
                    .messages(messages)
                    .call()
                    .content();

            // 处理Function Calling（如果启用）
            if (request.getEnableFunctionCalling()) {
                log.info("streamChat中启用了Function Calling，开始处理");

                // 创建临时的ChatResponseDTO来处理Function Calling
                ChatResponseDTO tempResponse = new ChatResponseDTO();
                tempResponse.setMessage(response);
                tempResponse.setSessionId(request.getSessionId());
                tempResponse.setAgentId(request.getAgentId());

                // 处理Function Calling
                handleFunctionCalling(instance.agent, request, tempResponse);

                // 如果有功能调用，将结果添加到响应中
                if (tempResponse.getFunctionCalled() && tempResponse.getFunctionCalls() != null) {
                    StringBuilder responseBuilder = new StringBuilder(response);
                    responseBuilder.append("\n\n📋 **功能调用结果：**\n");

                    for (ChatResponseDTO.FunctionCallDTO call : tempResponse.getFunctionCalls()) {
                        responseBuilder.append("- **").append(call.getFunctionName()).append("**: ");
                        if (call.getSuccess()) {
                            responseBuilder.append("✅ 调用成功\n");
                            if (call.getResult() != null) {
                                responseBuilder.append("  结果: ").append(call.getResult().toString()).append("\n");
                            }
                        } else {
                            responseBuilder.append("❌ 调用失败\n");
                            if (call.getError() != null) {
                                responseBuilder.append("  错误: ").append(call.getError()).append("\n");
                            }
                        }
                    }

                    response = responseBuilder.toString();
                    log.info("streamChat Function Calling处理完成，调用了{}个功能", tempResponse.getFunctionCalls().size());
                } else {
                    log.info("streamChat中没有触发任何功能调用");
                }
            }

            return response;

        } catch (Exception e) {
            log.error("处理自定义Agent流式聊天请求失败: agentId={}", request.getAgentId(), e);
            return "错误：处理请求失败 - " + e.getMessage();
        }
    }

    @Override
    public List<String> getSupportedFunctions() {
        return functionRegistry.getAllFunctionNames();
    }

    @Override
    public Object executeFunction(String functionName, Map<String, Object> parameters) {
        try {
            log.info("执行业务功能: functionName={}, parameters={}", functionName, parameters);
            Object result = functionRegistry.executeFunction(functionName, parameters);
            log.info("业务功能执行成功: functionName={}, result={}", functionName, result);
            return result;
        } catch (Exception e) {
            log.error("执行业务功能失败: functionName={}, parameters={}", functionName, parameters, e);
            return Map.of("error", "功能执行失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateConfig(Agent agent) {
        try {
            if (agent.getLlmApiKey() == null || agent.getLlmApiKey().isEmpty()) {
                return false;
            }
            if (agent.getLlmApiUrl() == null || agent.getLlmApiUrl().isEmpty()) {
                return false;
            }
            if (agent.getLlmModel() == null || agent.getLlmModel().isEmpty()) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("验证自定义Agent配置失败: agentId={}", agent.getId(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("type", getAgentType());
        status.put("name", getAgentName());
        status.put("description", getAgentDescription());
        status.put("activeInstances", agentInstances.size());
        status.put("supportedFunctions", getSupportedFunctions());
        return status;
    }

    @Override
    public void destroy() {
        log.info("销毁自定义Agent实例，当前实例数: {}", agentInstances.size());
        agentInstances.clear();
    }

    /**
     * 构建消息列表
     */
    private List<Message> buildMessages(Agent agent, ChatRequestDTO request) {
        List<Message> messages = new ArrayList<>();

        // 添加系统消息
        String systemPrompt = buildSystemPrompt(agent);
        messages.add(new SystemMessage(systemPrompt));

        // 添加历史消息
        if (request.getHistory() != null) {
            for (ChatRequestDTO.ChatMessageDTO historyMsg : request.getHistory()) {
                if ("user".equals(historyMsg.getRole())) {
                    messages.add(new UserMessage(historyMsg.getContent()));
                } else if ("assistant".equals(historyMsg.getRole())) {
                    // Spring AI中assistant消息需要特殊处理
                    messages.add(new SystemMessage("Assistant: " + historyMsg.getContent()));
                }
            }
        }

        // 添加用户消息
        String userPrompt = buildUserPrompt(agent, request);
        messages.add(new UserMessage(userPrompt));

        return messages;
    }

    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(Agent agent) {
        StringBuilder prompt = new StringBuilder();

        if (agent.getSystemPrompt() != null && !agent.getSystemPrompt().isEmpty()) {
            prompt.append(agent.getSystemPrompt());
        } else {
            prompt.append("你是一个智能业务助手，专门帮助用户处理项目管理相关的任务。");
        }

        // 添加功能说明
        prompt.append("\n\n你可以调用以下业务功能：\n");
        Map<String, Map<String, Object>> schemas = functionRegistry.getAllFunctionSchemas();
        for (Map.Entry<String, Map<String, Object>> entry : schemas.entrySet()) {
            Map<String, Object> schema = entry.getValue();
            prompt.append("- ").append(schema.get("name")).append(": ").append(schema.get("description")).append("\n");
        }

        prompt.append("\n请根据用户的需求，智能地调用相应的功能来帮助用户完成任务。");

        return prompt.toString();
    }

    /**
     * 构建用户提示词
     */
    private String buildUserPrompt(Agent agent, ChatRequestDTO request) {
        if (agent.getUserPromptTemplate() != null && !agent.getUserPromptTemplate().isEmpty()) {
            return agent.getUserPromptTemplate().replace("{message}", request.getMessage());
        }
        return request.getMessage();
    }

    /**
     * 处理Function Calling - 完全基于LLM意图识别
     */
    private void handleFunctionCalling(Agent agent, ChatRequestDTO request, ChatResponseDTO response) {
        try {
            log.info("开始LLM意图识别和功能调用: agentId={}, message={}", agent.getId(), request.getMessage());

            List<ChatResponseDTO.FunctionCallDTO> functionCalls = new ArrayList<>();

            // 使用LLM进行意图识别和参数提取
            IntentResult intentResult = intentRecognitionService.recognizeIntent(request.getMessage());

            if (intentResult.isValid()) {
                log.info("LLM意图识别成功: intent={}, action={}, confidence={}, reasoning={}",
                        intentResult.getIntent(), intentResult.getAction(), intentResult.getConfidence(), intentResult.getReasoning());

                // 使用LLM识别的结果调用功能
                ChatResponseDTO.FunctionCallDTO functionCall = executeFunctionFromIntent(intentResult, request);
                if (functionCall != null) {
                    functionCalls.add(functionCall);
                }
            } else {
                log.info("LLM未识别到有效意图，不执行任何功能调用: confidence={}, reasoning={}",
                        intentResult.getConfidence(), intentResult.getReasoning());
                // 完全依赖LLM，不再使用硬编码规则
            }



            if (!functionCalls.isEmpty()) {
                response.setFunctionCalls(functionCalls);
                response.setFunctionCalled(true);
                log.info("Function Calling处理完成，调用了{}个功能", functionCalls.size());

                // 将功能调用结果添加到响应消息中
                StringBuilder messageBuilder = new StringBuilder(response.getMessage());
                messageBuilder.append("\n\n📋 **功能调用结果：**\n");
                for (ChatResponseDTO.FunctionCallDTO call : functionCalls) {
                    messageBuilder.append("- **").append(call.getFunctionName()).append("**: ");
                    if (call.getSuccess()) {
                        messageBuilder.append("✅ 调用成功\n");
                        if (call.getResult() != null) {
                            messageBuilder.append("  结果: ").append(call.getResult().toString()).append("\n");
                        }
                    } else {
                        messageBuilder.append("❌ 调用失败\n");
                        if (call.getError() != null) {
                            messageBuilder.append("  错误: ").append(call.getError()).append("\n");
                        }
                    }
                }
                response.setMessage(messageBuilder.toString());
            } else {
                log.info("没有触发任何功能调用");
            }

        } catch (Exception e) {
            log.error("处理Function Calling失败: agentId={}", agent.getId(), e);
        }
    }

    /**
     * 根据LLM意图识别结果执行功能
     */
    private ChatResponseDTO.FunctionCallDTO executeFunctionFromIntent(IntentResult intentResult, ChatRequestDTO request) {
        try {
            String functionName = intentResult.getIntent();
            Map<String, Object> parameters = new HashMap<>(intentResult.getParameters());
            parameters.put("action", intentResult.getAction());

            // 添加用户信息
            if (request.getUserId() != null && intentResult.getAction().equals("get_my_tasks")) {
                parameters.put("assignee", request.getUserId());
            }

            log.info("执行LLM识别的功能: functionName={}, parameters={}", functionName, parameters);
            Object result = executeFunction(functionName, parameters);

            ChatResponseDTO.FunctionCallDTO functionCall = new ChatResponseDTO.FunctionCallDTO();
            functionCall.setFunctionName(functionName);
            functionCall.setParameters(parameters);
            functionCall.setSuccess(true);
            functionCall.setResult(result);

            return functionCall;

        } catch (Exception e) {
            log.error("执行LLM识别的功能失败: {}", e.getMessage(), e);

            ChatResponseDTO.FunctionCallDTO errorCall = new ChatResponseDTO.FunctionCallDTO();
            errorCall.setFunctionName(intentResult.getIntent());
            errorCall.setParameters(intentResult.getParameters());
            errorCall.setSuccess(false);
            errorCall.setError("功能执行失败: " + e.getMessage());

            return errorCall;
        }
    }













    /**
     * 构建成功响应
     */
    private ChatResponseDTO buildSuccessResponse(ChatRequestDTO request, ChatResponse chatResponse, long startTime) {
        ChatResponseDTO response = new ChatResponseDTO();
        response.setSessionId(request.getSessionId());
        response.setAgentId(request.getAgentId());
        response.setMessage(chatResponse.getResult().getOutput().getText());
        response.setSuccess(true);
        response.setResponseTime(System.currentTimeMillis() - startTime);
        response.setTimestamp(new Date().getTime());

        // 设置token使用量
        if (chatResponse.getMetadata() != null && chatResponse.getMetadata().getUsage() != null) {
            response.setTokensUsed(chatResponse.getMetadata().getUsage().getTotalTokens());
        }

        return response;
    }

    /**
     * 构建错误响应
     */
    private ChatResponseDTO buildErrorResponse(ChatRequestDTO request, String errorMessage, long startTime) {
        ChatResponseDTO response = new ChatResponseDTO();
        response.setSessionId(request.getSessionId());
        response.setAgentId(request.getAgentId());
        response.setSuccess(true);
        response.setMessage("抱歉，处理您的请求时遇到了问题：" + errorMessage);
        response.setResponseTime(System.currentTimeMillis() - startTime);
        response.setTimestamp(new Date().getTime());
        return response;
    }

    /**
     * Agent实例内部类
     */
    private static class AgentInstance {
        Agent agent;
        ChatClient chatClient;
        DeepSeekChatModel chatModel;
        boolean initialized;
        LocalDateTime initTime;
    }
}
