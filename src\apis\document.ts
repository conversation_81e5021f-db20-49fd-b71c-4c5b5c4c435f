// 文档相关 API 接口
import { httpClient, buildPaginationParams } from './index'
import type {
  Document,
  DocumentStatus,
  DocumentCategory,
  DocumentFileType,
  ApiResponse,
  PaginationParams,
  PaginationResponse
} from '@/types'

// 文档查询参数
export interface DocumentQueryParams extends PaginationParams {
  keyword?: string
  projectId?: number | string
  category?: DocumentCategory
  status?: DocumentStatus
  fileType?: DocumentFileType
  author?: string
  reviewer?: string
  approver?: string
  tags?: string[]
  uploadDateStart?: number
  uploadDateEnd?: number
}

// 文档上传参数
export interface UploadDocumentParams {
  file: File
  projectId: number | string
  category: DocumentCategory
  description?: string
  tags?: string[]
  isPublic?: boolean
  viewers?: string[]
  editors?: string[]
}

// 文档更新参数
export interface UpdateDocumentParams {
  id: string
  title?: string
  description?: string
  category?: DocumentCategory
  tags?: string[]
  isPublic?: boolean
  viewers?: string[]
  editors?: string[]
}

// 文档审核参数
export interface ReviewDocumentParams {
  id: string
  action: 'approve' | 'reject'
  comments?: string
}

// 文档统计信息
export interface DocumentStats {
  total: number
  byStatus: Record<DocumentStatus, number>
  byCategory: Record<DocumentCategory, number>
  byFileType: Record<DocumentFileType, number>
  totalSize: number
  totalViews: number
  totalDownloads: number
}

// 文档 API 类
export class DocumentApi {
  private static readonly BASE_PATH = '/documents'

  /**
   * 获取文档列表
   */
  static async getDocuments(params?: DocumentQueryParams): Promise<ApiResponse<PaginationResponse<Document>>> {
    const queryParams = params ? {
      ...buildPaginationParams(params),
      keyword: params.keyword,
      projectId: params.projectId,
      category: params.category,
      status: params.status,
      fileType: params.fileType,
      author: params.author,
      reviewer: params.reviewer,
      approver: params.approver,
      tags: params.tags?.join(','),
      uploadDateStart: params.uploadDateStart,
      uploadDateEnd: params.uploadDateEnd,
    } : undefined

    return httpClient.get<PaginationResponse<Document>>(this.BASE_PATH, queryParams)
  }

  /**
   * 获取项目文档列表
   */
  static async getProjectDocuments(projectId: number | string, params?: Omit<DocumentQueryParams, 'projectId'>): Promise<ApiResponse<PaginationResponse<Document>>> {
    const queryParams = params ? {
      ...buildPaginationParams(params),
      keyword: params.keyword,
      category: params.category,
      status: params.status,
      fileType: params.fileType,
      author: params.author,
      reviewer: params.reviewer,
      approver: params.approver,
      tags: params.tags?.join(','),
      uploadDateStart: params.uploadDateStart,
      uploadDateEnd: params.uploadDateEnd,
    } : undefined

    return httpClient.get<PaginationResponse<Document>>(`/projects/${projectId}/documents`, queryParams)
  }

  /**
   * 获取文档详情
   */
  static async getDocument(id: string): Promise<ApiResponse<Document>> {
    return httpClient.get<Document>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 上传文档
   */
  static async uploadDocument(params: UploadDocumentParams): Promise<ApiResponse<Document>> {
    const { file, ...data } = params
    return httpClient.upload<Document>(this.BASE_PATH, file, data)
  }

  /**
   * 更新文档信息
   */
  static async updateDocument(params: UpdateDocumentParams): Promise<ApiResponse<Document>> {
    const { id, ...data } = params
    return httpClient.put<Document>(`${this.BASE_PATH}/${id}`, data)
  }

  /**
   * 删除文档
   */
  static async deleteDocument(id: string): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 下载文档
   */
  static async downloadDocument(id: string): Promise<ApiResponse<{ downloadUrl: string }>> {
    return httpClient.get<{ downloadUrl: string }>(`${this.BASE_PATH}/${id}/download`)
  }

  /**
   * 预览文档
   */
  static async previewDocument(id: string): Promise<ApiResponse<{ previewUrl: string }>> {
    return httpClient.get<{ previewUrl: string }>(`${this.BASE_PATH}/${id}/preview`)
  }

  /**
   * 审核文档
   */
  static async reviewDocument(params: ReviewDocumentParams): Promise<ApiResponse<Document>> {
    const { id, ...data } = params
    return httpClient.patch<Document>(`${this.BASE_PATH}/${id}/review`, data)
  }

  /**
   * 发布文档
   */
  static async publishDocument(id: string): Promise<ApiResponse<Document>> {
    return httpClient.patch<Document>(`${this.BASE_PATH}/${id}/publish`)
  }

  /**
   * 归档文档
   */
  static async archiveDocument(id: string): Promise<ApiResponse<Document>> {
    return httpClient.patch<Document>(`${this.BASE_PATH}/${id}/archive`)
  }

  /**
   * 获取文档版本历史
   */
  static async getDocumentVersions(id: string): Promise<ApiResponse<Array<{
    version: string
    uploadTime: number
    uploader: string
    uploaderName: string
    fileSize: number
    changes?: string
  }>>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/versions`)
  }

  /**
   * 获取文档统计信息
   */
  static async getDocumentStats(projectId?: number | string): Promise<ApiResponse<DocumentStats>> {
    const params = projectId ? { projectId } : undefined
    return httpClient.get<DocumentStats>(`${this.BASE_PATH}/stats`, params)
  }

  /**
   * 批量删除文档
   */
  static async batchDeleteDocuments(ids: string[]): Promise<ApiResponse<void>> {
    return httpClient.post<void>(`${this.BASE_PATH}/batch/delete`, { ids })
  }

  /**
   * 复制文档
   */
  static async duplicateDocument(id: string, title: string, projectId?: number | string): Promise<ApiResponse<Document>> {
    return httpClient.post<Document>(`${this.BASE_PATH}/${id}/duplicate`, { title, projectId })
  }

  /**
   * 移动文档到其他项目
   */
  static async moveDocument(id: string, targetProjectId: number | string): Promise<ApiResponse<Document>> {
    return httpClient.patch<Document>(`${this.BASE_PATH}/${id}/move`, { projectId: targetProjectId })
  }

  /**
   * 获取文档访问记录
   */
  static async getDocumentAccessLog(id: string, params?: PaginationParams): Promise<ApiResponse<PaginationResponse<{
    id: string
    userId: string
    userName: string
    action: 'view' | 'download' | 'edit'
    timestamp: number
    ipAddress?: string
  }>>> {
    const queryParams = params ? buildPaginationParams(params) : undefined
    return httpClient.get(`${this.BASE_PATH}/${id}/access-log`, queryParams)
  }
}

// 导出默认实例
export const documentApi = DocumentApi
