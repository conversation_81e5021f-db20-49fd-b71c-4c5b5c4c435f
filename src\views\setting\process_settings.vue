<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import {
  NCard,
  NDataTable,
  NButton,
  NSpace,
  NInput,
  NSelect,
  NModal,
  NForm,
  NFormItem,
  NUpload,
  NUploadDragger,
  NText,
  NIcon,
  NTag,
  NPopconfirm,
  NTooltip,
  NDrawer,
  NDrawerContent,
  NBadge,
  useMessage,
  type DataTableColumns,
  type UploadFileInfo
} from 'naive-ui'
import {
  CloudUploadOutlined,
  InboxOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  RocketOutlined
} from '@vicons/antd'
import PageLayout from '@/layouts/PageLayout.vue'
import { processDefinitionApi, processVersionApi } from '@/apis'
import type {
  ProcessDefinition,
  ProcessVersion,
  ProcessDefinitionQuery,
  ProcessVersionQuery,
  CreateProcessDefinitionParams,
  UpdateProcessDefinitionParams,
  ProcessDefinitionStatus
} from '@/types'

// hooks
const message = useMessage()

// 响应式数据
const loading = ref(false)
const data = ref<ProcessDefinition[]>([])
const total = ref(0)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showVersionDrawer = ref(false)
const showPublishModal = ref(false)
const currentProcess = ref<ProcessDefinition | null>(null)
const versionData = ref<ProcessVersion[]>([])
const versionLoading = ref(false)

// 查询参数
const queryParams = reactive<ProcessDefinitionQuery>({
  page: 1,
  pageSize: 10,
  name: '',
  processKey: '',
  status: undefined,
  createdBy: ''
})

// 版本查询参数
const versionQueryParams = reactive<ProcessVersionQuery>({
  page: 1,
  pageSize: 10,
  processDefinitionId: undefined,
  version: undefined,
  status: undefined,
  createdBy: ''
})

// 表单数据
const createForm = reactive<CreateProcessDefinitionParams>({
  processKey: '',
  name: '',
  description: '',
  versionDescription: ''
})

const editForm = reactive<UpdateProcessDefinitionParams>({
  id: 0,
  name: '',
  description: ''
})

// 文件状态
const createFormFile = ref<File | null>(null)
const publishFormFile = ref<File | null>(null)

const publishForm = reactive({
  processDefinitionId: 0,
  description: ''
})

// 文件上传
const fileList = ref<UploadFileInfo[]>([])
const publishFileList = ref<UploadFileInfo[]>([])

// 状态选项
const statusOptions = [
  { label: '全部', value: undefined },
  { label: '草稿', value: 'DRAFT' },
  { label: '已发布', value: 'PUBLISHED' },
  { label: '已挂起', value: 'SUSPENDED' }
]

// 生命周期
onMounted(() => {
  loadData()
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await processDefinitionApi.getProcessDefinitions(queryParams)
    data.value = response.data.data
    total.value = response.data.total
  } catch (error) {
    console.error('加载流程定义失败:', error)
    message.error('加载流程定义失败')
  } finally {
    loading.value = false
  }
}

const loadVersions = async (processDefinitionId: number) => {
  versionLoading.value = true
  try {
    versionQueryParams.processDefinitionId = processDefinitionId
    const response = await processVersionApi.getProcessVersions(versionQueryParams)
    versionData.value = response.data.data
  } catch (error) {
    console.error('加载版本列表失败:', error)
    message.error('加载版本列表失败')
  } finally {
    versionLoading.value = false
  }
}

const handleSearch = () => {
  queryParams.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(queryParams, {
    page: 1,
    pageSize: 10,
    name: '',
    processKey: '',
    status: undefined,
    createdBy: ''
  })
  loadData()
}

const handleCreate = () => {
  Object.assign(createForm, {
    processKey: '',
    name: '',
    description: '',
    versionDescription: ''
  })
  createFormFile.value = null
  fileList.value = []
  showCreateModal.value = true
}

const handleEdit = (row: ProcessDefinition) => {
  currentProcess.value = row
  Object.assign(editForm, {
    id: row.id,
    name: row.name,
    description: row.description || ''
  })
  showEditModal.value = true
}

const handleViewVersions = async (row: ProcessDefinition) => {
  currentProcess.value = row
  await loadVersions(row.id)
  showVersionDrawer.value = true
}



const handlePublish = async (processOrVersion: ProcessDefinition | ProcessVersion) => {
  console.log('handlePublish 被调用，参数:', processOrVersion)

  let targetProcess: ProcessDefinition

  // 判断传入的是流程定义还是版本
  if ('processKey' in processOrVersion) {
    // 是流程定义
    targetProcess = processOrVersion as ProcessDefinition
    currentProcess.value = targetProcess
  } else {
    // 是版本，使用当前流程
    if (!currentProcess.value) return
    targetProcess = currentProcess.value
  }

  // 重置发布表单
  Object.assign(publishForm, {
    processDefinitionId: Number(targetProcess.id),
    description: ''
  })

  publishFormFile.value = null
  publishFileList.value = []

  console.log('发布表单设置完成:', publishForm)
  showPublishModal.value = true
}





const handleDeleteVersion = async (version: ProcessVersion) => {
  try {
    await processVersionApi.deleteProcessVersion(version.id)
    message.success('版本删除成功')
    if (currentProcess.value) {
      await loadVersions(currentProcess.value.id)
    }
  } catch (error) {
    console.error('删除版本失败:', error)
    message.error('删除版本失败')
  }
}

const handleDownloadVersion = async (version: ProcessVersion) => {
  try {
    const blob = await processVersionApi.downloadBpmnFile(version.id)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = version.fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    message.success('文件下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    message.error('下载文件失败')
  }
}

// 文件上传处理
const handleFileChange = (data: { fileList: UploadFileInfo[] }) => {
  console.log('文件变更1:', data.fileList) // 调试日志
  if (data.fileList.length > 0 && data.fileList[0].file) {
    createFormFile.value = data.fileList[0].file
    console.log('设置文件:', data.fileList[0].file) // 调试日志
  } else {
    createFormFile.value = null
    console.log('清空文件') // 调试日志
  }
}

const handlePublishFileChange = (data: { fileList: UploadFileInfo[] }) => {
  if (data.fileList.length > 0 && data.fileList[0].file) {
    publishFormFile.value = data.fileList[0].file
  } else {
    publishFormFile.value = null
  }
}

// 表单提交
const handleCreateSubmit = async () => {
  if (!createForm.processKey || !createForm.name || !createFormFile.value) {
    message.error('请填写完整信息并上传BPMN文件')
    return
  }

  console.log('创建流程参数:', createForm, '文件:', createFormFile.value) // 调试日志

  try {
    console.log('开始创建流程定义...', { file: createFormFile.value, params: createForm })
    const result = await processDefinitionApi.createProcessDefinition(createFormFile.value, createForm)
    console.log('创建流程定义成功:', result)

    message.success('流程定义创建并发布成功')
    showCreateModal.value = false
    loadData()
  } catch (error) {
    console.error('创建流程定义失败:', error)
    console.error('错误详情:', {
      message: (error as any)?.message,
      status: (error as any)?.status,
      data: (error as any)?.data,
      response: (error as any)?.response
    })
    message.error(`创建流程定义失败: ${(error as any)?.message || (error as any)?.data?.message || error}`)
  }
}

const handleEditSubmit = async () => {
  if (!editForm.name) {
    message.error('请填写流程名称')
    return
  }

  try {
    await processDefinitionApi.updateProcessDefinition(editForm)
    message.success('流程定义更新成功')
    showEditModal.value = false
    loadData()
  } catch (error) {
    console.error('更新流程定义失败:', error)
    message.error('更新流程定义失败')
  }
}



const handlePublishSubmit = async () => {
  console.log('发布提交前检查:', {
    publishForm: publishForm,
    processDefinitionId: publishForm.processDefinitionId,
    file: publishFormFile.value,
    currentProcess: currentProcess.value
  })

  if (!publishForm.processDefinitionId) {
    message.error('流程定义ID不能为空')
    return
  }

  if (!publishFormFile.value) {
    message.error('请上传BPMN文件')
    return
  }

  try {
    console.log('开始发布流程（创建新版本并发布）:', publishForm)

    // 直接调用合并后的发布接口
    await processDefinitionApi.publishProcessWithNewVersion(publishFormFile.value, publishForm)
    message.success('流程发布成功')
    showPublishModal.value = false
    loadData()
    if (currentProcess.value) {
      await loadVersions(currentProcess.value.id)
    }
  } catch (error) {
    console.error('发布流程失败:', error)
    console.error('错误详情:', {
      message: (error as any)?.message,
      data: (error as any)?.data,
      response: (error as any)?.response
    })
    message.error(`发布流程失败: ${(error as any)?.message || (error as any)?.data?.message || error}`)
  }
}

// 版本激活/挂起处理
const handleActivateVersion = async (version: ProcessVersion) => {
  try {
    await processVersionApi.activateVersion(version.id)
    message.success('版本激活成功')
    if (currentProcess.value) {
      await loadVersions(currentProcess.value.id)
    }
  } catch (error) {
    console.error('激活版本失败:', error)
    message.error(`激活版本失败: ${(error as any)?.message || error}`)
  }
}

const handleSuspendVersion = async (version: ProcessVersion) => {
  try {
    await processVersionApi.suspendVersion(version.id)
    message.success('版本挂起成功')
    if (currentProcess.value) {
      await loadVersions(currentProcess.value.id)
    }
  } catch (error) {
    console.error('挂起版本失败:', error)
    message.error(`挂起版本失败: ${(error as any)?.message || error}`)
  }
}

// 分页处理
const handlePageChange = (page: number) => {
  queryParams.page = page
  loadData()
}

const handlePageSizeChange = (pageSize: number) => {
  queryParams.pageSize = pageSize
  queryParams.page = 1
  loadData()
}

// 获取状态标签类型
const getStatusTagType = (status: ProcessDefinitionStatus) => {
  switch (status) {
    case 'DRAFT':
      return 'default'
    case 'PUBLISHED':
      return 'success'
    case 'SUSPENDED':
      return 'warning'
    default:
      return 'default'
  }
}

// 获取状态文本
const getStatusText = (status: ProcessDefinitionStatus) => {
  switch (status) {
    case 'DRAFT':
      return '草稿'
    case 'PUBLISHED':
      return '已发布'
    case 'SUSPENDED':
      return '已挂起'
    default:
      return '未知'
  }
}

// 表格列配置
const columns: DataTableColumns<ProcessDefinition> = [
  {
    title: '流程名称',
    key: 'name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '流程Key',
    key: 'processKey',
    width: 150
  },
  {
    title: '当前版本',
    key: 'publishedVersion',
    width: 100,
    align: 'center',
    render: (row) => {
      return row.publishedVersion ? `v${row.publishedVersion}` : '-'
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render: (row) => {
      return h(NTag, {
        type: getStatusTagType(row.status as ProcessDefinitionStatus)
      }, {
        default: () => getStatusText(row.status as ProcessDefinitionStatus)
      })
    }
  },

  {
    title: '创建时间',
    key: 'createdTime',
    width: 180,
    render: (row) => {
      return new Date(row.createdTime).toLocaleString()
    }
  },
  {
    title: '创建人',
    key: 'createdBy',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 300,
    align: 'center',
    render: (row) => {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'primary',
              ghost: true,
              onClick: () => handleViewVersions(row)
            }, {
              default: () => h(NIcon, null, { default: () => h(EyeOutlined) })
            }),
            default: () => '版本管理'
          }),
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'info',
              ghost: true,
              onClick: () => handleEdit(row)
            }, {
              default: () => h(NIcon, null, { default: () => h(EditOutlined) })
            }),
            default: () => '编辑'
          }),
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'success',
              ghost: true,
              onClick: () => handlePublish(row)
            }, {
              default: () => h(NIcon, null, { default: () => h(RocketOutlined) })
            }),
            default: () => '发布'
          })
        ].filter(Boolean)
      })
    }
  }
]

// 版本表格列配置
const versionColumns: DataTableColumns<ProcessVersion> = [
  {
    title: '版本',
    key: 'version',
    width: 80,
    align: 'center',
    render: (row) => {
      return h(NSpace, { size: 'small', align: 'center' }, {
        default: () => [
          h(NText, null, { default: () => `v${row.version}` }),
          row.isPublished ? h(NBadge, {
            dot: true,
            type: 'success'
          }) : null
        ]
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render: (row) => {
      return h(NTag, {
        type: getStatusTagType(row.status as ProcessDefinitionStatus)
      }, {
        default: () => getStatusText(row.status as ProcessDefinitionStatus)
      })
    }
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      return row.description || '-'
    }
  },
  {
    title: '文件名',
    key: 'fileName',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '创建时间',
    key: 'createdTime',
    width: 180,
    render: (row) => {
      return new Date(row.createdTime).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    render: (row) => {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NTooltip, { trigger: 'hover' }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'info',
              ghost: true,
              onClick: () => handleDownloadVersion(row)
            }, {
              default: () => h(NIcon, null, { default: () => h(DownloadOutlined) })
            }),
            default: () => '下载'
          })
        ].filter(Boolean)
      })
    }
  }
]
</script>

<template>
  <PageLayout title="流程管理">
    <template #header-extra>
      <NButton type="primary" @click="handleCreate">
        <template #icon>
          <NIcon><CloudUploadOutlined /></NIcon>
        </template>
        新建流程
      </NButton>
    </template>

    <!-- 搜索区域 -->
    <NCard class="mb-4">
      <NSpace :size="16" :wrap="false">
        <NInput
          v-model:value="queryParams.name"
          placeholder="流程名称"
          clearable
          style="width: 200px"
        />
        <NInput
          v-model:value="queryParams.processKey"
          placeholder="流程Key"
          clearable
          style="width: 200px"
        />
        <NSelect
          v-model:value="queryParams.status"
          placeholder="状态"
          :options="statusOptions"
          clearable
          style="width: 120px"
        />
        <NButton type="primary" @click="handleSearch">搜索</NButton>
        <NButton @click="handleReset">重置</NButton>
      </NSpace>
    </NCard>

    <!-- 数据表格 -->
    <NCard>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="{
          page: queryParams.page,
          pageSize: queryParams.pageSize,
          itemCount: total,
          showSizePicker: true,
          pageSizes: [10, 20, 50],
          onUpdatePage: handlePageChange,
          onUpdatePageSize: handlePageSizeChange
        }"
        :scroll-x="1200"
      />
    </NCard>

    <!-- 创建流程模态框 -->
    <NModal
      v-model:show="showCreateModal"
      preset="dialog"
      title="新建流程"
      style="width: 600px"
      :mask-closable="false"
    >
      <NForm :model="createForm" label-placement="left" label-width="100px">
        <NFormItem label="流程Key" required>
          <NInput v-model:value="createForm.processKey" placeholder="请输入流程Key" />
        </NFormItem>
        <NFormItem label="流程名称" required>
          <NInput v-model:value="createForm.name" placeholder="请输入流程名称" />
        </NFormItem>
        <NFormItem label="流程描述">
          <NInput
            v-model:value="createForm.description"
            type="textarea"
            placeholder="请输入流程描述"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="版本描述">
          <NInput
            v-model:value="createForm.versionDescription"
            type="textarea"
            placeholder="请输入版本描述"
            :rows="2"
          />
        </NFormItem>
        <NFormItem label="BPMN文件" required>
          <NUpload
            v-model:file-list="fileList"
            :max="1"
            accept=".bpmn"
            @change="handleFileChange"
          >
            <NUploadDragger>
              <div style="margin-bottom: 12px">
                <NIcon size="48" :depth="3">
                  <InboxOutlined />
                </NIcon>
              </div>
              <NText style="font-size: 16px">
                点击或者拖动文件到该区域来上传
              </NText>
              <NText depth="3" style="font-size: 12px">
                请上传.bpmn格式的流程定义文件
              </NText>
            </NUploadDragger>
          </NUpload>
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="showCreateModal = false">取消</NButton>
          <NButton type="primary" @click="handleCreateSubmit">确定</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 编辑流程模态框 -->
    <NModal
      v-model:show="showEditModal"
      preset="dialog"
      title="编辑流程"
      style="width: 600px"
      :mask-closable="false"
    >
      <NForm :model="editForm" label-placement="left" label-width="100px">
        <NFormItem label="流程名称" required>
          <NInput v-model:value="editForm.name" placeholder="请输入流程名称" />
        </NFormItem>
        <NFormItem label="流程描述">
          <NInput
            v-model:value="editForm.description"
            type="textarea"
            placeholder="请输入流程描述"
            :rows="3"
          />
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="showEditModal = false">取消</NButton>
          <NButton type="primary" @click="handleEditSubmit">确定</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 版本管理抽屉 -->
    <NDrawer
      v-model:show="showVersionDrawer"
      :width="800"
      placement="right"
    >
      <NDrawerContent :title="`${currentProcess?.name} - 版本管理`" closable>

        <NDataTable
          :columns="versionColumns"
          :data="versionData"
          :loading="versionLoading"
          :pagination="false"
          size="small"
        />
      </NDrawerContent>
    </NDrawer>



    <!-- 发布流程模态框 -->
    <NModal
      v-model:show="showPublishModal"
      preset="dialog"
      title="发布流程"
      style="width: 600px"
      :mask-closable="false"
    >
      <NForm :model="publishForm" label-placement="left" label-width="120px">
        <NFormItem label="流程Key">
          <NText>{{ currentProcess?.processKey }}</NText>
        </NFormItem>
        <NFormItem label="版本描述">
          <NInput
            v-model:value="publishForm.description"
            type="textarea"
            placeholder="请输入版本描述"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="BPMN文件" required>
          <NUpload
            v-model:file-list="publishFileList"
            :max="1"
            accept=".bpmn"
            @change="handlePublishFileChange"
          >
            <NUploadDragger>
              <div style="margin-bottom: 12px">
                <NIcon size="48" :depth="3">
                  <InboxOutlined />
                </NIcon>
              </div>
              <NText style="font-size: 16px">
                点击或者拖动文件到该区域来上传
              </NText>
              <NText depth="3" style="font-size: 12px">
                请上传.bpmn格式的流程定义文件，版本号将自动递增
              </NText>
            </NUploadDragger>
          </NUpload>
        </NFormItem>
        <NFormItem label="说明">
          <NText depth="3">发布后将创建新版本并自动发布，替换当前已发布的版本</NText>
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="showPublishModal = false">取消</NButton>
          <NButton type="primary" @click="handlePublishSubmit">发布</NButton>
        </NSpace>
      </template>
    </NModal>
  </PageLayout>
</template>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>
