/**
 * 时间处理工具函数 - 基于 dayjs
 */
import dayjs, { Dayjs } from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 扩展 dayjs 插件
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(customParseFormat)
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)
dayjs.extend(relativeTime)

// 设置中文语言
dayjs.locale('zh-cn')

// 常用日期格式常量
export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DATETIME_MINUTE: 'YYYY-MM-DD HH:mm',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY',
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSS[Z]',
  DISPLAY_DATE: 'YYYY年MM月DD日',
  DISPLAY_DATETIME: 'YYYY年MM月DD日 HH:mm:ss',
  DISPLAY_TIME: 'HH时mm分ss秒'
} as const

/**
 * 检查日期值是否有效
 * @param dateValue 日期值
 * @returns 是否为有效日期
 */
export function isValidDate(dateValue: any): boolean {
  if (!dateValue) return false
  if (dateValue === '') return false
  return dayjs(dateValue).isValid()
}

/**
 * 格式化日期
 * @param dateValue 日期值（字符串、时间戳、Date对象等）
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串，无效日期返回空字符串
 */
export function formatDate(dateValue: any, format: string = DATE_FORMATS.DATETIME): string {
  if (!isValidDate(dateValue)) return ''
  return dayjs(dateValue).format(format)
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳（毫秒）
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function formatTimestamp(timestamp: number, format: string = DATE_FORMATS.DATETIME): string {
  if (!timestamp || isNaN(timestamp)) return ''
  return dayjs(timestamp).format(format)
}

/**
 * 格式化日期字符串
 * @param dateString 日期字符串
 * @param format 格式字符串，默认为 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export function formatDateString(dateString: string, format: string = DATE_FORMATS.DATE): string {
  return formatDate(dateString, format)
}

/**
 * 安全地转换日期为时间戳
 * @param dateValue 日期值
 * @returns 时间戳或null
 */
export function safeToTimestamp(dateValue: any): number | null {
  if (!isValidDate(dateValue)) return null
  return dayjs(dateValue).valueOf()
}

/**
 * 获取当前时间戳
 * @returns 当前时间戳（毫秒）
 */
export function getCurrentTimestamp(): number {
  return dayjs().valueOf()
}

/**
 * 获取今天的开始时间戳（00:00:00）
 * @returns 今天开始时间戳
 */
export function getTodayStart(): number {
  return dayjs().startOf('day').valueOf()
}

/**
 * 获取今天的结束时间戳（23:59:59）
 * @returns 今天结束时间戳
 */
export function getTodayEnd(): number {
  return dayjs().endOf('day').valueOf()
}

/**
 * 获取相对时间描述
 * @param dateValue 日期值
 * @returns 相对时间描述，如"2小时前"、"3天后"
 */
export function getRelativeTime(dateValue: any): string {
  if (!isValidDate(dateValue)) return ''
  return dayjs(dateValue).fromNow()
}

/**
 * 计算两个日期之间的差值
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param unit 单位（day、hour、minute等）
 * @returns 差值
 */
export function getDiffBetween(startDate: any, endDate: any, unit: dayjs.ManipulateType = 'day'): number {
  if (!isValidDate(startDate) || !isValidDate(endDate)) return 0
  return dayjs(endDate).diff(dayjs(startDate), unit)
}

/**
 * 判断日期是否在指定范围内
 * @param dateValue 要检查的日期
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 是否在范围内
 */
export function isDateInRange(dateValue: any, startDate: any, endDate: any): boolean {
  if (!isValidDate(dateValue) || !isValidDate(startDate) || !isValidDate(endDate)) return false
  const date = dayjs(dateValue)
  return date.isSameOrAfter(dayjs(startDate)) && date.isSameOrBefore(dayjs(endDate))
}

/**
 * 添加时间
 * @param dateValue 基础日期
 * @param amount 数量
 * @param unit 单位
 * @returns 新的日期时间戳
 */
export function addTime(dateValue: any, amount: number, unit: dayjs.ManipulateType): number | null {
  if (!isValidDate(dateValue)) return null
  return dayjs(dateValue).add(amount, unit).valueOf()
}

/**
 * 减去时间
 * @param dateValue 基础日期
 * @param amount 数量
 * @param unit 单位
 * @returns 新的日期时间戳
 */
export function subtractTime(dateValue: any, amount: number, unit: dayjs.ManipulateType): number | null {
  if (!isValidDate(dateValue)) return null
  return dayjs(dateValue).subtract(amount, unit).valueOf()
}

/**
 * 获取dayjs实例
 * @param dateValue 日期值
 * @returns dayjs实例或null
 */
export function getDayjs(dateValue?: any): Dayjs | null {
  if (dateValue === undefined) return dayjs()
  if (!isValidDate(dateValue)) return null
  return dayjs(dateValue)
}

/**
 * 将任务数据中的时间字段转换为时间戳格式
 * @param taskData 任务数据
 * @returns 转换后的任务数据
 */
export function convertTaskTimesToTimestamp(taskData: any): any {
  return {
    ...taskData,
    plannedStartTime: safeToTimestamp(taskData.plannedStartTime),
    plannedEndTime: safeToTimestamp(taskData.plannedEndTime),
    actualStartTime: safeToTimestamp(taskData.actualStartTime),
    actualEndTime: safeToTimestamp(taskData.actualEndTime),
  }
}

// 导出 dayjs 实例供高级用法
export { dayjs }
export default dayjs
