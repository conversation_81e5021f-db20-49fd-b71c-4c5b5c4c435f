<template>
  <n-modal
    v-model:show="showModal"
    :preset="modalPreset"
    :title="title"
    :mask-closable="false"
    style="width: 600px;"
    :class="modalClass"
  >
    <div class="import-modal">
      <!-- 导入模式选择 -->
      <div class="mode-section">
        <h4>导入模式</h4>
        <n-radio-group v-model:value="importMode">
          <n-space>
            <n-radio value="incremental">
              增量导入
              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-icon :component="QuestionCircleOutlined" class="ml-1 text-gray-400" />
                </template>
                {{ incrementalTooltip }}
              </n-tooltip>
            </n-radio>
            <n-radio value="overwrite">
              覆盖导入
              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-icon :component="QuestionCircleOutlined" class="ml-1 text-gray-400" />
                </template>
                {{ overwriteTooltip }}
              </n-tooltip>
            </n-radio>
          </n-space>
        </n-radio-group>
      </div>

      <!-- 项目选择 -->
      <div v-if="showProjectSelector" class="project-section">
        <h4>选择项目</h4>
        <p class="text-gray-600 text-sm mb-3">
          {{ projectSelectorDescription }}
        </p>
        <n-select
          v-model:value="selectedProjectId"
          :options="projectOptions"
          placeholder="请选择项目"
          clearable
          filterable
        />
      </div>

      <!-- 模板下载 -->
      <div class="template-section">
        <h4>下载模板</h4>
        <p class="text-gray-600 text-sm mb-3">
          请先下载导入模板，按照模板格式填写数据后再上传
        </p>
        <n-button
          type="primary"
          ghost
          :loading="downloading"
          @click="handleDownloadTemplate"
        >
          <template #icon>
            <n-icon :component="DownloadOutlined" />
          </template>
          下载模板
        </n-button>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <h4>选择文件</h4>
        <n-upload
          ref="uploadRef"
          :file-list="fileList"
          :max="1"
          accept=".xlsx,.xls"
          :before-upload="beforeUpload"
          @change="handleFileChange"
          @remove="handleFileRemove"
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <component :is="UploadOutlined" />
              </n-icon>
            </div>
            <n-text style="font-size: 16px">
              点击或者拖动文件到该区域来上传
            </n-text>
            <n-p depth="3" style="margin: 8px 0 0 0">
              支持 .xlsx 和 .xls 格式的Excel文件，单个文件大小不超过10MB
            </n-p>
          </n-upload-dragger>
        </n-upload>
      </div>

      <!-- 导入进度 -->
      <div v-if="importing" class="progress-section">
        <h4>导入进度</h4>
        <n-progress type="line" :percentage="importProgress" :show-indicator="false" />
        <p class="text-center text-sm text-gray-600 mt-2">正在导入数据，请稍候...</p>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="result-section">
        <h4>导入结果</h4>
        <n-alert 
          :type="importResult.failed === 0 ? 'success' : (importResult.success > 0 ? 'warning' : 'error')"
          :title="getResultTitle()"
        >
          <div class="result-stats">
            <p>总记录数：{{ importResult.total }}</p>
            <p>成功导入：{{ importResult.success }}</p>
            <p v-if="importResult.skipped !== undefined">跳过记录：{{ importResult.skipped }}</p>
            <p>失败记录：{{ importResult.failed }}</p>
          </div>
        </n-alert>

        <!-- 错误详情 -->
        <div v-if="importResult.errors && importResult.errors.length > 0" class="mt-4">
          <n-collapse>
            <n-collapse-item title="查看错误详情" name="errors">
              <div class="error-list">
                <div v-for="error in importResult.errors" :key="`${error.row}-${error.field}`" class="error-item">
                  <span class="error-row">第{{ error.row }}行</span>
                  <span class="error-field">{{ error.field }}</span>
                  <span class="error-message">{{ error.message }}</span>
                </div>
              </div>
            </n-collapse-item>
          </n-collapse>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template v-if="modalPreset === 'card'" #footer>
      <div class="flex justify-end gap-3">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :disabled="!canImport"
          :loading="importing"
          @click="handleImport"
        >
          开始导入
        </n-button>
      </div>
    </template>

    <!-- Dialog 模式的操作按钮 -->
    <template v-if="modalPreset === 'dialog'" #action>
      <n-space>
        <n-button @click="handleCancel" :disabled="importing">
          取消
        </n-button>
        <n-button
          type="primary"
          @click="handleImport"
          :loading="importing"
          :disabled="!canImport"
        >
          开始导入
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  NModal, NRadioGroup, NRadio, NSpace, NTooltip, NIcon, NUpload, NUploadDragger,
  NText, NP, NProgress, NAlert, NCollapse, NCollapseItem, NButton, NSelect, useMessage
} from 'naive-ui'
import { UploadOutlined, QuestionCircleOutlined, DownloadOutlined } from '@vicons/antd'
import type { UploadFileInfo } from 'naive-ui'
import { useImport, type ImportData } from '@/composables/useImport'

interface Props {
  show: boolean
  title: string
  showProjectSelector?: boolean
  projectId?: number
  projectName?: string
  projects?: Array<{ id: number; name: string }>
  onImport?: (data: ImportData) => Promise<any>
  onDownloadTemplate?: () => Promise<void>
  // 新增的配置属性
  modalPreset?: 'card' | 'dialog'
  modalClass?: string
  incrementalTooltip?: string
  overwriteTooltip?: string
  projectSelectorDescription?: string
}

interface Emits {
  (e: 'update:show', show: boolean): void
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const { importing, importProgress, importResult, startImport, handleImportSuccess, handleImportError, resetImport } = useImport()

// 响应式数据
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const importMode = ref<'incremental' | 'overwrite'>('incremental')
const fileList = ref<UploadFileInfo[]>([])
const downloading = ref(false)
const selectedProjectId = ref<number | null>(null)

const uploadRef = ref()

// 计算属性
const canImport = computed(() => {
  const hasFile = fileList.value.length > 0 && !importing.value
  if (props.showProjectSelector) {
    return hasFile && selectedProjectId.value !== null
  }
  return hasFile
})

const projectOptions = computed(() => {
  if (!props.projects) return []
  return props.projects.map(project => ({
    label: project.name,
    value: project.id
  }))
})

const showProjectSelector = computed(() => {
  return props.showProjectSelector && !props.projectId
})

// 计算属性：模态框预设
const modalPreset = computed(() => props.modalPreset || 'card')

// 计算属性：模态框类名
const modalClass = computed(() => props.modalClass || 'import-modal')

// 计算属性：增量导入提示文本
const incrementalTooltip = computed(() =>
  props.incrementalTooltip || '根据唯一标识符跳过已存在的记录，只导入新记录'
)

// 计算属性：覆盖导入提示文本
const overwriteTooltip = computed(() =>
  props.overwriteTooltip || '根据唯一标识符更新已存在的记录，导入新记录'
)

// 计算属性：项目选择器描述文本
const projectSelectorDescription = computed(() =>
  props.projectSelectorDescription || '请选择要导入任务的目标项目'
)

// 获取结果标题
const getResultTitle = () => {
  if (!importResult.value) return ''
  
  const { success, failed, total } = importResult.value
  if (failed === 0) {
    return `导入成功！共导入 ${success} 条记录`
  } else if (success > 0) {
    return `部分导入成功！成功 ${success} 条，失败 ${failed} 条`
  } else {
    return `导入失败！共 ${total} 条记录全部失败`
  }
}

// 下载模板
const handleDownloadTemplate = async () => {
  if (!props.onDownloadTemplate) {
    message.error('下载模板功能未配置')
    return
  }

  try {
    downloading.value = true
    await props.onDownloadTemplate()
  } catch (error) {
    console.error('下载模板失败:', error)
    message.error('下载模板失败，请稍后重试')
  } finally {
    downloading.value = false
  }
}

// 文件上传前检查
const beforeUpload = (data: { file: UploadFileInfo }) => {
  const file = data.file.file
  if (!file) return false

  // 检查文件类型
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel' // .xls
  ]
  
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持 .xlsx 和 .xls 格式的Excel文件')
    return false
  }

  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过10MB')
    return false
  }

  return true
}

// 文件变化处理
const handleFileChange = (options: { fileList: UploadFileInfo[] }) => {
  fileList.value = options.fileList
  importResult.value = null // 清除之前的导入结果
}

// 文件移除处理
const handleFileRemove = () => {
  fileList.value = []
  importResult.value = null
}

// 开始导入
const handleImport = async () => {
  if (!fileList.value.length) {
    message.error('请先选择要导入的文件')
    return
  }

  const file = fileList.value[0].file
  if (!file) {
    message.error('文件无效')
    return
  }

  if (!props.onImport) {
    message.error('导入功能未配置')
    return
  }

  try {
    // 开始导入
    startImport()

    // 准备导入数据
    const projectId = props.projectId || selectedProjectId.value || undefined
    const selectedProject = props.projects?.find(p => p.id === selectedProjectId.value)
    const projectName = props.projectName || selectedProject?.name || undefined

    const importData: ImportData = {
      file,
      mode: importMode.value,
      projectId,
      projectName
    }

    // 调用导入函数
    const result = await props.onImport(importData)

    // 处理导入结果
    const status = handleImportSuccess(result)

    // 如果完全成功，延迟关闭弹窗
    if (status === 'success') {
      setTimeout(() => {
        emit('success')
      }, 1500)
    }

  } catch (error) {
    handleImportError(error)
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 重置状态
const resetState = () => {
  importMode.value = 'incremental'
  fileList.value = []
  downloading.value = false
  resetImport()
  if (props.showProjectSelector && !props.projectId) {
    selectedProjectId.value = null
  }
}

// 监听弹窗显示状态，重置状态
watch(() => props.show, (show) => {
  if (show) {
    resetState()
  }
})
</script>

<style lang="less" scoped>
.import-modal {
  .mode-section,
  .project-section,
  .template-section,
  .upload-section,
  .progress-section,
  .result-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .result-stats {
    p {
      margin: 4px 0;
      font-size: 13px;
    }
  }

  .error-list {
    max-height: 200px;
    overflow-y: auto;
    
    .error-item {
      display: flex;
      gap: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;
      
      .error-row {
        color: #666;
        min-width: 60px;
      }
      
      .error-field {
        color: #1890ff;
        min-width: 80px;
      }
      
      .error-message {
        color: #ff4d4f;
        flex: 1;
      }
    }
  }
}
</style>
