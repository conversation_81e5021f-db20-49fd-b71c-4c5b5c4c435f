// 任务状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { taskApi, type TaskQueryParams, type CreateTaskParams, type UpdateTaskParams, type TaskOperationParams } from '@/apis'
import type { Task } from '@/types'
import { showMessage } from '@/utils/errorHandler'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 计算属性
  const openTasks = computed(() => tasks.value.filter((t: Task) => t.status === 'open'))
  const inprogressTasks = computed(() => tasks.value.filter((t: Task) => t.status === 'inprogress'))
  const toVerifyTasks = computed(() => tasks.value.filter((t: Task) => t.status === 'toverify'))
  const closeTasks = computed(() => tasks.value.filter((t: Task) => t.status === 'close'))
  
  const myTasks = computed(() => {
    const currentUserId = localStorage.getItem('currentUserId')
    return tasks.value.filter((t: Task) => t.assignee === currentUserId)
  })

  // 获取任务列表
  const fetchTasks = async (params?: TaskQueryParams) => {
    try {
      loading.value = true
      const response = await taskApi.getTasks({
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...params
      })
      
      tasks.value = response.data.data
      pagination.value.total = response.data.total
      pagination.value.page = response.data.page
      pagination.value.pageSize = response.data.pageSize
    } catch (error: any) {
      showMessage('error', error.message || '获取任务列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取项目任务列表
  const fetchProjectTasks = async (projectId: number | string, params?: Omit<TaskQueryParams, 'projectId'>) => {
    try {
      loading.value = true
      const response = await taskApi.getProjectTasks(projectId, {
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...params
      })
      
      tasks.value = response.data.data
      pagination.value.total = response.data.total
      pagination.value.page = response.data.page
      pagination.value.pageSize = response.data.pageSize
    } catch (error: any) {
      showMessage('error', error.message || '获取项目任务失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取任务详情
  const fetchTask = async (id: number | string) => {
    try {
      loading.value = true
      const response = await taskApi.getTask(id)
      currentTask.value = response.data
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '获取任务详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建任务
  const createTask = async (data: CreateTaskParams) => {
    try {
      loading.value = true
      const response = await taskApi.createTask(data)
      tasks.value.unshift(response.data)
      pagination.value.total += 1
      showMessage('success', '任务创建成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '创建任务失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新任务
  const updateTask = async (data: UpdateTaskParams) => {
    try {
      loading.value = true
      const response = await taskApi.updateTask(data)
      
      // 更新列表中的任务
      const index = tasks.value.findIndex((t: Task) => t.id === data.id)
      if (index !== -1) {
        tasks.value[index] = response.data
      }
      
      // 更新当前任务
      if (currentTask.value?.id === data.id) {
        currentTask.value = response.data
      }
      
      showMessage('success', '任务更新成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '更新任务失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除任务
  const deleteTask = async (id: number | string) => {
    try {
      loading.value = true
      await taskApi.deleteTask(id)
      
      // 从列表中移除
      const index = tasks.value.findIndex((t: Task) => t.id === id)
      if (index !== -1) {
        tasks.value.splice(index, 1)
        pagination.value.total -= 1
      }
      
      // 清空当前任务
      if (currentTask.value?.id === id) {
        currentTask.value = null
      }
      
      showMessage('success', '任务删除成功')
    } catch (error: any) {
      showMessage('error', error.message || '删除任务失败')
      throw error
    } finally {
      loading.value = false
    }
  }



  // 执行任务操作
  const executeTaskOperation = async (params: TaskOperationParams) => {
    try {
      await taskApi.executeTaskOperation(params)

      // 重新获取任务详情以更新状态
      const response = await taskApi.getTask(params.id)
      const updatedTask = response.data

      // 更新列表中的任务
      const index = tasks.value.findIndex((t: Task) => t.id === params.id)
      if (index !== -1) {
        tasks.value[index] = updatedTask
      }

      // 更新当前任务
      if (currentTask.value?.id === params.id) {
        currentTask.value = updatedTask
      }

      showMessage('success', '任务操作执行成功')
      return updatedTask
    } catch (error: any) {
      showMessage('error', error.message || '操作执行失败')
      throw error
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.page = page
    pagination.value.pageSize = pageSize
  }

  // 重置状态
  const reset = () => {
    tasks.value = []
    currentTask.value = null
    loading.value = false
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }

  return {
    // 状态
    tasks,
    currentTask,
    loading,
    pagination,
    
    // 计算属性
    openTasks,
    inprogressTasks,
    toVerifyTasks,
    closeTasks,
    myTasks,
    
    // 方法
    fetchTasks,
    fetchProjectTasks,
    fetchTask,
    createTask,
    updateTask,
    deleteTask,
    executeTaskOperation,
    setPagination,
    reset
  }
})
