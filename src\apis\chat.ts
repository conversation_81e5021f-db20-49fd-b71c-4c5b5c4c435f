// 聊天相关 API 接口
import { httpClient } from './index'
import type { ApiResponse } from '@/types'

// 聊天消息类型
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
}

// 聊天请求参数
export interface ChatRequestParams {
  agentId: number
  message: string
  sessionId?: string
  userId?: string
  projectId?: number
  taskId?: number
  history?: ChatMessage[]
  context?: Record<string, any>
  enableFunctionCalling?: boolean
  stream?: boolean
}

// 功能调用信息
export interface FunctionCall {
  functionName: string
  parameters: Record<string, any>
  result: any
  success: boolean
  error?: string
}

// 建议操作
export interface SuggestedAction {
  action: string
  description: string
  parameters: Record<string, any>
}

// 聊天响应
export interface ChatResponse {
  message: string
  agentId: number
  agentName: string
  sessionId: string
  timestamp: number
  functionCalled?: boolean
  functionCalls?: FunctionCall[]
  suggestedActions?: SuggestedAction[]
  data?: Record<string, any>
  error?: string
  tokensUsed?: number
  responseTime?: number
}

// 快速聊天请求参数
export interface QuickChatParams {
  agentId: number
  message: string
  userId?: string
  projectId?: number
}

// 健康检查响应
export interface HealthResponse {
  status: string
  timestamp: number
  supportedProviders: string[]
}

/**
 * 聊天 API 类
 * 注意：聊天相关接口已合并到AgentApi中，这个类主要用于向后兼容
 */
export class ChatApi {
  private static readonly BASE_PATH = '/agents'

  /**
   * 发送聊天消息
   */
  static async chat(params: ChatRequestParams): Promise<ApiResponse<ChatResponse>> {
    return httpClient.post<ChatResponse>(`${this.BASE_PATH}/chat`, params)
  }

  /**
   * 发送流式聊天消息
   */
  static async streamChat(params: ChatRequestParams, onMessage: (chunk: string) => void, onComplete?: () => void, onError?: (error: Error) => void): Promise<void> {
    // 委托给AgentApi
    const { AgentApi } = await import('./agent')
    return AgentApi.streamChat(params, onMessage, onComplete, onError)
  }

  /**
   * 快速问答
   */
  static async quickChat(params: QuickChatParams): Promise<ApiResponse<string>> {
    return httpClient.post<string>(`${this.BASE_PATH}/chat/quick`, params)
  }

  /**
   * 验证Agent配置
   */
  static async validateAgent(agentId: number): Promise<ApiResponse<boolean>> {
    return httpClient.post<boolean>(`${this.BASE_PATH}/${agentId}/validate`)
  }

  /**
   * 获取Agent功能列表
   */
  static async getAgentFunctions(agentId: number): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>(`${this.BASE_PATH}/${agentId}/functions`)
  }

  /**
   * 获取支持的LLM提供商列表
   */
  static async getSupportedProviders(): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>(`${this.BASE_PATH}/providers`)
  }

  /**
   * 检查LLM提供商是否支持
   */
  static async isProviderSupported(provider: string): Promise<ApiResponse<boolean>> {
    return httpClient.get<boolean>(`${this.BASE_PATH}/providers/${provider}/supported`)
  }

  /**
   * 健康检查
   */
  static async health(): Promise<ApiResponse<HealthResponse>> {
    return httpClient.get<HealthResponse>(`${this.BASE_PATH}/health`)
  }
}
