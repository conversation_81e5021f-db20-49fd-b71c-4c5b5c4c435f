import { httpClient } from './index'
import type { ApiResponse, PaginationResponse } from '@/types'

export interface TaskCategoryOption {
  label: string
  value: string
}

export interface TaskCategory {
  id: number
  code: string
  name: string
  description?: string
  sortOrder: number
  enabled: boolean
  createTime: string
  updateTime: string
  createBy?: string
  updateBy?: string
}

export interface TaskCategoryQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  enabled?: boolean
}

export interface CreateTaskCategoryParams {
  code: string
  name: string
  description?: string
  sortOrder?: number
  enabled?: boolean
}

export interface UpdateTaskCategoryParams extends CreateTaskCategoryParams {
  id: number
}

/**
 * 任务分类 API
 */
export class TaskCategoryApi {
  private static readonly BASE_PATH = '/task-categories'

  /**
   * 获取任务分类选项列表
   */
  static async getTaskCategoryOptions(): Promise<ApiResponse<TaskCategoryOption[]>> {
    return httpClient.get<TaskCategoryOption[]>(`${this.BASE_PATH}/options`)
  }

  /**
   * 分页查询任务分类
   */
  static async getTaskCategoryPage(params?: TaskCategoryQueryParams): Promise<ApiResponse<PaginationResponse<TaskCategory>>> {
    return httpClient.get<PaginationResponse<TaskCategory>>(`${this.BASE_PATH}/page`, params)
  }

  /**
   * 根据ID查询任务分类
   */
  static async getTaskCategory(id: number): Promise<ApiResponse<TaskCategory>> {
    return httpClient.get<TaskCategory>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建任务分类
   */
  static async createTaskCategory(data: CreateTaskCategoryParams): Promise<ApiResponse<TaskCategory>> {
    return httpClient.post<TaskCategory>(this.BASE_PATH, data)
  }

  /**
   * 更新任务分类
   */
  static async updateTaskCategory(data: UpdateTaskCategoryParams): Promise<ApiResponse<TaskCategory>> {
    const { id, ...updateData } = data
    return httpClient.put<TaskCategory>(`${this.BASE_PATH}/${id}`, updateData)
  }

  /**
   * 删除任务分类
   */
  static async deleteTaskCategory(id: number): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/${id}`)
  }
}
