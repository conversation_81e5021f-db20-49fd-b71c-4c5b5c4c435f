<template>
  <div class="cab-submit-form">
    <div class="form-content-wrapper">
      <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="top"
        require-mark-placement="right-hanging" size="small">

        <div class="form-layout">
          <!-- 左侧主要内容 -->
          <div class="main-content">
          <!-- Change General Information -->
          <div class="section-card mb-4">
            <h3 class="section-title">Change General Information</h3>
            <n-grid :cols="12" :x-gap="12">
              <n-gi :span="2">
                <n-form-item label="CR ID" path="crId">
                  <n-input v-model:value="formData.crId" placeholder="请输入CR ID"
                    :disabled="readonly" clearable :allow-input="onlyAllowNumber" size="small" />
                </n-form-item>
              </n-gi>
              <n-gi :span="6">
                <n-form-item label="CR Title" path="crTitle">
                  <n-input v-model:value="formData.crTitle" placeholder="请输入CR Title"
                    :disabled="readonly" clearable size="small" />
                </n-form-item>
              </n-gi>
              <n-gi :span="4">
                <n-form-item label="CR Owner" path="crOwner">
                  <n-select v-model:value="formData.crOwner" :options="userOptions"
                    :loading="loadingUsers" placeholder="请选择CR Owner"
                    :disabled="readonly" filterable clearable size="small"
                    @click="handleUserClick" />
                </n-form-item>
              </n-gi>
            </n-grid>

            <n-form-item label="CR Link:"  :show-feedback="false" label-placement="left">
              <n-input :value="crLinkValue" placeholder="CR Link将自动生成"
                readonly disabled  size="small" />
            </n-form-item>
          </div>

          <!-- Description 和 Test record 并排显示 -->
          <n-grid :cols="2" :x-gap="12" class="mb-4">
            <!-- Description -->
            <n-gi>
              <div class="section-card equal-height-card">
                <h3 class="section-title">Description</h3>
                <n-form-item label="Change Reason" path="changeReason" class="flex-1">
                  <n-input v-model:value="formData.changeReason" type="textarea"
                    placeholder="请输入变更原因" :rows="5" :disabled="readonly"
                    show-count :maxlength="1000" class="h-full" size="small" />
                </n-form-item>
              </div>
            </n-gi>

            <!-- Test record -->
            <n-gi>
              <div class="section-card equal-height-card">
                <h3 class="section-title">Test record</h3>

                <n-form-item label="Test time" path="testTimeRange" class="mb-3">
                  <n-date-picker v-model:value="formData.testTimeRange" type="datetimerange"
                    placeholder="请选择测试时间范围" :disabled="readonly" clearable class="w-full"
                    format="yyyy-MM-dd HH:mm" size="small" />
                </n-form-item>

                <n-form-item label="Test Steps" path="testSteps">
                  <n-input v-model:value="formData.testSteps" type="textarea"
                    placeholder="请输入测试步骤" :rows="2" :disabled="readonly"
                    show-count :maxlength="2000" size="small" />
                </n-form-item>

                <n-form-item label="Test Result" path="testResult">
                  <n-input v-model:value="formData.testResult" type="textarea"
                    placeholder="请输入测试结果" :rows="2" :disabled="readonly"
                    show-count :maxlength="2000" size="small" />
                </n-form-item>
              </div>
            </n-gi>
          </n-grid>
        </div>

        <!-- 右侧信息面板 -->
        <div class="side-panel">
          <!-- Impact -->
          <div class="panel-section mb-3">
            <n-form-item label="Impact" path="impact">
              <n-input v-model:value="formData.impact" type="textarea"
                placeholder="请输入影响描述" :rows="4" :disabled="readonly"
                show-count :maxlength="500" size="small" />
            </n-form-item>
          </div>

          <!-- Planning -->
          <div class="panel-section mb-3">
            <n-form-item label="Planning" path="planningTimeRange">
              <n-date-picker v-model:value="formData.planningTimeRange" type="datetimerange"
                placeholder="请选择计划时间范围" :disabled="readonly" clearable size="small" class="w-full"
                format="yyyy-MM-dd HH:mm" />
            </n-form-item>
          </div>

          <!-- Rollback -->
          <div class="panel-section mb-3">
            <n-form-item label="Rollback" path="rollback">
              <n-input v-model:value="formData.rollback" type="textarea"
                placeholder="请输入回滚计划" :rows="4" :disabled="readonly"
                show-count :maxlength="500" size="small" />
            </n-form-item>
          </div>

          <!-- Remark -->
          <div class="panel-section">
            <n-form-item label="Remark" path="remark">
              <n-input v-model:value="formData.remark" type="textarea"
                placeholder="请输入备注" :rows="2" :disabled="readonly"
                show-count :maxlength="500" size="small" />
            </n-form-item>
          </div>
        </div>
      </div>
    </n-form>
    </div>

    <!-- 操作按钮 -->
    <div v-if="!readonly" class="form-actions">
      <n-button @click="handleReset" size="small">重置</n-button>
      <n-button type="primary" @click="handleSubmit" :loading="submitting" size="small">
        {{ submitText || '提交CAB' }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NDatePicker,
  NButton,
  NGrid,
  NGi,
  NSelect,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { UserApi } from '@/apis/user'

// Props定义
const props = defineProps<{
  initialData?: Record<string, any> // 初始数据
  readonly?: boolean // 是否只读
  submitText?: string // 提交按钮文本
  projectId?: number // 项目ID
}>()

// Emits定义
const emit = defineEmits<{
  submit: [data: Record<string, any>]
  change: [data: Record<string, any>]
}>()

const message = useMessage()
const formRef = ref<FormInst>()
const submitting = ref(false)
const loadingUsers = ref(false)
const userOptions = ref<Array<{ label: string; value: string }>>([])

// 表单数据
const formData = ref<{
  crId: string
  crTitle: string
  crOwner: string
  changeReason: string
  testTimeRange: [number, number] | null
  testSteps: string
  testResult: string
  impact: string
  planningTimeRange: [number, number] | null
  rollback: string
  remark: string
}>({
  crId: '',
  crTitle: '',
  crOwner: '',
  changeReason: '',
  testTimeRange: null,
  testSteps: '',
  testResult: '',
  impact: '',
  planningTimeRange: null,
  rollback: '',
  remark: ''
})

// CR Link计算属性
const crLinkValue = computed(() => {
  if (formData.value.crId && formData.value.crId.trim() !== '') {
    return `https://me.vwatj.ap.vwg/ChangeDetails.do?CHANGEID=${formData.value.crId}`
  }
  return ''
})

// 只允许输入数字的函数
const onlyAllowNumber = (value: string) => !value || /^\d+$/.test(value)

// 表单验证规则
const formRules = computed<FormRules>(() => ({
  crId: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请输入CR ID')
      }
      if (!/^\d+$/.test(value)) {
        return new Error('CR ID只能包含数字')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  crTitle: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请输入CR Title')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  crOwner: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请选择CR Owner')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  changeReason: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请输入变更原因')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  testTimeRange: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || !Array.isArray(value) || value.length !== 2) {
        return new Error('请选择测试时间范围')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  testSteps: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请输入测试步骤')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  testResult: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请输入测试结果')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  impact: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请输入影响描述')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  planningTimeRange: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || !Array.isArray(value) || value.length !== 2) {
        return new Error('请选择计划时间范围')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  rollback: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请输入回滚计划')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  remark: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (!value || value.trim() === '') {
        return new Error('请输入备注')
      }
      return true
    },
    trigger: ['blur', 'change']
  }
}))

// 加载用户选项
const loadUserOptions = async () => {
  if (userOptions.value.length > 0) return // 避免重复加载

  loadingUsers.value = true
  try {
    const response = await UserApi.getUserOptions()
    if (response.data) {
      userOptions.value = response.data
    }
  } catch (error) {
    console.error('加载用户选项失败:', error)
    message.error('加载用户选项失败')
  } finally {
    loadingUsers.value = false
  }
}

// 处理用户下拉点击
const handleUserClick = () => {
  loadUserOptions()
}

// 初始化表单数据
const initFormData = () => {
  if (props.initialData) {
    Object.assign(formData.value, props.initialData)
  }
  emit('change', formData.value)
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    // 构建提交数据
    const submitData = {
      ...formData.value,
      crLink: crLinkValue.value,
      submittedAt: new Date().toISOString()
    }

    emit('submit', submitData)
    message.success('CAB表单提交成功')
  } catch (error) {
    message.error('请检查表单填写是否完整')
  } finally {
    submitting.value = false
  }
}

// 处理表单重置
const handleReset = () => {
  formRef.value?.restoreValidation()
  initFormData()
  message.info('已重置表单')
}

// 监听表单数据变化
watch(formData, (newData) => {
  emit('change', newData)
}, { deep: true })

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    Object.assign(formData.value, newData)
  }
}, { deep: true })

// 组件挂载时初始化数据
onMounted(() => {
  initFormData()
})
</script>

<style scoped>

.form-layout {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  min-height: 0;
}

.form-actions {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 8px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  background: white;
}


.side-panel {
  flex: 0 0 380px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
}

/* 区域卡片样式 */
.section-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
}

/* 等高卡片样式 */
.equal-height-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}


.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #f3f4f6;
}

</style>
