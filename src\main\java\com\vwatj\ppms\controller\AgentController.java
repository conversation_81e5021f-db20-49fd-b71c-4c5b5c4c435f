package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.*;
import com.vwatj.ppms.entity.Agent;
import com.vwatj.ppms.service.AgentService;
import com.vwatj.ppms.agent.llm.LLMProviderFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 统一Agent控制器
 * 合并了原AgentController、CustomAgentController、ChatController的功能
 * 以及UnifiedAgentService和ChatService的功能
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@RestController
@RequestMapping("/agents")
@RequiredArgsConstructor
public class AgentController {

    private final AgentService agentService;
    private final LLMProviderFactory llmProviderFactory;
    
    /**
     * 分页查询Agent
     */
    @GetMapping
    public ApiResponse<PageResult<Agent>> getAgents(AgentQueryDTO queryDTO) {
        PageResult<Agent> result = agentService.getAgentPage(queryDTO);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询Agent
     */
    @GetMapping("/{id}")
    public ApiResponse<Agent> getAgent(@PathVariable Long id) {
        Agent agent = agentService.getById(id);
        if (agent == null) {
            return ApiResponse.notFound("Agent不存在");
        }
        return ApiResponse.success(agent);
    }
    
    /**
     * 创建Agent
     */
    @PostMapping
    public ApiResponse<Agent> createAgent(@Validated @RequestBody CreateAgentDTO createAgentDTO) {
        try {
            Agent agent = agentService.createAgent(createAgentDTO);
            return ApiResponse.success("Agent创建成功", agent);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 更新Agent
     */
    @PutMapping("/{id}")
    public ApiResponse<Agent> updateAgent(@PathVariable Long id, @Validated @RequestBody UpdateAgentDTO updateAgentDTO) {
        try {
            updateAgentDTO.setId(id);
            Agent agent = agentService.updateAgent(updateAgentDTO);
            return ApiResponse.success("Agent更新成功", agent);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 删除Agent
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteAgent(@PathVariable Long id) {
        try {
            agentService.deleteAgent(id);
            return ApiResponse.success("Agent删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 切换Agent状态
     */
    @PatchMapping("/{id}/toggle-status")
    public ApiResponse<String> toggleAgentStatus(@PathVariable Long id) {
        try {
            agentService.toggleAgentStatus(id);
            return ApiResponse.success("Agent状态切换成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    // ==================== 聊天相关接口 ====================

    /**
     * 发送聊天消息
     */
    @PostMapping("/chat")
    public ApiResponse<ChatResponseDTO> chat(@Validated @RequestBody ChatRequestDTO request) {
        try {
            log.info("收到聊天请求: agentId={}, sessionId={}, userId={}",
                    request.getAgentId(), request.getSessionId(), request.getUserId());

            ChatResponseDTO response = agentService.chat(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("聊天请求处理失败", e);
            return ApiResponse.badRequest("聊天请求处理失败: " + e.getMessage());
        }
    }

    /**
     * 发送流式聊天消息
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_PLAIN_VALUE)
    public Flux<String> streamChat(@Validated @RequestBody ChatRequestDTO request) {
        try {
            log.info("收到流式聊天请求: agentId={}, sessionId={}, userId={}",
                    request.getAgentId(), request.getSessionId(), request.getUserId());

            String response = agentService.streamChat(request);

            // 模拟流式输出
            return Flux.fromArray(response.split(""))
                    .delayElements(Duration.ofMillis(30));
        } catch (Exception e) {
            log.error("流式聊天请求处理失败", e);
            return Flux.just("错误：处理您的请求时遇到了问题");
        }
    }

    /**
     * 快速问答接口
     */
    @PostMapping("/chat/quick")
    public ApiResponse<String> quickChat(@RequestBody Map<String, Object> request) {
        try {
            Long agentId = request.get("agentId") != null ?
                    Long.valueOf(request.get("agentId").toString()) : null;
            String message = (String) request.get("message");
            String userId = (String) request.get("userId");
            Long projectId = request.get("projectId") != null ?
                    Long.valueOf(request.get("projectId").toString()) : null;

            if (agentId == null || message == null || message.trim().isEmpty()) {
                return ApiResponse.badRequest("agentId和message不能为空");
            }

            log.info("收到快速聊天请求: agentId={}, message={}", agentId, message);

            ChatRequestDTO chatRequest = new ChatRequestDTO();
            chatRequest.setAgentId(agentId);
            chatRequest.setMessage(message);
            chatRequest.setUserId(userId);
            chatRequest.setProjectId(projectId);
            chatRequest.setSessionId("quick-" + System.currentTimeMillis());

            ChatResponseDTO response = agentService.chat(chatRequest);
            return ApiResponse.success(response.getMessage());
        } catch (Exception e) {
            log.error("快速聊天请求处理失败", e);
            return ApiResponse.badRequest("快速聊天请求处理失败: " + e.getMessage());
        }
    }

    // ==================== Agent管理接口 ====================

    /**
     * 初始化Agent（自定义类型）
     */
    @PostMapping("/{agentId}/initialize")
    public ApiResponse<Void> initializeAgent(@PathVariable Long agentId) {
        try {
            log.info("初始化Agent: agentId={}", agentId);

            Agent agent = agentService.getById(agentId);
            if (agent == null) {
                return ApiResponse.notFound("Agent不存在");
            }

            // 使用统一Agent服务进行初始化
            agentService.initializeAgent(agentId);

            return ApiResponse.success();
        } catch (Exception e) {
            log.error("初始化Agent失败: agentId={}", agentId, e);
            return ApiResponse.badRequest("初始化Agent失败: " + e.getMessage());
        }
    }

    /**
     * 验证Agent配置
     */
    @PostMapping("/{agentId}/validate")
    public ApiResponse<Boolean> validateAgent(@PathVariable Long agentId) {
        try {
            log.info("验证Agent配置: agentId={}", agentId);

            boolean isValid = agentService.validateAgentConfig(agentId);
            return ApiResponse.success(isValid);
        } catch (Exception e) {
            log.error("验证Agent配置失败: agentId={}", agentId, e);
            return ApiResponse.badRequest("验证Agent配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取Agent功能列表
     */
    @GetMapping("/{agentId}/functions")
    public ApiResponse<List<String>> getAgentFunctions(@PathVariable Long agentId) {
        try {
            log.info("获取Agent功能列表: agentId={}", agentId);

            Agent agent = agentService.getById(agentId);
            if (agent == null) {
                return ApiResponse.notFound("Agent不存在");
            }

            List<String> functions = agentService.getSupportedFunctions(agentId);

            return ApiResponse.success(functions);
        } catch (Exception e) {
            log.error("获取Agent功能列表失败: agentId={}", agentId, e);
            return ApiResponse.badRequest("获取Agent功能列表失败: " + e.getMessage());
        }
    }

    /**
     * 执行Agent功能（自定义类型）
     */
    @PostMapping("/{agentId}/functions/{functionName}")
    public ApiResponse<Object> executeFunction(@PathVariable Long agentId,
                                             @PathVariable String functionName,
                                             @RequestBody Map<String, Object> parameters) {
        try {
            log.info("执行Agent功能: agentId={}, functionName={}", agentId, functionName);

            Agent agent = agentService.getById(agentId);
            if (agent == null) {
                return ApiResponse.notFound("Agent不存在");
            }

            if (!"custom".equals(agent.getType())) {
                return ApiResponse.badRequest("只有自定义类型的Agent支持直接功能调用");
            }

            Object result = agentService.executeFunction(agentId, functionName, parameters);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("执行Agent功能失败: agentId={}, functionName={}", agentId, functionName, e);
            return ApiResponse.badRequest("执行功能失败: " + e.getMessage());
        }
    }

    /**
     * 获取Agent状态
     */
    @GetMapping("/{agentId}/status")
    public ApiResponse<Map<String, Object>> getAgentStatus(@PathVariable Long agentId) {
        try {
            log.info("获取Agent状态: agentId={}", agentId);

            Agent agent = agentService.getById(agentId);
            if (agent == null) {
                return ApiResponse.notFound("Agent不存在");
            }

            Map<String, Object> status = agentService.getAgentStatus(agentId);

            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("获取Agent状态失败: agentId={}", agentId, e);
            return ApiResponse.badRequest("获取状态失败: " + e.getMessage());
        }
    }

    // ==================== 系统功能接口 ====================

    /**
     * 获取所有可用的业务功能
     */
    @GetMapping("/functions")
    public ApiResponse<List<String>> getAllAvailableFunctions() {
        try {
            log.info("获取所有可用的业务功能");

            List<String> functions = agentService.getAllAvailableFunctions();
            return ApiResponse.success(functions);
        } catch (Exception e) {
            log.error("获取所有可用的业务功能失败", e);
            return ApiResponse.badRequest("获取功能列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取业务功能的Schema定义
     */
    @GetMapping("/functions/schemas")
    public ApiResponse<Map<String, Map<String, Object>>> getFunctionSchemas() {
        try {
            log.info("获取业务功能的Schema定义");

            Map<String, Map<String, Object>> schemas = agentService.getFunctionSchemas();
            return ApiResponse.success(schemas);
        } catch (Exception e) {
            log.error("获取业务功能的Schema定义失败", e);
            return ApiResponse.badRequest("获取Schema失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的LLM提供商列表
     */
    @GetMapping("/providers")
    public ApiResponse<List<String>> getSupportedProviders() {
        try {
            List<String> providers = llmProviderFactory.getSupportedProviders();
            return ApiResponse.success(providers);
        } catch (Exception e) {
            log.error("获取支持的LLM提供商列表失败", e);
            return ApiResponse.badRequest("获取支持的LLM提供商列表失败: " + e.getMessage());
        }
    }

    /**
     * 检查LLM提供商是否支持
     */
    @GetMapping("/providers/{provider}/supported")
    public ApiResponse<Boolean> isProviderSupported(@PathVariable String provider) {
        try {
            boolean supported = llmProviderFactory.isSupported(provider);
            return ApiResponse.success(supported);
        } catch (Exception e) {
            log.error("检查LLM提供商支持失败: provider={}", provider, e);
            return ApiResponse.badRequest("检查LLM提供商支持失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        try {
            Map<String, Object> health = Map.of(
                "status", "healthy",
                "timestamp", System.currentTimeMillis(),
                "supportedProviders", llmProviderFactory.getSupportedProviders()
            );
            return ApiResponse.success(health);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return ApiResponse.badRequest("健康检查失败: " + e.getMessage());
        }
    }

}
