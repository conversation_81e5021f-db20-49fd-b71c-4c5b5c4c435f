<template>
  <page-layout title="系统管理">
    <!-- 顶部搜索栏 -->
    <div class="search-bar">
      <div class="search-left">
        <n-input
          v-model:value="searchQuery.keyword"
          placeholder="搜索资产编号、项目名称、CI名称..."
          clearable
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <n-icon :component="SearchOutlined" />
          </template>
        </n-input>
        
        <n-select
          v-model:value="searchQuery.region"
          placeholder="选择区域"
          clearable
          :options="regionOptions"
          class="filter-select"
        />
        
        <n-select
          v-model:value="searchQuery.businessDepartment"
          placeholder="选择业务部门"
          clearable
          :options="businessDepartmentOptions"
          class="filter-select"
        />
        
        <n-select
          v-model:value="searchQuery.assetState"
          placeholder="选择资产状态"
          clearable
          :options="assetStateOptions"
          class="filter-select"
        />
      </div>
      
      <div class="search-right">
        <n-button @click="handleSearch" type="primary">
          <template #icon>
            <n-icon :component="SearchOutlined" />
          </template>
          搜索
        </n-button>
        
        <n-button @click="handleReset">
          <template #icon>
            <n-icon :component="ReloadOutlined" />
          </template>
          重置
        </n-button>
        
        <n-button @click="handleExport">
          <template #icon>
            <n-icon :component="ExportOutlined" />
          </template>
          导出数据
        </n-button>

        <n-button @click="handleImport">
          <template #icon>
            <n-icon :component="UploadOutlined" />
          </template>
          导入资产
        </n-button>

        <n-button @click="handleCreate" type="primary">
          <template #icon>
            <n-icon :component="PlusOutlined" />
          </template>
          新建资产
        </n-button>
      </div>
    </div>

    <!-- 卡片视图 -->
    <div class="cards-container" v-loading="loading">
      <div class="cards-grid">
        <div
          v-for="asset in assets"
          :key="asset.id"
          class="asset-card"
          @click="handleCardClick(asset)"
        >
          <!-- 卡片图片 -->
          <div class="card-image">
            <img
              :src="getAssetImage(asset)"
              :alt="asset.projectName"
              @error="handleImageError"
            />

            <n-tag
              v-if="asset.region"
              size="small"
              :bordered="false"
              round
              class="region-tag"
              >
              {{ asset.region || '' }}
            </n-tag>

            <!-- 系统名称覆盖层 -->
            <div class="title-overlay">
              <h3 class="card-title">{{ asset.projectName }}</h3>
              <n-tag
                :type="getAssetStateType(asset.usageStatus)"
                size="small"
                class="status-tag"
              >
              {{ asset.usageStatus || '未知' }}
              </n-tag>
            </div>

            <!-- 悬浮详情覆盖层 -->
            <div class="hover-overlay">
              <div class="hover-content">
                <div class="asset-info">
                  <p class="single-line-ellipsis"><strong>资产编号:</strong> <span :title="asset.assetNo">{{ asset.assetNo }}</span></p>
                  <p class="single-line-ellipsis"><strong>CI名称:</strong> <span :title="asset.ciName">{{ asset.ciName }}</span></p>
                  <p class="single-line-ellipsis"><strong>BU:</strong> <span :title="asset.businessUser ?? ''">{{ asset.businessUser ?? '' }}</span></p>
                  <p class="single-line-ellipsis"><strong>PM负责人:</strong> <span :title="asset.pmOwner">{{ asset.pmOwner }}</span></p>
                  <p class="description"><strong>描述:</strong> <span :title="asset.description">{{ asset.description || '暂无描述' }}</span></p>
                </div>
              </div>

              <!-- 右侧操作按钮 -->
              <div class="hover-actions">
                <n-button
                  size="small"
                  type="primary"
                  circle
                  @click.stop="handleCardClick(asset)"
                  class="action-btn"
                >
                  <template #icon>
                    <n-icon :component="DatabaseOutlined" />
                  </template>
                </n-button>
                <n-button
                  size="small"
                  type="error"
                  circle
                  @click.stop="handleDelete(asset)"
                  class="action-btn"
                >
                  <template #icon>
                    <n-icon :component="DeleteOutlined" />
                  </template>
                </n-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="pagination.total > 0">
        <n-pagination
          v-model:page="searchQuery.page"
          v-model:page-size="searchQuery.pageSize"
          :item-count="pagination.total"
          :page-sizes="[12, 24, 48, 96]"
          show-size-picker
          show-quick-jumper
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 资产详情/编辑弹窗 -->
    <SystemAssetModal
      v-model:show="showModal"
      :asset="selectedAsset"
      :mode="modalMode"
      @save="handleSave"
      @cancel="handleCancel"
      @edit="handleEditFromModal"
    />

    <!-- 导入弹窗 -->
    <ImportModal
      v-model:show="showImportModal"
      title="导入系统资产"
      modal-preset="dialog"
      modal-class="import-modal"
      incremental-tooltip="如果资产编号已存在，则跳过该条记录"
      overwrite-tooltip="如果资产编号已存在，则覆盖更新该条记录"
      :on-download-template="handleDownloadTemplate"
      :on-import="handleImportData"
      @success="handleImportSuccess"
      @cancel="handleImportCancel"
    />
  </page-layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import {
  NInput, NButton, NSelect, NIcon, NTag, NPagination
} from 'naive-ui'
import {
  SearchOutlined, ReloadOutlined, PlusOutlined,
  DeleteOutlined, DatabaseOutlined, UploadOutlined, ExportOutlined
} from '@vicons/antd'
import PageLayout from '@/layouts/PageLayout.vue'
import SystemAssetModal from '@/components/system/SystemAssetModal.vue'
import ImportModal from '@/components/common/ImportModal.vue'
import { useSystemStore } from '@/stores/system'
import { useImport } from '@/composables/useImport'
import { useSystemOptions } from '@/composables/useSystemOptions'
import { systemApi } from '@/apis/system'
import type {
  SystemAsset, SystemAssetQuery
} from '@/types/system'
import { useDialog } from 'naive-ui'

const dialog = useDialog()

// 使用系统 Store
const systemStore = useSystemStore()

const systemOptions = useSystemOptions()

// 从 Store 获取数据
const assets = computed(() => systemStore.assets)
const loading = computed(() => systemStore.loading)
const pagination = computed(() => systemStore.pagination)
const regionOptions = computed(() => systemOptions.regionOptions.value)
const businessDepartmentOptions = computed(() => systemOptions.businessDepartmentOptions.value)
const assetStateOptions = computed(() => systemOptions.assetStateOptions.value)

// 响应式数据
const showModal = ref(false)
const showImportModal = ref(false)
const selectedAsset = ref<SystemAsset | null>(null)
const modalMode = ref<'view' | 'edit' | 'create'>('view')

// 搜索查询参数
const searchQuery = reactive<SystemAssetQuery>({
  keyword: '',
  region: undefined,
  businessDepartment: undefined,
  assetState: undefined,
  page: 1,
  pageSize: 12
})

// 获取资产图片
const getAssetImage = (_asset: SystemAsset) => {
  // 这里可以根据资产类型返回不同的默认图片
  switch(_asset.region) {
    case 'Tianjin': return '/images/system-asset-blue.svg';
    case 'Dalian': return '/images/system-asset-babyBlue.svg';
    case 'Anhui': return '/images/system-asset-cinerous.svg';
    default: return '/images/system-asset-blue.svg';
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/default-fallback.svg'
}

// 获取资产状态类型
const getAssetStateType = (usageStatus?: string) => {
  switch (usageStatus) {
    case '已上线': return 'success'
    case '开发中': return 'warning'
    case '停用': return 'default'
    default: return 'default'
  }
}

// 搜索处理
const handleSearch = async () => {
  searchQuery.page = 1
  await systemStore.fetchAssets(searchQuery)
}

// 重置搜索
const handleReset = async () => {
  Object.assign(searchQuery, {
    keyword: '',
    region: undefined,
    businessDepartment: undefined,
    assetState: undefined,
    page: 1,
    pageSize: 12
  })
  await systemStore.fetchAssets(searchQuery)
}

// 创建资产
const handleCreate = () => {
  selectedAsset.value = null
  modalMode.value = 'create'
  showModal.value = true
}

// 卡片点击
const handleCardClick = (asset: SystemAsset) => {
  selectedAsset.value = asset
  modalMode.value = 'view'
  showModal.value = true
}

// 处理从弹窗中切换到编辑模式
const handleEditFromModal = () => {
  modalMode.value = 'edit'
  // 不需要重新打开弹窗，因为已经在弹窗中了
  // selectedAsset.value 已经在查看时设置了
}

// 删除资产
const handleDelete = async (asset: SystemAsset) => {
  dialog.warning({
    title: '删除确认',
    content: `确定要删除资产 "${asset.projectName}" 吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await systemStore.deleteAsset(asset.id)
      } catch (error) {
        console.error('删除资产失败:', error)
      }
    }
  })
}

// 分页处理
const handlePageChange = async (page: number) => {
  searchQuery.page = page
  systemStore.setPagination(page, searchQuery.pageSize || 12)
  await systemStore.fetchAssets(searchQuery)
}

const handlePageSizeChange = async (pageSize: number) => {
  searchQuery.pageSize = pageSize
  searchQuery.page = 1
  systemStore.setPagination(1, pageSize)
  await systemStore.fetchAssets(searchQuery)
}

// 清理表单数据，移除空字符串和空对象
const cleanFormData = (data: any) => {
  const cleaned: any = {}

  // 处理基本字段
  Object.keys(data).forEach(key => {
    if (key === 'extraProperties') {
      debugger
      // 特殊处理 extraProperties
      if (data[key] && typeof data[key] === 'object') {
        const cleanedExtra: any = {}
        Object.keys(data[key]).forEach(extraKey => {
          if (data[key][extraKey] && typeof data[key][extraKey] === 'object') {
            const cleanedSubObj: any = {}
            Object.keys(data[key][extraKey]).forEach(subKey => {
              const value = data[key][extraKey][subKey]
              if (value !== null && value !== undefined && value !== '') {
                cleanedSubObj[subKey] = value
              }
            })
            if (Object.keys(cleanedSubObj).length > 0) {
              cleanedExtra[extraKey] = cleanedSubObj
            }
          }
        })
        if (Object.keys(cleanedExtra).length > 0) {
          cleaned[key] = cleanedExtra
        }
      }
    } else {
      // 处理其他字段
      const value = data[key]
      if (value !== null && value !== undefined && value !== '') {
        cleaned[key] = value
      }
    }
  })

  return cleaned
}

// 保存处理
const handleSave = async (formData: any) => {
  try {
    // 清理表单数据
    // const cleanedData = cleanFormData(formData)

    if (modalMode.value === 'create') {
      // 创建新资产
      await systemStore.createAsset(formData)
    } else if (modalMode.value === 'edit') {
      // 更新现有资产
      await systemStore.updateAsset({
        id: selectedAsset.value?.id,
        ...formData
      })
    }

    showModal.value = false
    await systemStore.fetchAssets(searchQuery)
  } catch (error) {
    console.error('保存资产失败:', error)
    // 错误处理已在 Store 中完成，这里不需要额外处理
  }
}

// 取消处理
const handleCancel = () => {
  showModal.value = false
}



// 导出数据
const handleExport = async () => {
  try {
    await systemStore.exportAssets(searchQuery)
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

// 导入资产
const handleImport = () => {
  showImportModal.value = true
}

// 导入成功处理
const handleImportSuccess = async () => {
  showImportModal.value = false
  // 刷新资产列表
  await systemStore.fetchAssets(searchQuery)
}

// 导入取消处理
const handleImportCancel = () => {
  showImportModal.value = false
}

// 下载模板处理
const handleDownloadTemplate = async () => {
  try {
    await systemStore.downloadTemplate()
  } catch (error) {
    console.error('下载模板失败:', error)
  }
}

// 导入数据处理（SSE方式）
const handleImportData = async (data: { file: File, mode: 'incremental' | 'overwrite' }) => {
  return new Promise((resolve) => {
    const { startSSEImport, handleSSEProgress, handleSSEComplete, handleSSEError } = useImport()

    startSSEImport()

    systemApi.importAssets(
      data.file,
      data.mode,
      (progressData: any) => {
        console.log('系统资产导入进度:', progressData)
        handleSSEProgress(progressData)
      },
      (result: any) => {
        console.log('系统资产导入完成回调被调用:', result)
        handleSSEComplete(result)
        resolve(result)
      },
      (error: any) => {
        console.log('系统资产导入错误回调被调用:', error)
        handleSSEError(error)
        // 返回一个错误结果而不是抛出异常
        resolve({
          total: 0,
          success: 0,
          failed: 1,
          skipped: 0,
          errors: [{
            row: 0,
            field: '系统错误',
            message: error.message || '导入失败，请稍后重试'
          }]
        })
      }
    ).catch((error: any) => {
      console.error('导入失败:', error)
      // 返回一个错误结果而不是抛出异常
      resolve({
        total: 0,
        success: 0,
        failed: 1,
        skipped: 0,
        errors: [{
          row: 0,
          field: '系统错误',
          message: error instanceof Error ? error.message : '导入失败，请稍后重试'
        }]
      })
    })
  })
}

// 初始化数据
const initData = async () => {
  try {
    await Promise.all([
      systemStore.fetchAssets(searchQuery),
      _loadOptions()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

const _loadOptions = async () => {
  try {
    systemOptions.loadRegionOptions();
    systemOptions.loadBusinessDepartmentOptions();
    systemOptions.loadAssetStateOptions();
  } catch (error) {
    
  }
}

onMounted(() => {
  initData()
})
</script>

<style lang="less" scoped>
.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-left {
  display: flex;
  gap: 12px;
  align-items: center;
  flex: 1;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 150px;
}

.search-right {
  display: flex;
  gap: 8px;
}

.cards-container {
  flex: 1;
  overflow: auto;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.asset-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.asset-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2);
  border: 1px solid rgba(24, 144, 255, 0.3);
}

.asset-card:hover .card-actions {
  opacity: 1;
  visibility: visible;
}

.card-image {
  height: 140px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 标题覆盖层 - 始终显示 */
.title-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16px 12px 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.title-overlay .card-title {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.status-tag {
  margin-left: 8px;
  flex-shrink: 0;
}

.region-tag {
  position: absolute;
  top: 8px;
  left: 8px;
}

/* 悬浮详情覆盖层 */
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: space-between;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 16px;
  border-radius: 12px;
}

.asset-card:hover .hover-overlay {
  opacity: 1;
  visibility: visible;
}

.asset-card:hover .title-overlay {
  opacity: 0;
}

.hover-content {
  color: white;
  flex: 1;
  padding-right: 16px;
  min-width: 0;
}

.asset-info p {
  margin: 4px 0;
  font-size: 12px;
  line-height: 1.3;
  color: rgba(255, 255, 255, 0.9);
  text-align: left;
}

.asset-info .description {
  margin-top: 8px;
  font-size: 11px;
  opacity: 0.8;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.hover-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  padding: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2d3d;
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-fields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.field-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.field-label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

.field-value {
  font-size: 14px;
  color: #1f2d3d;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



.card-actions {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  gap: 6px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

.single-line-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
