<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="modalTitle"
    style="width: 600px"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="字典类型" path="dictType">
        <n-select
          v-model:value="formData.dictType"
          placeholder="请选择字典类型"
          :options="dictTypeOptions"
          :disabled="mode === 'edit'"
        />
      </n-form-item>
      
      <n-form-item label="字典代码" path="dictCode">
        <n-input
          v-model:value="formData.dictCode"
          placeholder="请输入字典代码"
          :disabled="mode === 'edit'"
        />
      </n-form-item>
      
      <n-form-item label="字典名称" path="dictName">
        <n-input
          v-model:value="formData.dictName"
          placeholder="请输入字典名称"
        />
      </n-form-item>
      
      <n-form-item label="描述">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入描述"
          :rows="3"
        />
      </n-form-item>
      
      <n-form-item label="排序号">
        <n-input-number
          v-model:value="formData.sortOrder"
          placeholder="请输入排序号"
          :min="0"
          style="width: 100%"
        />
      </n-form-item>
      
      <n-form-item label="启用状态" path="enabled">
        <n-switch v-model:value="formData.enabled" />
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ mode === 'create' ? '创建' : '更新' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { SystemDictApi, type SystemDict, type CreateSystemDictParams, type UpdateSystemDictParams } from '@/apis/systemDict'

interface Props {
  show: boolean
  mode: 'create' | 'edit'
  dictData?: Partial<SystemDict>
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  dictData: () => ({})
})

const emit = defineEmits<Emits>()

const message = useMessage()
const formRef = ref<FormInst>()
const submitting = ref(false)

// 双向绑定显示状态
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 模态框标题
const modalTitle = computed(() => {
  return props.mode === 'create' ? '新增系统字典' : '编辑系统字典'
})

// 表单数据
const formData = reactive<Partial<SystemDict>>({
  dictType: '',
  dictCode: '',
  dictName: '',
  description: '',
  sortOrder: 0,
  enabled: true
})

// 字典类型选项
const dictTypeOptions = [
  { label: '区域', value: 'region' },
  { label: '资产状态', value: 'asset_state' },
  { label: '业务部门', value: 'business_department' },
  { label: '使用状态', value: 'usage_status' },
  { label: '优先级', value: 'priority' },
  { label: '站点', value: 'site' }
]

// 表单验证规则
const rules: FormRules = {
  dictType: [
    { required: true, message: '请选择字典类型', trigger: 'change' }
  ],
  dictCode: [
    { required: true, message: '请输入字典代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '字典代码只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  dictName: [
    { required: true, message: '请输入字典名称', trigger: 'blur' }
  ]
}

// 监听字典数据变化，更新表单
watch(() => props.dictData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      dictType: newData.dictType || '',
      dictCode: newData.dictCode || '',
      dictName: newData.dictName || '',
      description: newData.description || '',
      sortOrder: newData.sortOrder || 0,
      enabled: newData.enabled !== undefined ? newData.enabled : true
    })
  }
}, { immediate: true })

// 监听模态框显示状态，重置表单
watch(() => props.show, (show) => {
  if (show && props.mode === 'create') {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    dictType: '',
    dictCode: '',
    dictName: '',
    description: '',
    sortOrder: 0,
    enabled: true
  })
  formRef.value?.restoreValidation()
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const validationResult = await formRef.value?.validate()
    if (validationResult && Array.isArray(validationResult) && validationResult.length > 0) {
      // 验证失败，显示第一个错误信息
      const firstError = validationResult[0]
      if (firstError && firstError.message) {
        message.error(firstError.message)
      } else {
        message.error('表单验证失败，请检查输入内容')
      }
      return
    }

    submitting.value = true

    if (props.mode === 'create') {
      const createData: CreateSystemDictParams = {
        dictType: formData.dictType!,
        dictCode: formData.dictCode!,
        dictName: formData.dictName!,
        description: formData.description,
        sortOrder: formData.sortOrder || 0,
        enabled: formData.enabled!
      }
      await SystemDictApi.createSystemDict(createData)
      message.success('创建成功')
    } else {
      const updateData: UpdateSystemDictParams = {
        id: props.dictData!.id!,
        dictType: formData.dictType!,
        dictCode: formData.dictCode!,
        dictName: formData.dictName!,
        description: formData.description,
        sortOrder: formData.sortOrder || 0,
        enabled: formData.enabled!
      }
      await SystemDictApi.updateSystemDict(updateData)
      message.success('更新成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)

    // 更详细的错误处理
    if (error && typeof error === 'object') {
      if ('response' in error && error.response) {
        const response = error.response as any
        if (response.data && response.data.message) {
          message.error(response.data.message)
        } else if (response.status === 400) {
          message.error('请求参数错误，请检查输入内容')
        } else if (response.status === 500) {
          message.error('服务器内部错误，请稍后重试')
        } else {
          message.error(`请求失败: ${response.status}`)
        }
      } else if ('message' in error) {
        message.error(error.message as string)
      } else {
        message.error('操作失败，请稍后重试')
      }
    } else {
      message.error('操作失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

// 取消
const handleCancel = () => {
  showModal.value = false
}
</script>
