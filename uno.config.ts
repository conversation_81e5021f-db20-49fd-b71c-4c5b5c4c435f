import { defineConfig, presetUno, presetAttributify, presetIcons } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
  ],
  shortcuts: {
    // 基础布局
    'flex-center': 'flex justify-center items-center',
    'flex-between': 'flex justify-between items-center',

    // 基础工具
    'text-ellipsis': 'overflow-hidden text-ellipsis whitespace-nowrap',
  },
  theme: {
    colors: {
      primary: 'var(--primary-color)',
      text: 'var(--text-color)',
      'text-secondary': 'var(--text-color-secondary)',
      border: 'var(--border-color)',
    },
  },
})
