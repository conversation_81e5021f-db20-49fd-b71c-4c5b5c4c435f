package com.vwatj.ppms.agent.function;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.vwatj.ppms.dto.TaskQueryDTO;
import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.service.ProjectService;
import com.vwatj.ppms.service.ProjectTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务管理功能实现
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskManagementFunction implements BusinessFunction {

    private final ProjectTaskService projectTaskService;
    private final ProjectService projectService;

    @Override
    public String getFunctionName() {
        return "task_management";
    }

    @Override
    public String getFunctionDescription() {
        return "任务管理功能，支持查询任务信息、获取任务统计、分析任务状态等";
    }

    @Override
    public Map<String, Object> getParameterSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        // action参数
        Map<String, Object> actionParam = new HashMap<>();
        actionParam.put("type", "string");
        actionParam.put("description", "操作类型：query_task, get_stats, get_my_tasks, query_project_tasks");
        actionParam.put("enum", List.of("query_task", "get_stats", "get_my_tasks", "query_project_tasks"));
        properties.put("action", actionParam);

        // taskId参数
        Map<String, Object> taskIdParam = new HashMap<>();
        taskIdParam.put("type", "integer");
        taskIdParam.put("description", "任务ID（查询特定任务时使用）");
        properties.put("taskId", taskIdParam);

        // projectId参数
        Map<String, Object> projectIdParam = new HashMap<>();
        projectIdParam.put("type", "integer");
        projectIdParam.put("description", "项目ID（获取项目任务统计时使用）");
        properties.put("projectId", projectIdParam);

        // assignee参数
        Map<String, Object> assigneeParam = new HashMap<>();
        assigneeParam.put("type", "string");
        assigneeParam.put("description", "任务负责人（查询个人任务时使用）");
        properties.put("assignee", assigneeParam);

        // queryType参数
        Map<String, Object> queryTypeParam = new HashMap<>();
        queryTypeParam.put("type", "string");
        queryTypeParam.put("description", "查询类型：all, by_id, by_name, by_priority, by_status, by_project");
        queryTypeParam.put("enum", List.of("all", "by_id", "by_name", "by_priority", "by_status", "by_project"));
        properties.put("queryType", queryTypeParam);

        // taskName参数
        Map<String, Object> taskNameParam = new HashMap<>();
        taskNameParam.put("type", "string");
        taskNameParam.put("description", "任务名称（按名称查询时使用）");
        properties.put("taskName", taskNameParam);

        // priority参数
        Map<String, Object> priorityParam = new HashMap<>();
        priorityParam.put("type", "string");
        priorityParam.put("description", "任务优先级：high, medium, low");
        priorityParam.put("enum", List.of("high", "medium", "low"));
        properties.put("priority", priorityParam);

        // status参数
        Map<String, Object> statusParam = new HashMap<>();
        statusParam.put("type", "string");
        statusParam.put("description", "任务状态：todo, in_progress, completed, paused");
        statusParam.put("enum", List.of("todo", "in_progress", "completed", "paused"));
        properties.put("status", statusParam);

        schema.put("properties", properties);
        schema.put("required", List.of("action"));

        return schema;
    }

    @Override
    public Object execute(Map<String, Object> parameters) {
        try {
            String action = (String) parameters.get("action");
            log.info("执行任务管理功能: action={}", action);

            switch (action) {
                case "query_task":
                    return queryTask(parameters);
                case "get_stats":
                    return getTaskStats(parameters);
                case "get_my_tasks":
                    return getMyTasks(parameters);
                case "query_project_tasks":
                    return queryProjectTasksInternal(parameters);
                default:
                    return Map.of("error", "不支持的操作类型: " + action);
            }
        } catch (Exception e) {
            log.error("执行任务管理功能失败", e);
            return Map.of("error", "执行失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateParameters(Map<String, Object> parameters) {
        if (parameters == null || !parameters.containsKey("action")) {
            return false;
        }

        String action = (String) parameters.get("action");
        if (action == null || action.trim().isEmpty()) {
            return false;
        }

        // 验证特定操作的参数
        switch (action) {
            case "query_task":
                // query_task操作总是有效的，因为支持查询全部
                return true;
            case "get_stats":
                return true; // 可以查询全部统计或特定项目统计
            case "get_my_tasks":
                return parameters.containsKey("assignee");
            case "query_project_tasks":
                return parameters.containsKey("projectId")||parameters.containsKey("projectName");
            default:
                return false;
        }
    }

    /**
     * 查询任务信息
     */
    private Object queryTask(Map<String, Object> parameters) {
        try {
            String queryType = (String) parameters.getOrDefault("queryType", "all");
            log.info("查询任务: queryType={}, parameters={}", queryType, parameters);

            List<ProjectTask> tasks;
            String message;

            switch (queryType) {
                case "by_id":
                    tasks = queryTaskById(parameters);
                    message = "按ID查询任务完成";
                    break;
                case "by_name":
                    tasks = queryTaskByName(parameters);
                    message = "按名称查询任务完成";
                    break;
                case "by_priority":
                    tasks = queryTaskByPriority(parameters);
                    message = "按优先级查询任务完成";
                    break;
                case "by_status":
                    tasks = queryTaskByStatus(parameters);
                    message = "按状态查询任务完成";
                    break;
                case "by_project":
                    // 使用统一的项目任务查询逻辑
                    Object projectTaskResult = queryProjectTasksInternal(parameters);
                    if (projectTaskResult instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> resultMap = (Map<String, Object>) projectTaskResult;
                        if ((Boolean) resultMap.get("success")) {
                            @SuppressWarnings("unchecked")
                            List<ProjectTask> projectTasks = (List<ProjectTask>) resultMap.get("tasks");
                            tasks = projectTasks;
                            message = "按项目查询任务完成";
                        } else {
                            // 如果查询失败，返回空列表
                            tasks = List.of();
                            message = (String) resultMap.get("message");
                        }
                    } else {
                        tasks = List.of();
                        message = "按项目查询任务失败";
                    }
                    break;
                case "all":
                default:
                    tasks = queryAllTasks();
                    message = "查询全部任务完成";
                    break;
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("tasks", tasks);
            result.put("total", tasks.size());
            result.put("queryType", queryType);
            result.put("message", message);

            // 添加简单的统计信息
            if (tasks.size() > 0) {
                Map<String, Long> statusCount = tasks.stream()
                        .collect(java.util.stream.Collectors.groupingBy(
                                t -> t.getStatus() != null ? t.getStatus().toString() : "unknown",
                                java.util.stream.Collectors.counting()
                        ));
                result.put("statusCount", statusCount);

                Map<String, Long> priorityCount = tasks.stream()
                        .collect(java.util.stream.Collectors.groupingBy(
                                t -> t.getPriority() != null ? t.getPriority().toString() : "unknown",
                                java.util.stream.Collectors.counting()
                        ));
                result.put("priorityCount", priorityCount);
            }

            return result;
        } catch (Exception e) {
            log.error("查询任务失败", e);
            return Map.of("success", false, "message", "查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    private Object getTaskStats(Map<String, Object> parameters) {
        try {
            Long projectId = Long.valueOf(parameters.get("projectId").toString());
            Map<String, Object> stats = projectTaskService.getTaskStats(projectId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("stats", stats);
            result.put("message", "获取任务统计成功");

            return result;
        } catch (Exception e) {
            log.error("获取任务统计失败", e);
            return Map.of("success", false, "message", "获取统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的任务
     */
    private Object getMyTasks(Map<String, Object> parameters) {
        try {
            String assignee = (String) parameters.get("assignee");

            TaskQueryDTO queryDTO = new TaskQueryDTO();
            queryDTO.setPage(1L);
            queryDTO.setPageSize(10L);

            var myTasks = projectTaskService.getMyTasks(assignee, queryDTO);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("tasks", myTasks);
            result.put("message", "获取我的任务成功");

            return result;
        } catch (Exception e) {
            log.error("获取我的任务失败", e);
            return Map.of("success", false, "message", "获取失败: " + e.getMessage());
        }
    }

    /**
     * 查询全部任务
     */
    private List<ProjectTask> queryAllTasks() {
        log.info("查询全部任务");
        return projectTaskService.list();
    }

    /**
     * 按ID查询任务
     */
    private List<ProjectTask> queryTaskById(Map<String, Object> parameters) {
        Long taskId = Long.valueOf(parameters.get("taskId").toString());
        log.info("按ID查询任务: taskId={}", taskId);

        ProjectTask task = projectTaskService.getById(taskId);
        if (task != null) {
            return List.of(task);
        }
        return List.of();
    }

    /**
     * 按名称查询任务
     */
    private List<ProjectTask> queryTaskByName(Map<String, Object> parameters) {
        String taskName = (String) parameters.get("taskName");
        log.info("按名称查询任务: taskName={}", taskName);

        // 支持模糊查询
        LambdaQueryWrapper<ProjectTask> qw = new LambdaQueryWrapper<ProjectTask>().like(ProjectTask::getTitle, "%" + taskName + "%")
                .or().like(ProjectTask::getDescription, "%" + taskName + "%");
        return projectTaskService.list(qw);
    }

    /**
     * 按优先级查询任务
     */
    private List<ProjectTask> queryTaskByPriority(Map<String, Object> parameters) {
        String priority = (String) parameters.get("priority");
        log.info("按优先级查询任务: priority={}", priority);

        // 支持模糊查询
        LambdaQueryWrapper<ProjectTask> qw = new LambdaQueryWrapper<ProjectTask>().like(ProjectTask::getPriority, "%" + priority + "%");
        return projectTaskService.list(qw);
    }

    /**
     * 按状态查询任务
     */
    private List<ProjectTask> queryTaskByStatus(Map<String, Object> parameters) {
        String status = (String) parameters.get("status");
        log.info("按状态查询任务: status={}", status);

        // 支持模糊查询
        LambdaQueryWrapper<ProjectTask> qw = new LambdaQueryWrapper<ProjectTask>().like(ProjectTask::getStatus, "%" + status + "%");
        return projectTaskService.list(qw);
    }

    /**
     * 查询项目任务（统一的内部方法）
     * 支持通过项目ID或项目名称查询，返回完整的结果信息
     */
    private Object queryProjectTasksInternal(Map<String, Object> parameters) {
        try {
            List<Long> projectIds = new ArrayList<>();
            String queryInfo = "";

            // 优先使用项目ID
            if (parameters.containsKey("projectId")) {
                Long projectId = Long.valueOf(parameters.get("projectId").toString());
                projectIds.add(projectId);
                queryInfo = "项目ID: " + projectId;
                log.info("查询项目任务: projectId={}", projectId);
            }
            // 如果没有项目ID，尝试通过项目名称查询
            else if (parameters.containsKey("projectName")) {
                String projectName = (String) parameters.get("projectName");
                log.info("通过项目名称查询项目任务: projectName={}", projectName);

                // 先查询项目信息获取项目ID
                List<Project> projects = queryProjectsByName(projectName);
                if (projects.isEmpty()) {
                    return Map.of("success", false, "message", "未找到名称包含 '" + projectName + "' 的项目");
                }

                // 获取所有匹配项目的ID
                projectIds = projects.stream().map(Project::getId).collect(java.util.stream.Collectors.toList());
                queryInfo = "项目名称: " + projectName + " (匹配到 " + projects.size() + " 个项目)";
                log.info("找到匹配项目: count={}, projectIds={}", projects.size(), projectIds);
            }
            // 如果都没有，返回错误
            else {
                return Map.of("success", false, "message", "请提供项目ID或项目名称");
            }

            // 查询所有匹配项目的任务
            List<ProjectTask> allTasks = new ArrayList<>();
            for (Long projectId : projectIds) {
                LambdaQueryWrapper<ProjectTask> qw = new LambdaQueryWrapper<ProjectTask>().eq(ProjectTask::getProjectId, projectId);
                List<ProjectTask> tasks = projectTaskService.list(qw);
                allTasks.addAll(tasks);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("tasks", allTasks);
            result.put("total", allTasks.size());
            result.put("projectIds", projectIds);
            result.put("queryInfo", queryInfo);
            result.put("message", "查询项目任务完成");

            // 添加项目任务的统计信息
            if (allTasks.size() > 0) {
                Map<String, Long> statusCount = allTasks.stream()
                        .collect(java.util.stream.Collectors.groupingBy(
                                t -> t.getStatus() != null ? t.getStatus().toString() : "unknown",
                                java.util.stream.Collectors.counting()
                        ));
                result.put("statusCount", statusCount);

                Map<String, Long> priorityCount = allTasks.stream()
                        .collect(java.util.stream.Collectors.groupingBy(
                                t -> t.getPriority() != null ? t.getPriority().toString() : "unknown",
                                java.util.stream.Collectors.counting()
                        ));
                result.put("priorityCount", priorityCount);

                // 计算任务完成率
                long completedTasks = allTasks.stream()
                        .mapToLong(t -> "completed".equals(t.getStatus() != null ? t.getStatus().toString() : "") ? 1 : 0)
                        .sum();
                double completionRate = (double) completedTasks / allTasks.size() * 100;
                result.put("completionRate", Math.round(completionRate * 100.0) / 100.0);

                // 按项目分组统计
                Map<Long, Long> projectTaskCount = allTasks.stream()
                        .collect(java.util.stream.Collectors.groupingBy(
                                ProjectTask::getProjectId,
                                java.util.stream.Collectors.counting()
                        ));
                result.put("projectTaskCount", projectTaskCount);
            }

            return result;
        } catch (Exception e) {
            log.error("查询项目任务失败", e);
            return Map.of("success", false, "message", "查询失败: " + e.getMessage());
        }
    }

    /**
     * 通过项目名称查询项目列表
     */
    private List<Project> queryProjectsByName(String projectName) {
        log.info("通过项目名称查询项目: projectName={}", projectName);

        // 支持模糊查询
        LambdaQueryWrapper<Project> qw = new LambdaQueryWrapper<Project>()
                .like(Project::getName, "%" + projectName + "%")
                .or()
                .like(Project::getDescription, "%" + projectName + "%");

        List<Project> projects = projectService.list(qw);
        log.info("找到匹配项目数量: {}", projects.size());

        return projects;
    }
}
