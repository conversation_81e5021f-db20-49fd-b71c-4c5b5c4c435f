<template>
  <PageLayout title="数据设置">
    <template #header>
      <div class="page-header">
        <h1 class="page-title">字典设置</h1>
        <p class="page-description">管理系统字典、任务类型和项目类型</p>
      </div>
    </template>

    <n-card>
      <n-tabs v-model:value="activeTab" type="line" animated>
        <!-- 系统字典 -->
        <n-tab-pane name="systemDict" tab="系统字典">
          <SystemDictTable />
        </n-tab-pane>

        <!-- 任务类型 -->
        <n-tab-pane name="taskCategory" tab="任务类型">
          <TaskCategoryTable />
        </n-tab-pane>

        <!-- 项目类型 -->
        <n-tab-pane name="projectCategory" tab="项目类型">
          <ProjectCategoryTable />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PageLayout from '@/layouts/PageLayout.vue'
import SystemDictTable from '@/components/setting/SystemDictTable.vue'
import TaskCategoryTable from '@/components/setting/TaskCategoryTable.vue'
import ProjectCategoryTable from '@/components/setting/ProjectCategoryTable.vue'

// 当前激活的标签页
const activeTab = ref('systemDict')
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

:deep(.n-tabs-nav) {
  margin-bottom: 24px;
}

:deep(.n-tab-pane) {
  padding: 0;
}
</style>
