package com.vwatj.ppms.agent.function;

import java.util.Map;

/**
 * 业务功能接口
 * 定义Agent可调用的业务功能
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface BusinessFunction {
    
    /**
     * 获取功能名称
     */
    String getFunctionName();
    
    /**
     * 获取功能描述
     */
    String getFunctionDescription();
    
    /**
     * 获取功能参数定义
     */
    Map<String, Object> getParameterSchema();
    
    /**
     * 执行功能
     * 
     * @param parameters 参数
     * @return 执行结果
     */
    Object execute(Map<String, Object> parameters);
    
    /**
     * 验证参数
     * 
     * @param parameters 参数
     * @return 是否有效
     */
    boolean validateParameters(Map<String, Object> parameters);
}
