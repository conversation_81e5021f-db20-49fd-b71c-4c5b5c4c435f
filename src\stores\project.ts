// 项目状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { projectApi, type ProjectQueryParams, type CreateProjectParams, type UpdateProjectParams } from '@/apis'
import type { Project, PaginationResponse } from '@/types'
import { showMessage } from '@/utils/errorHandler'
import { createProjectWithProgress } from '@/utils/project'

export const useProjectStore = defineStore('project', () => {

  // 状态
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 12,
    total: 0
  })

  // 计算属性
  const starredProjects = computed(() => projects.value.filter(p => p.starred))
  const archivedProjects = computed(() => projects.value.filter(p => p.archived))
  const activeProjects = computed(() => projects.value.filter(p => !p.archived))
  const inProgressProjects = computed(() => projects.value.filter(p => ['development', 'inner test', 'uat'].includes(p.status)))
  const completedProjects = computed(() => projects.value.filter(p => p.status === 'go live'))

  // 获取项目列表
  const fetchProjects = async (params?: ProjectQueryParams) => {
    try {
      loading.value = true
      const response = await projectApi.getProjects({
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...params
      })
      
      // 确保数组字段不为 null，并添加计算的进度
      projects.value = response.data.data.map((project: Project) => {
        const processedProject = {
          ...project,
          tags: Array.isArray(project.tags) ? project.tags : [],
          teamMembers: Array.isArray(project.teamMembers) ? project.teamMembers : []
        }
        return createProjectWithProgress(processedProject)
      })
      pagination.value.total = response.data.total
      pagination.value.page = response.data.page
      pagination.value.pageSize = response.data.pageSize
    } catch (error: any) {
      showMessage('error', error.message || '获取项目列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取项目详情
  const fetchProject = async (id: number | string) => {
    try {
      loading.value = true
      const response = await projectApi.getProject(id)
      // 确保数组字段不为 null，并添加计算的进度
      const processedProject = {
        ...response.data,
        tags: Array.isArray(response.data.tags) ? response.data.tags : [],
        teamMembers: Array.isArray(response.data.teamMembers) ? response.data.teamMembers : []
      }
      const project = createProjectWithProgress(processedProject)
      currentProject.value = project
      return project
    } catch (error: any) {
      showMessage('error', error.message || '获取项目详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建项目
  const createProject = async (data: CreateProjectParams) => {
    try {
      loading.value = true
      const response = await projectApi.createProject(data)
      // 确保数组字段不为 null，并添加计算的进度
      const processedProject = {
        ...response.data,
        tags: Array.isArray(response.data.tags) ? response.data.tags : [],
        teamMembers: Array.isArray(response.data.teamMembers) ? response.data.teamMembers : []
      }
      const project = createProjectWithProgress(processedProject)
      projects.value.unshift(project)
      pagination.value.total += 1
      showMessage('success', '项目创建成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '创建项目失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新项目
  const updateProject = async (data: UpdateProjectParams) => {
    try {
      loading.value = true
      const response = await projectApi.updateProject(data)

      // 确保数组字段不为 null，并添加计算的进度
      const processedProject = {
        ...response.data,
        tags: Array.isArray(response.data.tags) ? response.data.tags : [],
        teamMembers: Array.isArray(response.data.teamMembers) ? response.data.teamMembers : []
      }
      const project = createProjectWithProgress(processedProject)

      // 更新列表中的项目
      const index = projects.value.findIndex(p => p.id === data.id)
      if (index !== -1) {
        projects.value[index] = project
      }

      // 更新当前项目
      if (currentProject.value?.id === data.id) {
        currentProject.value = project
      }
      
      showMessage('success', '项目更新成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '更新项目失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除项目
  const deleteProject = async (id: number | string) => {
    try {
      loading.value = true
      await projectApi.deleteProject(id)

      // 从列表中移除
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value.splice(index, 1)
        pagination.value.total -= 1
      }

      // 清空当前项目
      if (currentProject.value?.id === id) {
        currentProject.value = null
      }

      showMessage('success', '项目删除成功')
    } catch (error: any) {
      showMessage('error', error.message || '删除项目失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 切换关注状态
  const toggleStar = async (id: number | string) => {
    try {
      const response = await projectApi.toggleStar(id)
      
      // 更新列表中的项目
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        // 创建新的项目对象以确保响应式更新
        projects.value[index] = {
          ...projects.value[index],
          starred: response.data.starred
        }
      }
      
      // 更新当前项目
      if (currentProject.value?.id === id) {
        currentProject.value.starred = response.data.starred
      }
      
      showMessage('success', response.data.starred ? '已关注' : '已取消关注')
      return response.data.starred
    } catch (error: any) {
      showMessage('error', error.message || '操作失败')
      throw error
    }
  }

  // 切换归档状态
  const toggleArchive = async (id: number | string) => {
    try {
      const response = await projectApi.toggleArchive(id)

      // 更新列表中的项目
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        // 创建新的项目对象以确保响应式更新
        projects.value[index] = {
          ...projects.value[index],
          archived: response.data.archived
        }
      }

      // 更新当前项目
      if (currentProject.value?.id === id) {
        currentProject.value.archived = response.data.archived
      }

      showMessage('success', response.data.archived ? '已归档' : '已取消归档')
      return response.data.archived
    } catch (error: any) {
      showMessage('error', error.message || '操作失败')
      throw error
    }
  }

  // 更新项目状态
  const updateProjectStatus = async (id: number | string, status: string) => {
    try {
      const response = await projectApi.updateStatus(id, status as any)

      // 更新列表中的项目
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index] = response.data
      }

      // 更新当前项目
      if (currentProject.value?.id === id) {
        currentProject.value = response.data
      }

      showMessage('success', '状态更新成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '状态更新失败')
      throw error
    }
  }

  // 上传项目封面
  const uploadCover = async (id: number | string, file: File) => {
    try {
      loading.value = true
      const response = await projectApi.uploadCover(id, file)

      // 更新列表中的项目
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index].cover = response.data.coverUrl
      }

      // 更新当前项目
      if (currentProject.value?.id === id) {
        currentProject.value.cover = response.data.coverUrl
      }

      showMessage('success', '封面上传成功')
      return response.data.coverUrl
    } catch (error: any) {
      showMessage('error', error.message || '封面上传失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.page = page
    pagination.value.pageSize = pageSize
  }

  // 重置状态
  const reset = () => {
    projects.value = []
    currentProject.value = null
    loading.value = false
    pagination.value = {
      page: 1,
      pageSize: 12,
      total: 0
    }
  }

  return {
    // 状态
    projects,
    currentProject,
    loading,
    pagination,
    
    // 计算属性
    starredProjects,
    archivedProjects,
    activeProjects,
    inProgressProjects,
    completedProjects,
    
    // 方法
    fetchProjects,
    fetchProject,
    createProject,
    updateProject,
    deleteProject,
    toggleStar,
    toggleArchive,
    updateProjectStatus,
    uploadCover,
    setPagination,
    reset
  }
})
