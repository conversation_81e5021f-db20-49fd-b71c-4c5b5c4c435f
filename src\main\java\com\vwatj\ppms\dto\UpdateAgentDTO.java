package com.vwatj.ppms.dto;

import lombok.Data;

import jakarta.validation.constraints.Size;

/**
 * 更新Agent DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class UpdateAgentDTO {
    
    /**
     * Agent ID
     */
    private Long id;
    
    /**
     * Agent名称
     */
    @Size(min = 2, max = 50, message = "Agent名称长度必须在2-50个字符之间")
    private String name;
    
    /**
     * Agent描述
     */
    @Size(max = 200, message = "描述不能超过200个字符")
    private String description;
    
    /**
     * Agent状态 (0-禁用, 1-启用)
     */
    private Integer status;
    
    /**
     * Agent类型
     */
    private String type;
    
    /**
     * Agent配置信息 (JSON格式)
     */
    private String config;

    /**
     * LLM提供商
     */
    private String llmProvider;

    /**
     * LLM模型名称
     */
    private String llmModel;

    /**
     * LLM API URL
     */
    private String llmApiUrl;

    /**
     * LLM API Key
     */
    private String llmApiKey;

    /**
     * LLM参数配置
     */
    private String llmParams;

    /**
     * 系统提示词
     */
    private String systemPrompt;

    /**
     * 用户提示词模板
     */
    private String userPromptTemplate;

    /**
     * 功能配置
     */
    private String functionConfig;

    /**
     * 实现类名（自定义类型Agent使用）
     */
    private String implementationClass;
}
