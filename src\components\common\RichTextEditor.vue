<template>
  <div class="rich-text-editor">
    <QuillEditor
      ref="quillRef"
      v-model:content="content"
      :options="editorOptions"
      :placeholder="placeholder"
      :disabled="disabled"
      content-type="html"
      @update:content="handleContentChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 字数统计 -->
    <div v-if="showWordCount" class="word-count" :class="wordCountClass">
      <span v-if="maxLength > 0">
        {{ wordCount }} / {{ maxLength }}
      </span>
      <span v-else>
        {{ wordCount }} 字
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  height?: string | number
  maxHeight?: string | number
  toolbar?: 'essential' | 'minimal' | 'full' | Array<any>
  theme?: 'snow' | 'bubble'
  showWordCount?: boolean
  maxLength?: number
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'blur', event: any): void
  (e: 'focus', event: any): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  disabled: false,
  height: '200px',
  maxHeight: '400px',
  toolbar: 'essential',
  theme: 'snow',
  showWordCount: false,
  maxLength: 0
})

const emit = defineEmits<Emits>()

const quillRef = ref()
const content = ref(props.modelValue)

// 工具栏配置
const toolbarConfigs = {
  minimal: [
    ['bold', 'italic', 'underline'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    ['link']
  ],
  essential: [
    ['bold', 'italic', 'underline', 'strike'],
    ['blockquote', 'code-block'],
    [{ 'header': 1 }, { 'header': 2 }],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'script': 'sub'}, { 'script': 'super' }],
    [{ 'indent': '-1'}, { 'indent': '+1' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'align': [] }],
    ['link', 'image'],
    ['clean']
  ],
  full: [
    [{ 'font': [] }],
    [{ 'size': ['small', false, 'large', 'huge'] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'script': 'sub'}, { 'script': 'super' }],
    [{ 'header': '1'}, { 'header': '2'}, 'blockquote', 'code-block'],
    [{ 'list': 'ordered'}, { 'list': 'bullet'}, { 'indent': '-1'}, { 'indent': '+1' }],
    [{ 'direction': 'rtl' }, { 'align': [] }],
    ['link', 'image', 'video', 'formula'],
    ['clean']
  ]
}

// 编辑器配置
const editorOptions = computed(() => ({
  theme: props.theme,
  placeholder: props.placeholder,
  modules: {
    toolbar: Array.isArray(props.toolbar) ? props.toolbar : toolbarConfigs[props.toolbar]
  }
}))

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    content.value = newValue || ''
  }
}, { immediate: true })

// 处理内容变化
const handleContentChange = (value: string) => {
  console.log('RichTextEditor内容变化:', value)
  console.log('RichTextEditor内容长度:', value?.length)
  content.value = value
  emit('update:modelValue', value)
  emit('change', value)
}

// 处理失焦事件
const handleBlur = (event: any) => {
  emit('blur', event)
}

// 处理聚焦事件
const handleFocus = (event: any) => {
  emit('focus', event)
}

// 获取编辑器实例
const getQuill = () => {
  return quillRef.value?.getQuill()
}

// 获取纯文本内容
const getText = () => {
  return quillRef.value?.getText()
}

// 计算字数
const wordCount = computed(() => {
  if (!content.value) return 0
  const text = content.value.replace(/<[^>]*>/g, '').trim()
  return text.length
})

// 字数统计样式类
const wordCountClass = computed(() => {
  if (!props.maxLength || props.maxLength <= 0) return ''
  const ratio = wordCount.value / props.maxLength
  if (ratio >= 1) return 'word-count-error'
  if (ratio >= 0.8) return 'word-count-warning'
  return ''
})

// 获取HTML内容
const getHTML = () => {
  return quillRef.value?.getHTML()
}

// 设置内容
const setContent = (content: string, type: 'html' | 'text' = 'html') => {
  if (type === 'html') {
    quillRef.value?.setHTML(content)
  } else {
    quillRef.value?.setText(content)
  }
}

// 清空内容
const clear = () => {
  quillRef.value?.setHTML('')
}

// 聚焦编辑器
const focus = () => {
  quillRef.value?.focus()
}

// 暴露方法给父组件
defineExpose({
  getQuill,
  getText,
  getHTML,
  setContent,
  clear,
  focus
})
</script>

<style lang="less" scoped>
.rich-text-editor {
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #40a9ff;
  }

  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :deep(.ql-container) {
    min-height: v-bind(height);
    max-height: v-bind(maxHeight);
    font-size: 14px;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
  }

  :deep(.ql-editor) {
    min-height: v-bind(height);
    max-height: v-bind(maxHeight);
    padding: 12px 15px;
    overflow-y: auto;
    flex: 1;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #d1d5db;
      border-radius: 3px;

      &:hover {
        background: #9ca3af;
      }
    }

    // Firefox 滚动条样式
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;

    &.ql-blank::before {
      font-style: normal;
      color: #c0c4cc;
    }

    p {
      margin: 0 0 8px 0;
      
      &:last-child {
        margin-bottom: 0;
      }
    }

    ul, ol {
      margin: 8px 0;
      padding-left: 20px;
    }

    blockquote {
      border-left: 4px solid #1890ff;
      margin: 8px 0;
      padding: 8px 12px;
      background-color: #f6f8fa;
    }

    code {
      background-color: #f1f3f4;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 85%;
    }

    pre {
      background-color: #f6f8fa;
      border-radius: 6px;
      padding: 12px;
      margin: 8px 0;
      overflow-x: auto;
    }
  }

  :deep(.ql-toolbar) {
    border-bottom: 1px solid #e8e8e8;
    padding: 8px;

    .ql-formats {
      margin-right: 12px;
    }

    button {
      width: 28px;
      height: 28px;
      border-radius: 4px;
      
      &:hover {
        background-color: #f5f5f5;
      }

      &.ql-active {
        background-color: #e6f7ff;
        color: #1890ff;
      }
    }

    .ql-picker {
      .ql-picker-label {
        border-radius: 4px;
        padding: 4px 8px;
        
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }

  // 禁用状态样式
  &.disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;

    :deep(.ql-toolbar) {
      pointer-events: none;
      opacity: 0.6;
    }

    :deep(.ql-editor) {
      background-color: #f5f5f5;
      cursor: not-allowed;
    }
  }
}

// 字数统计样式
.word-count {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 12px;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 3px;
  pointer-events: none;

  &.word-count-warning {
    color: #fa8c16;
  }

  &.word-count-error {
    color: #ff4d4f;
  }
}
</style>
