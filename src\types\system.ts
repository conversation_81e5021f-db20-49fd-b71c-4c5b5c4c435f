// 系统资产数据类型定义

export interface SystemAsset {
  // 主要字段 (系统信息tab)
  id: number
  assetNo: string
  projectName: string
  shortCode: string
  ciName: string
  businessDepartment: string
  pmOwner: string
  description: string
  assetState: string
  region: string
  retireDate: string
  // 应用信息字段
  appId: string
  onlineDate: string
  mail: string
  version: string
  language: string
  userCount: number
  usageStatus: string
  businessUser: string

  // 额外属性 - 其他信息tab (动态JSON字段)
  extraProperties: {
    serverInfo?: ServerInfo
    [key: string]: any
  }

  // 创建和更新时间
  createdAt?: string
  updatedAt?: string
}

// 系统资产创建/编辑表单数据
export interface SystemAssetForm {
  assetNo: string
  projectName: string
  shortCode: string
  businessDepartment: string
  pmOwner: string
  description: string
  assetState: string
  region: string
  retireDate: string | null
  // 应用信息字段
  onlineDate: string | null
  mail: string
  version: string
  language: string
  userCount: number
  usageStatus: string
  businessUser: string
  extraProperties: SystemAsset['extraProperties']
}

// 系统资产查询参数
export interface SystemAssetQuery {
  keyword?: string
  region?: string
  businessDepartment?: string
  assetState?: string
  page?: number
  pageSize?: number
}

// 系统资产列表响应
export interface SystemAssetListResponse {
  data: SystemAsset[]
  total: number
  page: number
  pageSize: number
}

// 主要选项卡配置
export interface MainTab {
  key: string
  label: string
  icon?: string
}

export const MAIN_TABS: MainTab[] = [
  { key: 'systemInfo', label: '系统信息' },
  { key: 'otherInfo', label: '其他信息' }
]

// 服务器信息字段
export interface ServerInfo {
  platform?: string
  prodAppServer?: string
  prodDbName?: string
  prodDbServer?: string
  prodWebsite?: string
  prodAccountList?: []
  qaAppServer?: string
  qaDbName?: string
  qaDbServer?: string
  qaWebsite?: string
  qaAccountList?: []
}