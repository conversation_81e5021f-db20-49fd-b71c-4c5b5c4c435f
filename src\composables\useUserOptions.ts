import { ref } from 'vue'
import { UserApi } from '@/apis/user'
import { useMessage } from 'naive-ui'

export interface UserOption {
  label: string
  value: string
}

/**
 * 用户选项composable
 * 统一供项目经理、任务报告人/指派人、系统资产PM负责人等字段使用
 */
export function useUserOptions() {
  const message = useMessage()
  const userOptions = ref<UserOption[]>([])
  const loading = ref(false)
  const loaded = ref(false)

  // 获取用户选项
  const fetchUserOptions = async () => {
    if (loading.value) return userOptions.value
    
    try {
      loading.value = true
      const response = await UserApi.getUserOptions()
      userOptions.value = response.data
      loaded.value = true
      return userOptions.value
    } catch (error) {
      console.error('获取用户选项失败:', error)
      message.error('获取用户选项失败')
      return []
    } finally {
      loading.value = false
    }
  }

  // 重新加载用户选项
  const reloadUserOptions = async () => {
    loaded.value = false
    return await fetchUserOptions()
  }



  return {
    userOptions,
    loading,
    loaded,
    fetchUserOptions,
    reloadUserOptions
  }
}
