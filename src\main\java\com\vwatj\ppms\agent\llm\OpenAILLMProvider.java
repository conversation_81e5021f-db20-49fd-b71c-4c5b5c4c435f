package com.vwatj.ppms.agent.llm;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.vwatj.ppms.dto.ChatRequestDTO;
import com.vwatj.ppms.dto.ChatResponseDTO;
import com.vwatj.ppms.entity.Agent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OpenAI兼容的LLM提供商实现
 * 支持OpenAI、Azure OpenAI、以及其他兼容OpenAI API的服务
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Component
public class OpenAILLMProvider extends LLMProvider {

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public String getProviderName() {
        return "openai";
    }

    @Override
    public boolean supports(String provider) {
        return "openai".equalsIgnoreCase(provider) || 
               "azure".equalsIgnoreCase(provider) ||
               "qwen".equalsIgnoreCase(provider) ||
               "zhipu".equalsIgnoreCase(provider);
    }

    @Override
    public ChatResponseDTO chat(Agent agent, ChatRequestDTO request) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("发送OpenAI兼容聊天请求: agentId={}, provider={}, sessionId={}", 
                    agent.getId(), agent.getLlmProvider(), request.getSessionId());

            // 构建请求体
            Map<String, Object> requestBody = buildRequestBody(agent, request);
            
            // 设置请求头
            HttpHeaders headers = buildHeaders(agent);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            String url = buildApiUrl(agent);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            
            // 解析响应
            return parseResponse(agent, request, response.getBody(), startTime);
            
        } catch (Exception e) {
            log.error("OpenAI兼容聊天请求失败: agentId={}, provider={}", 
                    agent.getId(), agent.getLlmProvider(), e);
            return buildErrorResponse(agent, request, e.getMessage(), startTime);
        }
    }

    @Override
    public String streamChat(Agent agent, ChatRequestDTO request) {
        try {
            log.info("发送OpenAI兼容流式聊天请求: agentId={}, provider={}, sessionId={}", 
                    agent.getId(), agent.getLlmProvider(), request.getSessionId());

            // 构建请求体
            Map<String, Object> requestBody = buildRequestBody(agent, request);
            requestBody.put("stream", true);
            
            // 设置请求头
            HttpHeaders headers = buildHeaders(agent);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            String url = buildApiUrl(agent);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            
            // 简化处理，实际应该处理SSE流
            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            return jsonResponse.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .getString("content");
            
        } catch (Exception e) {
            log.error("OpenAI兼容流式聊天请求失败: agentId={}, provider={}", 
                    agent.getId(), agent.getLlmProvider(), e);
            return "抱歉，处理您的请求时遇到了问题：" + e.getMessage();
        }
    }

    @Override
    public boolean validateConfig(Agent agent) {
        try {
            if (agent.getLlmApiKey() == null || agent.getLlmApiKey().isEmpty()) {
                return false;
            }
            if (agent.getLlmApiUrl() == null || agent.getLlmApiUrl().isEmpty()) {
                return false;
            }
            if (agent.getLlmModel() == null || agent.getLlmModel().isEmpty()) {
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("验证OpenAI兼容配置失败: agentId={}, provider={}", 
                    agent.getId(), agent.getLlmProvider(), e);
            return false;
        }
    }

    /**
     * 构建API URL
     */
    private String buildApiUrl(Agent agent) {
        String baseUrl = agent.getLlmApiUrl();
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        
        // 根据不同提供商构建不同的URL
        switch (agent.getLlmProvider().toLowerCase()) {
            case "azure":
                return baseUrl + "/openai/deployments/" + agent.getLlmModel() + "/chat/completions?api-version=2023-12-01-preview";
            default:
                return baseUrl + "/chat/completions";
        }
    }

    /**
     * 构建请求头
     */
    private HttpHeaders buildHeaders(Agent agent) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 根据不同提供商设置不同的认证方式
        switch (agent.getLlmProvider().toLowerCase()) {
            case "azure":
                headers.set("api-key", agent.getLlmApiKey());
                break;
            default:
                headers.setBearerAuth(agent.getLlmApiKey());
                break;
        }
        
        return headers;
    }

    /**
     * 构建请求体
     */
    private Map<String, Object> buildRequestBody(Agent agent, ChatRequestDTO request) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // 基本参数
        requestBody.put("model", agent.getLlmModel());
        
        // 构建消息列表
        List<Map<String, String>> messages = new ArrayList<>();
        
        // 添加系统消息
        if (agent.getSystemPrompt() != null && !agent.getSystemPrompt().isEmpty()) {
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", agent.getSystemPrompt());
            messages.add(systemMessage);
        }
        
        // 添加历史消息
        if (request.getHistory() != null) {
            for (ChatRequestDTO.ChatMessageDTO historyMsg : request.getHistory()) {
                Map<String, String> message = new HashMap<>();
                message.put("role", historyMsg.getRole());
                message.put("content", historyMsg.getContent());
                messages.add(message);
            }
        }
        
        // 添加用户消息
        Map<String, String> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", buildUserPrompt(agent, request));
        messages.add(userMessage);
        
        requestBody.put("messages", messages);
        
        // 添加LLM参数
        LLMParams params = parseLLMParams(agent);
        requestBody.put("temperature", params.getTemperature());
        requestBody.put("max_tokens", params.getMaxTokens());
        requestBody.put("top_p", params.getTopP());
        
        return requestBody;
    }

    /**
     * 解析响应
     */
    private ChatResponseDTO parseResponse(Agent agent, ChatRequestDTO request, 
                                        String responseBody, long startTime) {
        try {
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            
            ChatResponseDTO response = new ChatResponseDTO();
            response.setAgentId(agent.getId());
            response.setAgentName(agent.getName());
            response.setSessionId(request.getSessionId());
            response.setTimestamp(System.currentTimeMillis());
            response.setResponseTime(System.currentTimeMillis() - startTime);
            
            // 提取消息内容
            String content = jsonResponse.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .getString("content");
            response.setMessage(content);
            
            // 提取token使用量
            if (jsonResponse.containsKey("usage")) {
                JSONObject usage = jsonResponse.getJSONObject("usage");
                response.setTokensUsed(usage.getInteger("total_tokens"));
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("解析OpenAI兼容响应失败: agentId={}, provider={}", 
                    agent.getId(), agent.getLlmProvider(), e);
            return buildErrorResponse(agent, request, "解析响应失败: " + e.getMessage(), startTime);
        }
    }

    /**
     * 构建错误响应
     */
    private ChatResponseDTO buildErrorResponse(Agent agent, ChatRequestDTO request, 
                                             String error, long startTime) {
        ChatResponseDTO response = new ChatResponseDTO();
        response.setAgentId(agent.getId());
        response.setAgentName(agent.getName());
        response.setSessionId(request.getSessionId());
        response.setTimestamp(System.currentTimeMillis());
        response.setResponseTime(System.currentTimeMillis() - startTime);
        response.setError(error);
        response.setMessage("抱歉，处理您的请求时遇到了问题，请稍后再试。");
        
        return response;
    }
}
