// 任务相关 API 接口
import { httpClient } from './index'
import type {
  Task,
  TaskStatus,
  ApiResponse,
  PaginationParams,
  PaginationResponse
} from '@/types'
import { SSE } from 'sse.js'

// 任务查询参数
export interface TaskQueryParams extends PaginationParams {
  keyword?: string
  projectId?: number | string
  status?: TaskStatus
  priority?: string
  issueType?: string
  assignee?: string
  reporter?: string
  plannedStartTime?: number
  plannedEndTime?: number
  actualStartTime?: number
  actualEndTime?: number
  // 新增筛选参数
  taskScope?: 'all' | 'my' | 'unassigned' | 'todo'
  currentUser?: string
  statusList?: TaskStatus[]
  dateRangeStart?: number
  dateRangeEnd?: number
  weekRangeStart?: number
  weekRangeEnd?: number
}

// 任务创建参数
export interface CreateTaskParams {
  title: string
  description?: string
  issueType: string
  assignee: string
  reporter: string
  status: TaskStatus
  priority: string
  projectId?: number | string
  plannedStartTime?: number
  plannedEndTime?: number
  duration?: number
  stageName?: string
}

// 任务更新参数
export interface UpdateTaskParams extends Partial<CreateTaskParams> {
  id: number | string
  actualStartTime?: number
  actualEndTime?: number
}



// 任务操作参数
export interface TaskOperationParams {
  id: number | string
  operation: string
  targetStatus?: string
  comment?: string
}

// 任务状态流转路径
export interface TaskTransitions {
  [operation: string]: string // operation -> targetStatus
}

// 任务操作信息
export interface TaskOperation {
  operation: string
  name: string
  targetStatus: string
  description?: string
}

// 任务统计信息
export interface TaskStats {
  total: number
  byStatus: Record<TaskStatus, number>
  byPriority: Record<string, number>
  byIssueType: Record<string, number>
  toVerify: number
  completed: number
}

// 任务 API 类
export class TaskApi {
  private static readonly BASE_PATH = '/tasks'

  // 解析SSE事件数据的通用方法
  private static parseEventData(eventData: any): any {
    if (typeof eventData === 'string') {
      if (eventData.trim() === '') {
        return { total: 0, success: 0, failed: 0, skipped: 0 }
      }
      return JSON.parse(eventData)
    } else if (typeof eventData === 'object' && eventData !== null) {
      return eventData
    }
    return { total: 0, success: 0, failed: 0, skipped: 0 }
  }

  /**
   * 高级搜索任务（新接口）
   */
  static async searchTasks(params?: TaskQueryParams): Promise<ApiResponse<PaginationResponse<Task>>> {
    // 构建请求体
    const requestBody = params ? {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
      keyword: params.keyword,
      projectId: params.projectId,
      status: params.status,
      priority: params.priority,
      issueType: params.issueType,
      assignee: params.assignee,
      reporter: params.reporter,
      taskScope: params.taskScope || 'all',
      currentUser: params.currentUser,
      statusList: params.statusList,
      dateRangeStart: params.dateRangeStart,
      dateRangeEnd: params.dateRangeEnd,
      weekRangeStart: params.weekRangeStart,
      weekRangeEnd: params.weekRangeEnd,
    } : {
      page: 1,
      pageSize: 10,
      taskScope: 'all'
    }

    return httpClient.post<PaginationResponse<Task>>(`${this.BASE_PATH}/search`, requestBody)
  }

  /**
   * 获取任务列表（兼容旧接口）
   */
  static async getTasks(params?: TaskQueryParams): Promise<ApiResponse<PaginationResponse<Task>>> {
    // 直接调用新的搜索接口
    return this.searchTasks(params)
  }

  /**
   * 获取项目任务列表
   */
  static async getProjectTasks(projectId: number | string, params?: Omit<TaskQueryParams, 'projectId'>): Promise<ApiResponse<PaginationResponse<Task>>> {
    // 使用新的搜索接口
    const searchParams: TaskQueryParams = {
      page: 1,
      pageSize: 10,
      ...params,
      projectId
    }
    return this.searchTasks(searchParams)
  }

  /**
   * 获取任务详情
   */
  static async getTask(id: number | string): Promise<ApiResponse<Task>> {
    return httpClient.get<Task>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建任务
   */
  static async createTask(data: CreateTaskParams): Promise<ApiResponse<Task>> {
    return httpClient.post<Task>(this.BASE_PATH, data)
  }

  /**
   * 更新任务
   */
  static async updateTask(data: UpdateTaskParams): Promise<ApiResponse<Task>> {
    const { id, ...updateData } = data
    return httpClient.put<Task>(`${this.BASE_PATH}/${id}`, updateData)
  }

  /**
   * 删除任务
   */
  static async deleteTask(id: number | string): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/${id}`)
  }





  /**
   * 分配任务
   */
  static async assignTask(id: number | string, assignee: string): Promise<ApiResponse<Task>> {
    return httpClient.patch<Task>(`${this.BASE_PATH}/${id}/assign`, { assignee })
  }

  /**
   * 开始任务
   */
  static async startTask(id: number | string): Promise<ApiResponse<Task>> {
    return httpClient.patch<Task>(`${this.BASE_PATH}/${id}/start`)
  }

  /**
   * 完成任务
   */
  static async completeTask(id: number | string, comment?: string): Promise<ApiResponse<Task>> {
    return httpClient.patch<Task>(`${this.BASE_PATH}/${id}/complete`, { comment })
  }

  /**
   * 执行任务操作
   */
  static async executeTaskOperation(params: TaskOperationParams): Promise<ApiResponse<string>> {
    const { id, ...data } = params
    return httpClient.post<string>(`${this.BASE_PATH}/${id}/operation`, data)
  }

  /**
   * 获取任务状态流转路径
   */
  static async getTaskTransitions(id: number | string): Promise<ApiResponse<TaskTransitions>> {
    return httpClient.get<TaskTransitions>(`${this.BASE_PATH}/${id}/transitions`)
  }

  /**
   * 检查任务操作是否允许
   */
  static async isOperationAllowed(id: number | string, operation: string): Promise<ApiResponse<boolean>> {
    return httpClient.get<boolean>(`${this.BASE_PATH}/${id}/operation/${operation}/allowed`)
  }

  /**
   * 获取指定状态的可用操作
   */
  static async getAvailableOperations(status: string): Promise<ApiResponse<TaskOperation[]>> {
    return httpClient.get<TaskOperation[]>(`${this.BASE_PATH}/status/${status}/operations`)
  }

  /**
   * 获取任务统计信息
   */
  static async getTaskStats(projectId?: number | string): Promise<ApiResponse<TaskStats>> {
    const config = projectId ? { params: { projectId } } : undefined
    return httpClient.get<TaskStats>(`${this.BASE_PATH}/stats`, config)
  }

  /**
   * 获取我的任务
   */
  static async getMyTasks(params?: Omit<TaskQueryParams, 'assignee'>): Promise<ApiResponse<PaginationResponse<Task>>> {
    // 使用新的搜索接口
    const searchParams: TaskQueryParams = {
      page: 1,
      pageSize: 10,
      ...params,
      taskScope: 'my'
    }
    return this.searchTasks(searchParams)
  }

  /**
   * 获取未分配的任务
   */
  static async getUnassignedTasks(params?: TaskQueryParams): Promise<ApiResponse<PaginationResponse<Task>>> {
    const searchParams: TaskQueryParams = {
      page: 1,
      pageSize: 10,
      ...params,
      taskScope: 'unassigned'
    }
    return this.searchTasks(searchParams)
  }

  /**
   * 获取我的待办任务
   */
  static async getTodoTasks(currentUser: string, params?: TaskQueryParams): Promise<ApiResponse<PaginationResponse<Task>>> {
    const searchParams: TaskQueryParams = {
      page: 1,
      pageSize: 10,
      ...params,
      taskScope: 'todo',
      currentUser
    }
    return this.searchTasks(searchParams)
  }

  /**
   * 获取任务时间线
   */
  static async getTaskTimeline(id: number | string): Promise<ApiResponse<Array<{
    id: string
    action: string
    user: string
    timestamp: number
    comment?: string
    oldValue?: any
    newValue?: any
  }>>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/timeline`)
  }

  /**
   * 复制任务
   */
  static async duplicateTask(id: number | string, title: string): Promise<ApiResponse<Task>> {
    return httpClient.post<Task>(`${this.BASE_PATH}/${id}/duplicate`, { title })
  }

  /**
   * 导出任务数据或下载模板
   */
  static async exportTasks(params?: TaskQueryParams, isTemplate: boolean = false): Promise<void> {
    try {
      // 构建查询参数，过滤掉空值
      const queryParams = new URLSearchParams()

      // 添加模板参数
      queryParams.append('template', isTemplate.toString())

      // 只有在不是模板下载且有参数时才添加查询条件
      if (params && !isTemplate) {
        // 过滤并添加有效参数
        if (params.keyword && params.keyword.trim()) {
          queryParams.append('keyword', params.keyword.trim())
        }
        if (params.projectId !== undefined && params.projectId !== null) {
          queryParams.append('projectId', String(params.projectId))
        }
        if (params.status) {
          queryParams.append('status', params.status)
        }
        if (params.priority) {
          queryParams.append('priority', params.priority)
        }
        if (params.issueType) {
          queryParams.append('issueType', params.issueType)
        }
        if (params.assignee && params.assignee.trim()) {
          queryParams.append('assignee', params.assignee.trim())
        }
        if (params.reporter && params.reporter.trim()) {
          queryParams.append('reporter', params.reporter.trim())
        }
        if (params.page !== undefined && params.page !== null) {
          queryParams.append('page', String(params.page))
        }
        if (params.pageSize !== undefined && params.pageSize !== null) {
          queryParams.append('pageSize', String(params.pageSize))
        }
      }

      const response = await fetch(`/api${this.BASE_PATH}/export?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      const filename = contentDisposition
        ? decodeURIComponent(contentDisposition.split('filename=')[1]?.replace(/"/g, '') || '')
        : isTemplate ? '任务导入模板.xlsx' : `任务数据_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`

      // 下载文件
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)
    } catch (error) {
      console.error(isTemplate ? '下载模板失败:' : '导出数据失败:', error)
      throw error
    }
  }

  /**
   * 导入任务数据（SSE方式）
   */
  static async importTasks(
    file: File,
    mode: 'incremental' | 'overwrite',
    projectId?: number,
    projectName?: string,
    onProgress?: (data: any) => void,
    onComplete?: (data: any) => void,
    onError?: (error: any) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      // 准备表单数据
      const formData = new FormData()
      formData.append('file', file)
      formData.append('mode', mode)
      if (projectId) {
        formData.append('projectId', projectId.toString())
      }
      if (projectName) {
        formData.append('projectName', projectName)
      }

      const token = localStorage.getItem('token')

      // 构建SSE URL
      const sseUrl = new URL(`/api${this.BASE_PATH}/import`, window.location.origin)

      // 创建SSE连接
      const eventSource = new SSE(sseUrl.toString(), {
        method: 'POST',
        payload: formData,
        headers: {
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        }
      })

      console.log('创建任务SSE连接:', sseUrl.toString())

      // 监听进度事件
      eventSource.addEventListener('progress', (event: any) => {
        try {
          const data = this.parseEventData(event.data)
          if (data && onProgress) {
            onProgress(data)
          }
        } catch (e) {
          console.warn('解析进度事件失败:', e)
        }
      })

      // 监听完成事件
      eventSource.addEventListener('complete', (event: any) => {
        try {
          const data = this.parseEventData(event.data)
          if (onComplete) {
            onComplete(data)
          }
        } catch (e) {
          // 解析失败时使用默认数据
          const fallbackData = { total: 0, success: 0, failed: 0, skipped: 0, errors: [] }
          if (onComplete) {
            onComplete(fallbackData)
          }
        } finally {
          eventSource.close()
          resolve()
        }
      })

      // 监听错误事件
      eventSource.addEventListener('error', (event: any) => {
        try {
          const data = this.parseEventData(event.data)
          const errorMessage = data.message || '任务导入失败'
          if (onError) {
            onError(data)
          }
          eventSource.close()
          reject(new Error(errorMessage))
        } catch (e) {
          const fallbackError = { message: '任务导入过程中发生错误' }
          if (onError) {
            onError(fallbackError)
          }
          eventSource.close()
          reject(new Error('任务导入过程中发生错误'))
        }
      })

      // 监听连接错误
      eventSource.onerror = () => {
        eventSource.close()
        if (onError) {
          onError({ message: 'SSE连接失败' })
        }
        reject(new Error('SSE连接失败'))
      }
    })
  }


}

// 导出默认实例
export const taskApi = TaskApi
