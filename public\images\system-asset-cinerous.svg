<svg width="400" height="160" viewBox="0 0 400 160" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A708B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#383F48;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="160" fill="url(#gradient)"/>
  <g transform="translate(200, 80)">
    <circle cx="0" cy="0" r="30" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
    <path d="M-15 -10 L15 -10 L15 10 L-15 10 Z" fill="rgba(255,255,255,0.8)"/>
    <path d="M-10 -5 L10 -5 M-10 0 L10 0 M-10 5 L10 5" stroke="rgba(102,126,234,0.8)" stroke-width="1.5"/>
  </g>
</svg>
