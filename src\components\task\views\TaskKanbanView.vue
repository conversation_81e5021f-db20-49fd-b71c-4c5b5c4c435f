<script setup lang="ts">
import { computed, ref } from 'vue'
import { NDropdown, useDialog, useMessage } from 'naive-ui'
import { useEventListener, useToggle, useDocumentVisibility, useDebounceFn } from '@vueuse/core'
import type { Task, TaskTransitions, KanbanColumn } from '@/types/task'
import { KANBAN_COLUMNS, getIssueTypeInfo, getPriorityInfo, formatDate } from '@/types/task'
import { UserApi } from '@/apis/user'

interface Props {
  tasks: Task[]
  loading?: boolean
}

interface Emits {
  (e: 'edit', task: Task): void
  (e: 'copy', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'operationExecute', task: Task, operation: string): void
  (e: 'field-update', task: Task, field: keyof Task, value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 对话框实例
const dialog = useDialog()
const message = useMessage()

// 文档可见性检测 - 优化性能
const documentVisibility = useDocumentVisibility()

// 键盘事件处理 - 使用 VueUse
useEventListener('keydown', (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    clearSelection()
  }
})

// 拖拽相关状态
const draggedTask = ref<Task | null>(null)
const dragOverColumn = ref<string | null>(null)
const dragOverTask = ref<number | null>(null)
const dragCloneElement = ref<HTMLElement | null>(null)
const clickOffset = ref({ x: 0, y: 0 })

// 右键菜单状态 - 使用 VueUse useToggle
const [contextMenuVisible, toggleContextMenu] = useToggle(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const contextMenuTask = ref<Task | null>(null)

// 多选状态
const selectedTasks = ref<Set<number>>(new Set())
const lastSelectedTaskId = ref<number | null>(null)

// 分配相关状态
const showAssignModal = ref(false)
const assigningTasks = ref<Task[]>([])
const selectedAssignee = ref('')
const userOptions = ref<Array<{label: string, value: string}>>([])
const loadingUsers = ref(false)

// 右键菜单选项 - 根据选中数量动态生成
const contextMenuOptions = computed(() => {
  const selectedCount = selectedTasks.value.size

  if (selectedCount > 1) {
    // 多选时显示批量操作
    return [
      {
        label: `批量分配 (${selectedCount}个任务)`,
        key: 'batchAssign',
        icon: () => '👤'
      },
      {
        label: `批量删除 (${selectedCount}个任务)`,
        key: 'batchDelete',
        icon: () => '🗑️'
      }
    ]
  } else {
    // 单选时显示所有操作
    return [
      {
        label: '修改',
        key: 'edit',
        icon: () => '✏️'
      },
      {
        label: '复制',
        key: 'copy',
        icon: () => '📋'
      },
      {
        label: '分配',
        key: 'assign',
        icon: () => '👤'
      },
      {
        label: '删除',
        key: 'delete',
        icon: () => '🗑️'
      }
    ]
  }
})

// 按状态分组的任务
const tasksByStatus = computed(() => {
  const grouped: Record<string, Task[]> = {}
  KANBAN_COLUMNS.forEach(col => {
    grouped[col.status] = props.tasks.filter(task => task && task.status === col.status)
  })
  return grouped
})

// 获取固定的状态流转规则
const getTaskTransitions = (task: Task): TaskTransitions => {
  const { status, issueType } = task

  switch (status) {
    case 'open':
      // open可以转全部
      if (issueType === '任务') {
        return {
          'progress': 'inprogress',
          'resolve': 'toverify',
          'close': 'close'
        }
      } else {
        return {
          'progress': 'inprogress',
          'resolve': 'toverify',
        }
      }

    case 'inprogress':
      // inprogress可以转全部，但是如果类型不是任务，不能转close
      if (issueType === '任务') {
        return {
          'reopen': 'open',
          'resolve': 'toverify',
          'close': 'close'
        }
      } else {
        return {
          'reopen': 'open',
          'resolve': 'toverify'
        }
      }

    case 'toverify':
      // toverify可以转全部
      return {
        'reopen': 'open',
        'close': 'close'
      }

    case 'close':
      // close只能转open
      return {
        'reopen': 'open'
      }

    default:
      return {}
  }
}


// 获取拖拽到指定状态的操作
const getOperationForStatus = (task: Task, targetStatus: string): string | null => {
  const transitions = getTaskTransitions(task)
  for (const [operation, status] of Object.entries(transitions)) {
    if (status === targetStatus) {
      return operation
    }
  }
  return null
}

// 获取任务的所有可拖拽状态
const getAvailableDropStatuses = (task: Task): string[] => {
  const transitions = getTaskTransitions(task)
  return Object.values(transitions).filter(status => status !== task.status)
}

// 当前拖拽任务的可拖拽状态列表
const currentTaskAvailableStatuses = ref<string[]>([])

// 增强的看板列配置（包含拖拽状态）
const enhancedKanbanColumns = computed(() => {
  return KANBAN_COLUMNS.map(col => ({
    ...col,
    allowDrop: draggedTask.value ? currentTaskAvailableStatuses.value.includes(col.status) : false,
    isDragOver: dragOverColumn.value === col.status,
    isCurrentColumn: draggedTask.value?.status === col.status,
    isBlocked: draggedTask.value ? !currentTaskAvailableStatuses.value.includes(col.status) && col.status !== draggedTask.value.status : false
  }))
})

// 事件处理
const handleDelete = (task: Task) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除任务"${task.title}"吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: () => {
      emit('delete', task)
    }
  })
}
const handleEdit = (task: Task) => emit('edit', task)
const handleCopy = (task: Task) => emit('copy', task)
const handleOperationExecute = (task: Task, operation: string) => emit('operationExecute', task, operation)

// 批量删除处理
const handleBatchDelete = () => {
  const selectedTaskIds = Array.from(selectedTasks.value)
  const selectedTaskList = props.tasks.filter(task => selectedTaskIds.includes(task.id))

  if (selectedTaskList.length === 0) return

  dialog.warning({
    title: '批量删除确认',
    content: `确定要删除选中的 ${selectedTaskList.length} 个任务吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: () => {
      // 触发批量删除事件
      selectedTaskList.forEach(task => {
        emit('delete', task)
      })
    }
  })
}

// 单个任务分配处理
const handleAssign = (task: Task) => {
  assigningTasks.value = [task]
  selectedAssignee.value = task.assignee || ''
  showAssignModal.value = true
  fetchUserOptions()
}

// 批量分配处理
const handleBatchAssign = () => {
  const selectedTaskIds = Array.from(selectedTasks.value)
  const selectedTaskList = props.tasks.filter(task => selectedTaskIds.includes(task.id))

  if (selectedTaskList.length === 0) return

  assigningTasks.value = selectedTaskList
  selectedAssignee.value = ''
  showAssignModal.value = true
  fetchUserOptions()
}

// 获取用户选项
const fetchUserOptions = async () => {
  try {
    loadingUsers.value = true
    const response = await UserApi.getUserOptions()
    if (response.code === 200 && response.data) {
      userOptions.value = response.data
    } else {
      console.error('获取用户选项失败:', response.message)
      message.error(response.message || '获取用户列表失败')
    }
  } catch (error: any) {
    console.error('获取用户列表失败:', error)
    message.error('获取用户列表失败')
  } finally {
    loadingUsers.value = false
  }
}

// 确认分配
const handleConfirmAssign = async () => {
  if (!selectedAssignee.value) {
    message.error('请选择指派人')
    return
  }

  try {
    // 批量更新任务的指派人
    for (const task of assigningTasks.value) {
      // 触发更新事件，由父组件处理实际的API调用
      emit('field-update', task, 'assignee', selectedAssignee.value)
    }

    message.success(`成功分配 ${assigningTasks.value.length} 个任务`)
    showAssignModal.value = false

    // 清除选中状态
    clearSelectionCore()
  } catch (error: any) {
    console.error('分配任务失败:', error)
    message.error('分配任务失败')
  }
}

// 取消分配
const handleCancelAssign = () => {
  showAssignModal.value = false
  assigningTasks.value = []
  selectedAssignee.value = ''
}

// 右键菜单处理 - 使用 VueUse toggle
const handleContextMenu = (event: MouseEvent, task: Task) => {
  event.preventDefault()
  event.stopPropagation()

  contextMenuTask.value = task
  contextMenuX.value = event.clientX
  contextMenuY.value = event.clientY
  toggleContextMenu(true)
}

const handleContextMenuSelect = (key: string) => {
  switch (key) {
    case 'edit':
      if (contextMenuTask.value) {
        handleEdit(contextMenuTask.value)
      }
      break
    case 'copy':
      if (contextMenuTask.value) {
        handleCopy(contextMenuTask.value)
      }
      break
    case 'assign':
      if (contextMenuTask.value) {
        handleAssign(contextMenuTask.value)
      }
      break
    case 'delete':
      if (contextMenuTask.value) {
        handleDelete(contextMenuTask.value)
      }
      break
    case 'batchAssign':
      handleBatchAssign()
      break
    case 'batchDelete':
      handleBatchDelete()
      break
  }

  toggleContextMenu(false)
  contextMenuTask.value = null
  // 批量操作后清除选中状态
  if (key === 'batchDelete') {
    clearSelectionCore()
  }
}

const handleContextMenuClickOutside = () => {
  toggleContextMenu(false)
  contextMenuTask.value = null
}

// 多选处理
const handleTaskClick = (event: MouseEvent, task: Task) => {
  if (event.shiftKey) {
    event.preventDefault()
    event.stopPropagation()

    if (lastSelectedTaskId.value === null) {
      // 如果没有上次选中的任务，直接选中当前任务
      selectedTasks.value.clear()
      selectedTasks.value.add(task.id)
      lastSelectedTaskId.value = task.id
    } else {
      // 获取当前任务所在的列
      const currentColumn = KANBAN_COLUMNS.find(col =>
        tasksByStatus.value[col.status]?.some(t => t.id === task.id)
      )

      if (currentColumn) {
        const columnTasks = tasksByStatus.value[currentColumn.status] || []
        const lastIndex = columnTasks.findIndex(t => t.id === lastSelectedTaskId.value)
        const currentIndex = columnTasks.findIndex(t => t.id === task.id)

        if (lastIndex !== -1 && currentIndex !== -1) {
          // 选择范围内的所有任务
          const startIndex = Math.min(lastIndex, currentIndex)
          const endIndex = Math.max(lastIndex, currentIndex)

          for (let i = startIndex; i <= endIndex; i++) {
            selectedTasks.value.add(columnTasks[i].id)
          }
        }
      }
    }
  } else if (event.ctrlKey || event.metaKey) {
    // Ctrl/Cmd + 点击切换选中状态
    event.preventDefault()
    event.stopPropagation()

    if (selectedTasks.value.has(task.id)) {
      selectedTasks.value.delete(task.id)
      if (lastSelectedTaskId.value === task.id) {
        lastSelectedTaskId.value = selectedTasks.value.size > 0 ?
          Array.from(selectedTasks.value)[0] : null
      }
    } else {
      selectedTasks.value.add(task.id)
      lastSelectedTaskId.value = task.id
    }
  } else {
    // 普通点击清除多选
    selectedTasks.value.clear()
    lastSelectedTaskId.value = null
  }
}

// 清除多选状态 - 使用防抖优化性能
const clearSelectionCore = () => {
  selectedTasks.value.clear()
  lastSelectedTaskId.value = null
}

// 防抖清除选择，避免频繁的状态更新
const clearSelection = useDebounceFn(clearSelectionCore, 100)



// 清除默认拖拽图像
const clearDefaultDragImage = (dataTransfer: DataTransfer) => {
  const img = new Image()
  img.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' %3E%3Cpath /%3E%3C/svg%3E"
  dataTransfer.setDragImage(img, 0, 0)
}

// 创建跟随鼠标的拖拽元素
const createDragClone = (originalElement: HTMLElement, event: DragEvent, taskCount: number = 1) => {
  const container = document.createElement('div')
  container.className = 'drag-clone-container'
  container.style.cssText = `
    position: fixed;
    pointer-events: none;
    z-index: 9999;
  `

  // 如果是多选，创建层叠效果
  if (taskCount > 1) {
    // 创建多个层叠的卡片
    const maxLayers = Math.min(taskCount, 4) // 最多显示4层

    for (let i = maxLayers - 1; i >= 0; i--) {
      const layer = document.createElement('div')
      layer.className = `drag-clone drag-layer-${i}`

      const offsetX = i * 6
      const offsetY = i * 6
      const scale = 1 - (i * 0.03) // 每层稍微缩小一点
      const opacity = 0.95 - (i * 0.15) // 后面的层稍微透明一点
      const rotation = (2 + i * 1.5) // 每层稍微旋转一点

      layer.style.cssText = `
        position: absolute;
        width: ${originalElement.offsetWidth}px;
        height: ${originalElement.offsetHeight}px;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: 0 ${6 + i * 3}px ${20 + i * 6}px rgba(0, 0, 0, ${0.2 + i * 0.08});
        transform: translate(${offsetX}px, ${offsetY}px) scale(${scale}) rotate(${rotation}deg);
        opacity: ${opacity};
        overflow: hidden;
        transition: none;
        --layer-scale: ${scale};
        --layer-rotate: ${rotation}deg;
      `

      if (i === 0) {
        // 最前面的卡片显示实际内容
        const clonedContent = originalElement.cloneNode(true) as HTMLElement
        clonedContent.style.transform = 'none'
        clonedContent.style.margin = '0'
        layer.appendChild(clonedContent)
      } else {
        // 后面的卡片显示简化内容
        const placeholder = document.createElement('div')
        placeholder.style.cssText = `
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          padding: 10px;
          box-sizing: border-box;
        `

        // 创建模拟的卡片内容
        const mockHeader = document.createElement('div')
        mockHeader.style.cssText = `
          height: 16px;
          background: linear-gradient(90deg, #dee2e6 0%, #f8f9fa 50%, #dee2e6 100%);
          border-radius: 4px;
          margin-bottom: 8px;
          animation: shimmer 1.5s ease-in-out infinite;
        `

        const mockContent = document.createElement('div')
        mockContent.style.cssText = `
          height: 12px;
          background: linear-gradient(90deg, #e9ecef 0%, #f8f9fa 50%, #e9ecef 100%);
          border-radius: 3px;
          margin-bottom: 6px;
          width: 80%;
          animation: shimmer 1.5s ease-in-out infinite 0.2s;
        `

        const mockFooter = document.createElement('div')
        mockFooter.style.cssText = `
          height: 10px;
          background: linear-gradient(90deg, #f1f3f4 0%, #f8f9fa 50%, #f1f3f4 100%);
          border-radius: 3px;
          width: 60%;
          animation: shimmer 1.5s ease-in-out infinite 0.4s;
        `

        placeholder.appendChild(mockHeader)
        placeholder.appendChild(mockContent)
        placeholder.appendChild(mockFooter)
        layer.appendChild(placeholder)
      }

      container.appendChild(layer)
    }

    // 添加数量标识
    const badge = document.createElement('div')
    badge.className = 'drag-count-badge'
    badge.textContent = taskCount.toString()
    badge.style.cssText = `
      position: absolute;
      top: -8px;
      right: -8px;
      background: #ff4d4f;
      color: white;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      font-weight: bold;
      border: 3px solid white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      z-index: 10;
    `
    container.appendChild(badge)
  } else {
    // 单个卡片的原有逻辑
    const clone = document.createElement('div')
    clone.className = 'drag-clone'
    clone.style.cssText = `
      position: absolute;
      width: ${originalElement.offsetWidth}px;
      height: ${originalElement.offsetHeight}px;
      opacity: 0.9;
      transform: rotate(5deg);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      border-radius: 8px;
      overflow: hidden;
    `

    const clonedContent = originalElement.cloneNode(true) as HTMLElement
    clonedContent.style.transform = 'none'
    clone.appendChild(clonedContent)
    container.appendChild(clone)
  }

  document.body.appendChild(container)

  // 计算点击偏移
  const rect = originalElement.getBoundingClientRect()
  clickOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }

  return container
}

// 更新拖拽元素位置 - 修复拖拽跟随问题
const updateDragClonePosition = (event: DragEvent) => {
  if (dragCloneElement.value && documentVisibility.value === 'visible') {
    // 使用事件的坐标而不是 useMouse，确保拖拽时准确跟随
    const clientX = event.clientX
    const clientY = event.clientY

    dragCloneElement.value.style.left = `${clientX - clickOffset.value.x}px`
    dragCloneElement.value.style.top = `${clientY - clickOffset.value.y}px`

    // 只在页面可见时添加摆动效果，优化性能
    const time = Date.now() * 0.003
    const wobbleX = Math.sin(time) * 2
    const wobbleY = Math.cos(time * 1.2) * 1
    dragCloneElement.value.style.transform = `translate(${wobbleX}px, ${wobbleY}px)`
  }
}



// 拖拽事件监听器 - 使用 VueUse
let dragOverListener: (() => void) | null = null
let dragEndListener: (() => void) | null = null

// 清理拖拽状态
const clearDragState = () => {
  if (dragCloneElement.value) {
    document.body.removeChild(dragCloneElement.value)
    dragCloneElement.value = null
  }

  // 恢复所有任务元素的透明度
  const allTaskElements = document.querySelectorAll('[data-task-id]')
  allTaskElements.forEach(el => {
    (el as HTMLElement).style.opacity = '1'
  })

  draggedTask.value = null
  dragOverColumn.value = null
  dragOverTask.value = null
  currentTaskAvailableStatuses.value = []

  // 清理事件监听器
  if (dragOverListener) {
    dragOverListener()
    dragOverListener = null
  }
  if (dragEndListener) {
    dragEndListener()
    dragEndListener = null
  }
}

// 增强的拖拽处理
const handleDragStart = (event: DragEvent, task: Task) => {
  if (!event.dataTransfer || !event.target) return

  const target = event.target as HTMLElement

  // 判断是否为多选拖拽
  const isMultiSelect = selectedTasks.value.has(task.id) && selectedTasks.value.size > 1

  let selectedTaskList: Task[]

  if (isMultiSelect) {
    // 多选拖拽：使用所有选中的任务
    selectedTaskList = props.tasks.filter(t => selectedTasks.value.has(t.id))
  } else {
    // 单个拖拽：只拖拽当前任务，不修改选中状态
    selectedTaskList = [task]
  }

  // 清除默认拖拽图像
  clearDefaultDragImage(event.dataTransfer)

  // 设置拖拽数据
  event.dataTransfer.setData('text/plain', JSON.stringify({
    tasks: selectedTaskList,
    isMultiple: isMultiSelect
  }))
  draggedTask.value = task

  // 创建跟随鼠标的拖拽元素
  dragCloneElement.value = createDragClone(target, event, isMultiSelect ? selectedTaskList.length : 1)

  // 设置拖拽任务的透明度
  if (isMultiSelect) {
    // 多选拖拽：设置所有选中任务的透明度
    selectedTaskList.forEach(selectedTask => {
      const taskElements = document.querySelectorAll(`[data-task-id="${selectedTask.id}"]`)
      taskElements.forEach(el => {
        (el as HTMLElement).style.opacity = '0.5'
      })
    })
  } else {
    // 单个拖拽：只设置当前任务的透明度
    target.style.opacity = '0.5'
  }

  // 获取当前任务的可拖拽状态列表（假设所有选中任务都有相同的规则）
  currentTaskAvailableStatuses.value = getAvailableDropStatuses(task)
  console.log(`拖拽 ${selectedTaskList.length} 个任务，可拖拽到状态:`, currentTaskAvailableStatuses.value)

  // 添加全局事件监听 - 使用 VueUse，确保事件正确传递
  dragOverListener = useEventListener(window, 'dragover', (event: DragEvent) => {
    updateDragClonePosition(event)
  }, { capture: true })
  dragEndListener = useEventListener(window, 'dragend', clearDragState, { capture: true })
}

const handleDragEnd = () => {
  // 透明度恢复在clearDragState中处理
  clearDragState()
}

const handleDragOver = (event: DragEvent, targetStatus: string) => {
  if (!draggedTask.value) return

  event.preventDefault()
  event.stopPropagation()

  // 设置拖拽悬停状态
  dragOverColumn.value = targetStatus

  // 检查是否允许拖拽到目标状态
  const canDrop = currentTaskAvailableStatuses.value.includes(targetStatus)
  if (!canDrop) {
    event.dataTransfer!.dropEffect = 'none'
  } else {
    event.dataTransfer!.dropEffect = 'move'
  }
}

// 处理拖拽进入列
const handleDragEnter = (event: DragEvent, targetStatus: string) => {
  if (!draggedTask.value) return

  event.preventDefault()
  event.stopPropagation()

  dragOverColumn.value = targetStatus
}

// 处理拖拽离开列
const handleDragLeave = (event: DragEvent, targetStatus: string) => {
  if (!draggedTask.value) return

  // 检查是否真的离开了列（而不是进入子元素）
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY

  // 添加一些容差，考虑到缩放效果
  const tolerance = 20
  const isOutside = x < rect.left - tolerance ||
                   x > rect.right + tolerance ||
                   y < rect.top - tolerance ||
                   y > rect.bottom + tolerance

  if (isOutside && dragOverColumn.value === targetStatus) {
    dragOverColumn.value = null
  }
}

// 处理任务上的拖拽悬停
const handleTaskDragOver = (event: DragEvent, task: Task) => {
  if (!draggedTask.value || draggedTask.value.id === task.id) return

  event.preventDefault()
  event.stopPropagation()

  dragOverTask.value = task.id
}

const handleDrop = (event: DragEvent, newStatus: string) => {
  event.preventDefault()
  event.stopPropagation()

  if (!event.dataTransfer || !draggedTask.value) return

  const dragData = event.dataTransfer.getData('text/plain')
  if (dragData) {
    const parsedData = JSON.parse(dragData)

    if (parsedData.isMultiple && parsedData.tasks) {
      // 批量操作
      const tasksToUpdate = parsedData.tasks as Task[]
      console.log(`批量更新 ${tasksToUpdate.length} 个任务到状态: ${newStatus}`)

      tasksToUpdate.forEach(task => {
        if (task.status !== newStatus) {
          const operation = getOperationForStatus(task, newStatus)
          if (operation) {
            handleOperationExecute(task, operation)
          } else {
            console.warn(`无法找到从状态 ${task.status} 到 ${newStatus} 的操作`)
          }
        }
      })

      // 只有在多选拖拽时才清除选择状态
      if (parsedData.isMultiple) {
        clearSelection()
      }
    } else {
      // 单个任务操作（兼容旧格式）
      const task = parsedData.tasks ? parsedData.tasks[0] : parsedData
      if (task.status !== newStatus) {
        const operation = getOperationForStatus(task, newStatus)
        if (operation) {
          handleOperationExecute(task, operation)
        } else {
          console.warn(`无法找到从状态 ${task.status} 到 ${newStatus} 的操作`)
        }
      }
    }
  }

  clearDragState()
}



</script>

<template>
  <div class="kanban-view">
    <!-- 空状态 -->
    <div v-if="tasks.length === 0" class="empty-state">
      <div class="empty-content">
        <div class="empty-icon">📋</div>
        <h3 class="empty-title">暂无任务</h3>
        <p class="empty-description">当前项目还没有任务，创建第一个任务开始工作吧！</p>
      </div>
    </div>

    <!-- 看板内容 -->
    <div v-else class="kanban-container">

      <div class="kanban-columns">
        <div v-for="column in enhancedKanbanColumns" :key="column.key" class="kanban-column" :class="{
          'drop-allowed': column.allowDrop && draggedTask,
          'drag-over': column.isDragOver && draggedTask,
          'current-column': column.isCurrentColumn && draggedTask,
          'drop-blocked': column.isBlocked && draggedTask
        }" @dragover="handleDragOver($event, column.status)" @drop="handleDrop($event, column.status)"
          @dragenter="handleDragEnter($event, column.status)" @dragleave="handleDragLeave($event, column.status)">

          <!-- 阴影遮罩层 -->
          <div v-if="column.isBlocked && draggedTask" class="column-shadow-overlay">
            <div class="block-icon">🚫</div>
            <div class="block-text">不可拖拽</div>
          </div>

          <!-- 列标题 -->
          <div class="column-header" :style="{ '--column-color': column.color }">
            <div class="header-content">
              <div class="column-title-wrapper">
                <div class="column-indicator"></div>
                <h4 class="column-title">{{ column.title }}</h4>
              </div>
              <div class="task-count-wrapper">
                <span class="task-count">{{ tasksByStatus[column.status]?.length || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 任务列表区域 -->
          <div class="column-content">
            <div class="column-tasks">
              <div v-for="task in tasksByStatus[column.status]" :key="`kanban-${task.id}`"
                class="kanban-task" :class="{
                  'dragging': draggedTask?.id === task.id,
                  'drag-over-task': dragOverTask === task.id,
                  'selected': selectedTasks.has(task.id)
                }"
                draggable="true"
                @dragstart="handleDragStart($event, task)"
                @dragend="handleDragEnd"
                @dragover="handleTaskDragOver($event, task)"
                @contextmenu="handleContextMenu($event, task)"
                @click="handleTaskClick($event, task)"
                :data-task-id="task.id">

                <!-- 任务卡片内容 -->
                <div class="task-card">
                  <!-- 任务头部 -->
                  <div class="task-header">
                    <div class="task-title-section">
                      <div class="title-with-id">
                        <h5 class="task-title">
                          <span v-if="task.taskNo" class="task-no">{{ task.taskNo }}</span>
                          {{ task.title }}
                        </h5>
                        <span class="task-id">#{{ task.id }}</span>
                      </div>
                      <!-- 阶段信息 -->
                      <div v-if="task.stageName" class="stage-info">
                        <span class="stage-icon">📋</span>
                        <span class="stage-name">{{ task.stageName }}</span>
                      </div>
                    </div>
                    <div class="task-type-badge">
                      <n-tag :type="getIssueTypeInfo(task.issueType).type" size="small" round>
                        {{ getIssueTypeInfo(task.issueType).text }}
                      </n-tag>
                    </div>
                  </div>

                  <!-- 任务底部信息 -->
                  <div class="task-footer">
                    <div class="assignee-section">
                      <n-avatar :size="24" :src="task.assigneeAvatar" class="assignee-avatar">
                        {{ task.assignee.charAt(0) }}
                      </n-avatar>
                      <span class="assignee-name">{{ task.assignee }}</span>
                    </div>

                    <div class="task-meta-section">
                      <div class="priority-badge">
                        <n-tag :type="getPriorityInfo(task.priority).type" size="tiny" round>
                          {{ getPriorityInfo(task.priority).text }}
                        </n-tag>
                      </div>
                      <div v-if="task.duration" class="duration-badge">
                        <span class="duration-icon">⏱</span>
                        <span class="duration-text">{{ task.duration }}h</span>
                      </div>
                    </div>
                  </div>

                  <!-- 选中状态指示器 -->
                  <div v-if="selectedTasks.has(task.id)" class="selection-indicator">
                    <div class="selection-check">✓</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <n-dropdown
      placement="bottom-start"
      trigger="manual"
      :x="contextMenuX"
      :y="contextMenuY"
      :options="contextMenuOptions"
      :show="contextMenuVisible"
      :on-clickoutside="handleContextMenuClickOutside"
      @select="handleContextMenuSelect"
    />

    <!-- 分配模态框 -->
    <n-modal
      v-model:show="showAssignModal"
      preset="dialog"
      :title="assigningTasks.length > 1 ? `批量分配 (${assigningTasks.length}个任务)` : '分配任务'"
      positive-text="确认分配"
      negative-text="取消"
      @positive-click="handleConfirmAssign"
      @negative-click="handleCancelAssign"
    >
      <div class="assign-modal-content">
        <div class="assign-info">
          <p v-if="assigningTasks.length === 1" class="single-task-info">
            任务：{{ assigningTasks[0]?.title }}
          </p>
          <p v-else class="batch-task-info">
            将为以下 {{ assigningTasks.length }} 个任务分配指派人：
          </p>
          <div v-if="assigningTasks.length > 1" class="task-list">
            <div v-for="task in assigningTasks" :key="task.id" class="task-item">
              <span class="task-no" v-if="task.taskNo">{{ task.taskNo }}</span>
              <span class="task-title">{{ task.title }}</span>
            </div>
          </div>
        </div>

        <div class="assign-form">
          <n-form-item label="指派人" required>
            <n-select
              v-model:value="selectedAssignee"
              :options="userOptions"
              :loading="loadingUsers"
              placeholder="请选择指派人"
              clearable
            />
          </n-form-item>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<style lang="less" scoped>
.kanban-view {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

.empty-content {
  text-align: center;
  padding: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.8;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.5;
}

/* 看板容器 */
.kanban-container {
  overflow: hidden;
  padding: 20px;
}

.kanban-columns {
  display: flex;
  gap: 20px;
  height: 100%+40px;
  overflow-x: auto;
  overflow: hidden;
  padding: 40px; /* 为拖拽扩展区域提供空间 */
  margin: -20px; /* 补偿padding */

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.kanban-column {
  flex: 0 0 320px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border: 1px solid #e0e0e0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--column-color, #e9ecef);
    border-radius: 16px 16px 0 0;
  }
}

/* 拖拽状态样式 */
.kanban-column.drop-allowed {
  background: rgba(230, 247, 255, 0.95);
  border: 2px dashed #1890ff;
  position: relative;
  z-index: 1;

  /* 使用outline而不是伪元素来避免边界问题 */
  outline: 8px solid rgba(24, 144, 255, 0.1);
  outline-offset: 4px;

  &::before {
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    animation: pulse 2s ease-in-out infinite;
    z-index: 1;
  }
}

.kanban-column.drag-over {
  background: rgba(240, 249, 255, 0.98);
  border: 2px solid #1890ff;
  position: relative;
  z-index: 2;

  /* 使用outline确保完全覆盖 */
  outline: 12px solid rgba(24, 144, 255, 0.15);
  outline-offset: 6px;

  &::before {
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    animation: glow 1s ease-in-out infinite alternate;
    z-index: 1;
  }

  /* 添加一个扩展的拖拽检测区域 */
  &::after {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    pointer-events: auto;
    z-index: -1;
  }
}

.kanban-column.current-column {
  background: rgba(255, 247, 230, 0.95);
  border: 2px dashed #fa8c16;
  opacity: 0.8;

  &::before {
    background: #fa8c16;
  }
}

.kanban-column.drop-blocked {
  position: relative;
  opacity: 0.3;
  pointer-events: none;
  transform: scale(0.95);
  filter: grayscale(0.5);
}

/* 阴影遮罩层 */
.column-shadow-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 10;
  border-radius: 16px;
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.block-icon {
  font-size: 48px;
  opacity: 0.9;
  animation: blockPulse 2s ease-in-out infinite;
}

.block-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
}

@keyframes blockPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px var(--column-color, #1890ff);
  }
  100% {
    box-shadow: 0 0 20px var(--column-color, #1890ff);
  }
}

@keyframes expandBorder {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.02);
    opacity: 0.6;
  }
}

.column-header {
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px 16px 0 0;
  flex-shrink: 0;
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.column-title-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.column-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--column-color, #e9ecef);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
}

.column-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  letter-spacing: -0.02em;
}

.task-count-wrapper {
  display: flex;
  align-items: center;
}

.task-count {
  background: var(--column-color, #e9ecef);
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 6px 10px;
  border-radius: 20px;
  min-width: 24px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* 任务内容区域 */
.column-content {
  flex: 1;
  height: calc(100vh - 350px);
  min-height: calc(100vh - 350px);
  max-height: calc(100vh - 350px);
  background: rgba(248, 250, 252, 0.5);
  border-radius: 0 0 16px 16px;
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.2);
    }
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

/* 任务列表容器 */
.column-tasks {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.kanban-task {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &:hover {
    transform: translateY(-2px) scale(1.02);

    .task-card {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      border-color: #1890ff;
    }
  }
}

.task-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: 140px; /* 固定高度，增加以容纳阶段字段 */
  display: flex;
  flex-direction: column;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
}

/* 选中状态样式 */
.kanban-task.selected {
  .task-card {
    border-color: #1890ff;
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
    transform: scale(1.02);

    &::before {
      opacity: 1;
      background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
    }
  }
}

.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
}

.selection-check {
  width: 20px;
  height: 20px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  animation: checkBounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes checkBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 拖拽中的任务样式 */
.kanban-task.dragging {
  opacity: 0.6;
  transform: scale(0.95) rotate(2deg);

  .task-card {
    border: 2px dashed #1890ff;
    background: rgba(240, 249, 255, 0.9);
    box-shadow: 0 8px 32px rgba(24, 144, 255, 0.2);
  }
}

/* 拖拽悬停在任务上的样式 */
.kanban-task.drag-over-task {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    border-radius: 2px;
    box-shadow: 0 0 12px rgba(24, 144, 255, 0.6);
    animation: dropIndicator 1s ease-in-out infinite;
  }
}

@keyframes dropIndicator {
  0%, 100% {
    opacity: 0.8;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(0.95);
  }
}

/* 任务头部 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.task-title-section {
  flex: 1;
  min-width: 0;
}

.title-with-id {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.task-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.4;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.task-no {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  flex-shrink: 0;
}

.task-id {
  font-size: 11px;
  color: #718096;
  font-weight: 500;
  opacity: 0.8;
  background: rgba(113, 128, 150, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  flex-shrink: 0;
}

.task-type-badge {
  flex-shrink: 0;
}

/* 阶段信息样式 */
.stage-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(99, 102, 241, 0.08);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 6px;
  width: fit-content;
}

.stage-icon {
  font-size: 11px;
  opacity: 0.8;
}

.stage-name {
  font-size: 11px;
  color: #6366f1;
  font-weight: 500;
  line-height: 1.2;
}



/* 任务底部 */
.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  margin-top: auto;
}

.assignee-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.assignee-avatar {
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assignee-name {
  font-size: 12px;
  color: #4a5568;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-meta-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.priority-badge {
  display: flex;
  align-items: center;
}

.duration-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(99, 102, 241, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.duration-icon {
  font-size: 10px;
  opacity: 0.8;
}

.duration-text {
  font-size: 11px;
  color: #6366f1;
  font-weight: 500;
}

/* 全局拖拽克隆元素样式 */
:global(.drag-clone-container) {
  transition: none;
  animation: dragFloat 2s ease-in-out infinite alternate;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

:global(.drag-clone) {
  background: white;
  border: 2px solid #1890ff;
  border-radius: 12px;
  transition: none;
  overflow: hidden;
}

/* 单个卡片拖拽样式 */
:global(.drag-clone-container .drag-clone:only-child) {
  box-shadow: 0 12px 40px rgba(24, 144, 255, 0.3);
  opacity: 0.95;
  animation: dragPulse 1.5s ease-in-out infinite alternate;
}

/* 层叠卡片样式 */
:global(.drag-layer-0) {
  border: 2px solid #1890ff !important;
  animation: dragLayerFloat 2s ease-in-out infinite alternate;
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.2);
}

:global(.drag-layer-1) {
  border: 2px solid #40a9ff !important;
  animation: dragLayerFloat 2s ease-in-out infinite alternate 0.2s;
  box-shadow: 0 6px 24px rgba(64, 169, 255, 0.15);
}

:global(.drag-layer-2) {
  border: 2px solid #69c0ff !important;
  animation: dragLayerFloat 2s ease-in-out infinite alternate 0.4s;
  box-shadow: 0 4px 16px rgba(105, 192, 255, 0.1);
}

:global(.drag-layer-3) {
  border: 2px solid #91d5ff !important;
  animation: dragLayerFloat 2s ease-in-out infinite alternate 0.6s;
  box-shadow: 0 2px 8px rgba(145, 213, 255, 0.08);
}

:global(.drag-count-badge) {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
  border: 3px solid white !important;
  box-shadow: 0 4px 16px rgba(255, 77, 79, 0.4) !important;
  animation: badgePulse 2s ease-in-out infinite !important;
}

@keyframes dragFloat {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  100% {
    transform: translateY(-4px) rotate(1deg);
  }
}

@keyframes dragPulse {
  0% {
    box-shadow: 0 12px 40px rgba(24, 144, 255, 0.3);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 16px 48px rgba(24, 144, 255, 0.4);
    transform: scale(1.02);
  }
}

@keyframes dragLayerFloat {
  0% {
    transform: translateY(0px) scale(var(--layer-scale, 1)) rotate(var(--layer-rotate, 0deg));
  }
  100% {
    transform: translateY(-3px) scale(var(--layer-scale, 1)) rotate(var(--layer-rotate, 0deg));
  }
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 多选状态提示样式 */
.selection-status {
  background: #f0f8ff;
  border: 1px solid #1890ff;
  border-radius: 6px;
  padding: 8px 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selection-count {
  color: #1890ff;
  font-weight: 500;
  font-size: 14px;
}

/* 分配模态框样式 */
.assign-modal-content {
  padding: 16px 0;
}

.assign-info {
  margin-bottom: 20px;
}

.single-task-info,
.batch-task-info {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.task-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 8px;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background: #f5f5f5;
  }
}

.task-item .task-no {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  flex-shrink: 0;
}

.task-item .task-title {
  flex: 1;
  font-size: 13px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.assign-form {
  margin-top: 16px;
}
</style>
