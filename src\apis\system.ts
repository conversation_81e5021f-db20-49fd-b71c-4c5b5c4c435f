// 系统管理相关 API 接口
import { httpClient, buildPaginationParams } from './index'
import type {
  SystemAsset,
  SystemAssetQuery
} from '@/types/system'
import type {
  ApiResponse,
  PaginationParams,
  PaginationResponse
} from '@/types'
import { SSE } from 'sse.js'

// 系统资产创建参数 - 与后端 CreateSystemAssetDTO 保持一致
export interface CreateSystemAssetParams {
  assetNo: string
  projectName?: string
  shortCode?: string
  ciName?: string
  site?: string
  location?: string
  businessDepartment?: string
  pmOwner?: string
  description?: string
  extraProperties?: Record<string, any>
}

// 系统资产更新参数
export interface UpdateSystemAssetParams extends Partial<CreateSystemAssetParams> {
  id: number
}

// 系统资产统计信息
export interface SystemAssetStats {
  total: number
  bySite: Record<string, number>
  byBusinessDepartment: Record<string, number>
  byAssetState: Record<string, number>
  active: number
  inactive: number
}

// 系统管理 API 类
export class SystemApi {
  private static readonly BASE_PATH = '/system/assets'

  // 解析SSE事件数据的通用方法
  private static parseEventData(eventData: any): any {
    if (typeof eventData === 'string') {
      if (eventData.trim() === '') {
        return { total: 0, success: 0, failed: 0, skipped: 0 }
      }
      return JSON.parse(eventData)
    } else if (typeof eventData === 'object' && eventData !== null) {
      return eventData
    }
    return { total: 0, success: 0, failed: 0, skipped: 0 }
  }
 
  /**
   * 获取系统资产列表
   */
  static async getSystemAssets(params?: SystemAssetQuery): Promise<ApiResponse<PaginationResponse<SystemAsset>>> {
    const queryParams = params ? {
      ...buildPaginationParams({
        page: params.page || 1,
        pageSize: params.pageSize || 12
      }),
      keyword: params.keyword,
      region: params.region,
      businessDepartment: params.businessDepartment,
      assetState: params.assetState
    } : undefined

    return httpClient.get<PaginationResponse<SystemAsset>>(this.BASE_PATH, {params: queryParams})
  }

  /**
   * 获取系统资产详情
   */
  static async getSystemAsset(id: number): Promise<ApiResponse<SystemAsset>> {
    return httpClient.get<SystemAsset>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建系统资产
   */
  static async createSystemAsset(data: CreateSystemAssetParams): Promise<ApiResponse<SystemAsset>> {
    return httpClient.post<SystemAsset>(this.BASE_PATH, data)
  }

  /**
   * 更新系统资产
   */
  static async updateSystemAsset(data: UpdateSystemAssetParams): Promise<ApiResponse<SystemAsset>> {
    const { id, ...updateData } = data
    return httpClient.put<SystemAsset>(`${this.BASE_PATH}/${id}`, updateData)
  }

  /**
   * 删除系统资产
   */
  static async deleteSystemAsset(id: number): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 批量删除系统资产
   */
  static async batchDeleteSystemAssets(ids: string[]): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/batch?ids=${ids.join(',')}`)
  }

  /**
   * 获取系统资产统计信息
   */
  static async getSystemAssetStats(): Promise<ApiResponse<SystemAssetStats>> {
    return httpClient.get<SystemAssetStats>(`${this.BASE_PATH}/stats`)
  }

  /**
   * 导出系统资产数据
   */
  static async exportSystemAssets(params?: SystemAssetQuery): Promise<ApiResponse<{ downloadUrl: string }>> {
    const queryParams = params ? {
      keyword: params.keyword,
      region: params.region,
      businessDepartment: params.businessDepartment,
      assetState: params.assetState
    } : undefined

    return httpClient.get<{ downloadUrl: string }>(`${this.BASE_PATH}/export`, queryParams)
  }

  /**
   * 导入系统资产数据
   */
  static async importSystemAssets(file: File): Promise<ApiResponse<{
    total: number
    success: number
    failed: number
    errors?: Array<{
      row: number
      field: string
      message: string
    }>
  }>> {
    return httpClient.upload(`${this.BASE_PATH}/import`, file)
  }

  /**
   * 获取站点选项
   */
  static async getSiteOptions(): Promise<ApiResponse<Array<{ value: string; label: string }>>> {
    return httpClient.get<Array<{ value: string; label: string }>>('/system/options/sites')
  }

  /**
   * 获取业务部门选项
   */
  static async getBusinessDepartmentOptions(): Promise<ApiResponse<Array<{ value: string; label: string }>>> {
    return httpClient.get<Array<{ value: string; label: string }>>('/system/options/business-departments')
  }

  /**
   * 获取资产状态选项
   */
  static async getAssetStateOptions(): Promise<ApiResponse<Array<{ value: string; label: string }>>> {
    return httpClient.get<Array<{ value: string; label: string }>>('/system/options/asset-states')
  }

  /**
   * 获取项目经理选项
   */
  static async getPmOwnerOptions(): Promise<ApiResponse<Array<{ value: string; label: string }>>> {
    return httpClient.get<Array<{ value: string; label: string }>>('/system/options/pm-owners')
  }

  /**
   * 验证资产编号唯一性
   */
  static async validateAssetNo(assetNo: string, excludeId?: string): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = excludeId ? { assetNo, excludeId } : { assetNo }
    return httpClient.get<{ isUnique: boolean }>(`${this.BASE_PATH}/validate/asset-no`, params)
  }

  /**
   * 导出系统资产数据或下载模板
   * @param params 查询参数
   * @param isTemplate 是否为模板下载
   */
  static async exportAssets(params?: any, isTemplate: boolean = false): Promise<void> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams()

      // 添加模板参数
      queryParams.append('template', isTemplate.toString())

      // 只有在不是模板下载且有参数时才添加查询条件
      if (params && !isTemplate) {
        // 过滤并添加有效参数
        if (params.keyword && params.keyword.trim()) {
          queryParams.append('keyword', params.keyword.trim())
        }
        if (params.region && params.region.trim()) {
          queryParams.append('region', params.region.trim())
        }
        if (params.businessDepartment && params.businessDepartment.trim()) {
          queryParams.append('businessDepartment', params.businessDepartment.trim())
        }
        if (params.assetState && params.assetState.trim()) {
          queryParams.append('assetState', params.assetState.trim())
        }
        if (params.pmOwner && params.pmOwner.trim()) {
          queryParams.append('pmOwner', params.pmOwner.trim())
        }
        if (params.page !== undefined && params.page !== null) {
          queryParams.append('page', String(params.page))
        }
        if (params.pageSize !== undefined && params.pageSize !== null) {
          queryParams.append('pageSize', String(params.pageSize))
        }
      }

      const baseURL = import.meta.env.VITE_API_BASE_URL || '/api'
      const url = `${baseURL}${this.BASE_PATH}/export${queryParams.toString() ? '?' + queryParams.toString() : ''}`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        }
      })

      if (!response.ok) {
        throw new Error(isTemplate ? '下载模板失败' : '导出数据失败')
      }

      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = isTemplate ? '系统资产导入模板.xlsx' : '系统资产数据.xlsx'
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      }

      // 下载文件
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)
    } catch (error) {
      console.error(isTemplate ? '下载模板失败:' : '导出数据失败:', error)
      throw error
    }
  }

  /**
   * 导入系统资产（SSE方式）
   */
  static async importAssets(
    file: File,
    mode: 'incremental' | 'overwrite',
    onProgress?: (data: any) => void,
    onComplete?: (data: any) => void,
    onError?: (error: any) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      // 先上传文件，获取导入任务ID或直接开始SSE连接
      const formData = new FormData()
      formData.append('file', file)
      formData.append('mode', mode)

      const token = localStorage.getItem('token')

      // 构建SSE URL，包含必要的参数
      const sseUrl = new URL(`/api${this.BASE_PATH}/import`, window.location.origin)

      // 创建SSE连接
      const eventSource = new SSE(sseUrl.toString(), {
        method: 'POST',
        payload: formData,
        headers: {
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        }
      })

      console.log('创建SSE连接:', sseUrl.toString())

      // 监听进度事件
      eventSource.addEventListener('progress', (event: any) => {
        try {
          const data = this.parseEventData(event.data)
          if (data && onProgress) {
            onProgress(data)
          }
        } catch (e) {
          console.warn('解析进度事件失败:', e)
        }
      })

      // 监听完成事件
      eventSource.addEventListener('complete', (event: any) => {
        try {
          const data = this.parseEventData(event.data)
          if (onComplete) {
            onComplete(data)
          }
        } catch (e) {
          // 解析失败时使用默认数据
          const fallbackData = { total: 0, success: 0, failed: 0, skipped: 0, errors: [] }
          if (onComplete) {
            onComplete(fallbackData)
          }
        } finally {
          eventSource.close()
          resolve()
        }
      })

      // 监听错误事件
      eventSource.addEventListener('error', (event: any) => {
        try {
          const data = this.parseEventData(event.data)
          const errorMessage = data.message || '导入失败'
          if (onError) {
            onError(data)
          }
          eventSource.close()
          reject(new Error(errorMessage))
        } catch (e) {
          const fallbackError = { message: '导入过程中发生错误' }
          if (onError) {
            onError(fallbackError)
          }
          eventSource.close()
          reject(new Error('导入过程中发生错误'))
        }
      })

      // 监听连接错误
      eventSource.onerror = () => {
        eventSource.close()
        if (onError) {
          onError({ message: 'SSE连接失败' })
        }
        reject(new Error('SSE连接失败'))
      }
    })
  }



  /**
   * 获取系统配置
   */
  static async getSystemConfig(): Promise<ApiResponse<{
    siteName: string
    siteDescription: string
    logo?: string
    theme: 'light' | 'dark' | 'auto'
    language: string
    timezone: string
    dateFormat: string
    timeFormat: string
  }>> {
    return httpClient.get('/system/config')
  }

  /**
   * 更新系统配置
   */
  static async updateSystemConfig(config: {
    siteName?: string
    siteDescription?: string
    logo?: string
    theme?: 'light' | 'dark' | 'auto'
    language?: string
    timezone?: string
    dateFormat?: string
    timeFormat?: string
  }): Promise<ApiResponse<void>> {
    return httpClient.put('/system/config', config)
  }

  /**
   * 获取系统健康状态
   */
  static async getSystemHealth(): Promise<ApiResponse<{
    status: 'healthy' | 'warning' | 'error'
    database: { status: string; responseTime: number }
    redis: { status: string; responseTime: number }
    storage: { status: string; usage: number; total: number }
    memory: { usage: number; total: number }
    cpu: { usage: number }
    uptime: number
  }>> {
    return httpClient.get('/system/health')
  }

  /**
   * 清理系统缓存
   */
  static async clearCache(): Promise<ApiResponse<void>> {
    return httpClient.post('/system/cache/clear')
  }

  /**
   * 获取系统日志
   */
  static async getSystemLogs(params?: PaginationParams & {
    level?: 'error' | 'warn' | 'info' | 'debug'
    startTime?: number
    endTime?: number
  }): Promise<ApiResponse<PaginationResponse<{
    id: string
    level: string
    message: string
    timestamp: number
    source: string
    details?: Record<string, any>
  }>>> {
    const queryParams = params ? {
      ...buildPaginationParams(params),
      level: params.level,
      startTime: params.startTime,
      endTime: params.endTime,
    } : undefined

    return httpClient.get('/system/logs', queryParams)
  }
}

// 导出默认实例
export const systemApi = SystemApi
