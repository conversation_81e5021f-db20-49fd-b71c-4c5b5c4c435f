// 项目相关类型定义

// 项目状态
export type ProjectStatus = 'request collect' | 'planning' | 'tab' | 'development' | 'inner test' | 'uat' | 'cab' | 'go live'

// 项目优先级
export type ProjectPriority = 'Low' | 'Medium' | 'High' | 'Urgent'

// 流程状态
export type ProcessStatus = 'pending' | 'in_progress' | 'completed' | 'skipped' | 'failed'

// 流程类型
export type ProcessType = 'requirement' | 'design' | 'development' | 'testing' | 'deployment' | 'review'

// 项目状态配置
export const PROJECT_STATUS_CONFIG: Record<ProjectStatus, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
  'request collect': { text: '需求收集', type: 'default' },
  'planning': { text: '规划中', type: 'info' },
  'tab': { text: 'TAB评审', type: 'warning' },
  'development': { text: '开发中', type: 'primary' },
  'inner test': { text: '内测', type: 'info' },
  'uat': { text: 'UAT测试', type: 'warning' },
  'cab': { text: 'CAB评审', type: 'warning' },
  'go live': { text: '已上线', type: 'success' }
}

// 项目状态选项
export const PROJECT_STATUS_OPTIONS = [
  { label: '需求收集', value: 'request collect' as ProjectStatus },
  { label: '规划中', value: 'planning' as ProjectStatus },
  { label: 'TAB评审', value: 'tab' as ProjectStatus },
  { label: '开发中', value: 'development' as ProjectStatus },
  { label: '内测', value: 'inner test' as ProjectStatus },
  { label: 'UAT测试', value: 'uat' as ProjectStatus },
  { label: 'CAB评审', value: 'cab' as ProjectStatus },
  { label: '已上线', value: 'go live' as ProjectStatus }
]

// 获取项目状态显示信息
export const getProjectStatusInfo = (status: ProjectStatus) => {
  return PROJECT_STATUS_CONFIG[status] || { text: status, type: 'default' as const }
}

// 项目类
export interface Project {
  id: number
  name: string
  description?: string
  cover?: string
  version?: string

  // 基本信息
  manager: string
  category: string
  priority: ProjectPriority
  status: ProjectStatus

  // 关联系统资产信息
  relatedAssetId?: number // 关联的系统资产ID
  assetVersion?: string // 系统资产版本号
  assetNo?: string // 资产号
  assetName?: string // 资产名

  // 时间信息
  startDate: number | null // 时间戳
  endDate: number | null // 时间戳
  createdAt: number // 时间戳
  updatedAt: number // 时间戳

  // 团队信息
  teamMembers?: string[] // 团队成员ID列表

  // 标签和分类
  tags: string[]

  // 状态标识
  starred: boolean // 是否关注
  archived: boolean // 是否归档

  // 统计信息
  totalTasks?: number // 总任务数
  completedTasks?: number // 已完成任务数
  totalDocuments?: number // 总文档数

  // 扩展字段
  customFields?: Record<string, any>

  // 图片错误状态（前端使用）
  imageError?: boolean

  // 计算属性（只读）
  readonly progress?: number // 基于时间计算的进度百分比 0-100
}

// 项目详情信息接口（用于项目详情页面）
export interface ProjectInfo {
  id: number
  name: string
  version: string
  description: string
  status: string
  progress: number
  startDate: string
  endDate: string
  manager: string
  relatedAssetId?: number
  assetVersion?: string
  assetName?: string
  assetNo?: string
}



// 项目统计信息
export interface ProjectStats {
  projectId: number | string
  
  // 任务统计
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  delayedTasks: number
  
  // 流程统计
  totalProcesses: number
  completedProcesses: number
  inProgressProcesses: number
  
  // 文档统计
  totalDocuments: number
  publishedDocuments: number
  draftDocuments: number
  
  // 团队统计
  totalMembers: number
  activeMembers: number
  
  // 时间统计
  totalEstimatedHours: number
  totalActualHours: number
  averageTaskCompletionTime: number // 平均任务完成时间（天）
  
  // 质量统计
  averageQualityScore: number
  onTimeCompletionRate: number // 按时完成率
  
  // 更新时间
  lastUpdated: number
}