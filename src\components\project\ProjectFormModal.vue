<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { Project } from '@/types'
import { useUserOptions } from '@/composables/useUserOptions'
import { useSystemOptions } from '@/composables/useSystemOptions'
import { useSystemAssetStore } from '@/stores/systemAsset'
import RichTextEditor from '@/components/common/RichTextEditor.vue'

interface Props {
  show: boolean
  mode: 'create' | 'edit'
  projectData?: Partial<Project>
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success', data: Partial<Project>): void
  (e: 'closed'): void // 新增关闭回调事件
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  mode: 'create',
  projectData: () => ({})
})

const emit = defineEmits<Emits>()

const message = useMessage()

const systemAssetStore = useSystemAssetStore();

// 表单数据
const formRef = ref()
const formData = reactive<Partial<Project>>({
  name: '',
  description: '',
  manager: '',
  category: undefined,
  priority: 'Medium',
  tags: [],
  cover: '',
  relatedAssetId: undefined,
  assetVersion: ''
})

// 项目经理选项
const managerOptions = ref<Array<{label: string, value: string}>>([])
const loadingManagers = ref(false)

// 项目分类选项
const projectCategoryOptions = ref<Array<{label: string, value: string}>>([])
const loadingProjectCategories = ref(false)

// 项目优先级选项
const priorityOptions = ref<Array<{label: string, value: string}>>([])
const loadingPriorities = ref(false)

// 系统资产选项
const assetOptions = ref<Array<{ label: string; value: number; version?: string }>>([])
const loadingAssets = ref(false)

// 文件上传相关
const uploadRef = ref()
const coverImageUrl = ref('')

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入项目名称',
    trigger: ['input', 'blur']
  },
  manager: {
    required: true,
    message: '请选择项目经理',
    trigger: ['change', 'blur']
  },
  type: {
    required: true,
    message: '请选择项目类型',
    trigger: ['change', 'blur']
  }
}

const loading = ref(false)

// 计算属性
const modalTitle = computed(() => props.mode === 'create' ? '新建项目' : '编辑项目')
const submitText = computed(() => props.mode === 'create' ? '创建项目' : '保存更改')

// 监听显示状态变化
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const assetOptionsComputed = () => {
  if (systemAssetStore.assets && Array.isArray(systemAssetStore.assets)) {
      assetOptions.value = systemAssetStore.assets.map((asset: any) => ({
        label: `${asset.projectName || asset.assetNo} (${asset.assetNo})`,
        value: asset.id,
        version: asset.version || ''
      }))
    } else {
      assetOptions.value = []
    }
}

// 监听项目数据变化
watch(() => props.projectData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    const processedData = { ...newData }
    // 确保数组字段不为 null
    if (!Array.isArray(processedData.tags)) {
      processedData.tags = []
    }
    if (!Array.isArray(processedData.teamMembers)) {
      processedData.teamMembers = []
    }
    Object.assign(formData, processedData)
  }
}, { immediate: true, deep: true })

// 监听模态框显示状态
watch(() => props.show, (show) => {
  if (show && props.mode === 'create') {
    resetForm()
  }
})

// 图片上传处理
const handleUploadChange = (options: any) => {
  const { file } = options
  if (file.status === 'finished') {
    // 这里应该是上传成功后的URL，现在模拟一个
    coverImageUrl.value = URL.createObjectURL(file.file)
    formData.cover = coverImageUrl.value
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.restoreValidation()
  Object.assign(formData, {
    name: '',
    description: '',
    manager: '',
    category: undefined,
    priority: 'Medium',
    tags: [],
    cover: '',
    relatedAssetId: undefined,
    assetVersion: ''
  })
  coverImageUrl.value = ''
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    const submitData = { ...formData }

    // 添加创建和更新时间戳
    const now = Date.now()
    if (props.mode === 'create') {
      submitData.createdAt = now
      submitData.updatedAt = now
      // 新建项目默认状态为需求收集
      submitData.status = 'request collect'
      // 新建项目默认进度为0，不关注，不归档
      submitData.progress = 0
      submitData.starred = false
      submitData.archived = false
    } else {
      submitData.updatedAt = now
    }

    // 触发成功事件，让父组件处理实际的 API 调用
    // 注意：不在这里关闭模态框，让父组件根据 API 调用结果来决定是否关闭
    emit('success', submitData)
  } catch (error) {
    message.error('请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:show', false)
}

// 处理项目经理选择器点击事件
const handleManagerClick = async () => {
  if (managerOptions.value.length === 0 && !loadingManagers.value) {
    loadingManagers.value = true
    try {
      const { fetchUserOptions } = useUserOptions()
      const users = await fetchUserOptions()
      managerOptions.value = users.map((user: any) => ({
        label: user.label,
        value: user.value
      }))
    } catch (error) {
      console.error('获取项目经理选项失败:', error)
      message.error('获取项目经理选项失败')
    } finally {
      loadingManagers.value = false
    }
  }
}

// 处理项目分类选择器点击事件
const handleProjectCategoryClick = async () => {
  if (projectCategoryOptions.value.length === 0 && !loadingProjectCategories.value) {
    loadingProjectCategories.value = true
    try {
      const { loadProjectCategoryOptions } = useSystemOptions()
      const categories = await loadProjectCategoryOptions()
      projectCategoryOptions.value = categories
    } catch (error) {
      console.error('获取项目分类选项失败:', error)
      message.error('获取项目分类选项失败')
    } finally {
      loadingProjectCategories.value = false
    }
  }
}

// 处理优先级选择器点击事件
const handlePriorityClick = async () => {
  if (priorityOptions.value.length === 0 && !loadingPriorities.value) {
    loadingPriorities.value = true
    try {
      const { loadPriorityOptions } = useSystemOptions()
      const priorities = await loadPriorityOptions()
      priorityOptions.value = priorities
    } catch (error) {
      console.error('获取优先级选项失败:', error)
      message.error('获取优先级选项失败')
    } finally {
      loadingPriorities.value = false
    }
  }
}

// 模态框打开后初始化
const onAfterEnter = async () => {
  assetOptionsComputed();
};
</script>

<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="modalTitle"
    class="project-form-modal"
    style="width: 800px"
    :on-after-enter="onAfterEnter"
    :on-after-leave="() => emit('closed')"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="2" :x-gap="24">
        <n-gi>
          <n-form-item label="项目名称" path="name">
            <n-input
              v-model:value="formData.name"
              placeholder="请输入项目名称"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="项目经理" path="manager">
            <n-select
              v-model:value="formData.manager"
              :options="managerOptions"
              :loading="loadingManagers"
              placeholder="请选择项目经理"
              filterable
              clearable
              @click="handleManagerClick"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="项目分类" path="category">
            <n-select
              v-model:value="formData.category"
              :options="projectCategoryOptions"
              :loading="loadingProjectCategories"
              placeholder="请选择项目分类"
              clearable
              @click="handleProjectCategoryClick"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="优先级" path="priority">
            <n-select
              v-model:value="formData.priority"
              :options="priorityOptions"
              :loading="loadingPriorities"
              placeholder="请选择优先级"
              @click="handlePriorityClick"
            />
          </n-form-item>
        </n-gi>


        <n-gi>
          <n-form-item label="项目标签">
            <n-dynamic-tags v-model:value="formData.tags" />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="关联资产ID">
            <n-select
              v-model:value="formData.relatedAssetId"
              :options="assetOptions"
              :loading="loadingAssets"
              placeholder="请选择系统资产"
              clearable
              filterable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="资产版本号">
            <n-input
              v-model:value="formData.assetVersion"
              placeholder="请输入系统资产版本号"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi :span="2">
          <n-form-item label="项目描述">
            <RichTextEditor
              v-model="formData.description"
              placeholder="请输入项目描述"
              height="180px"
              max-height="300px"
            />
          </n-form-item>
        </n-gi>
        <n-gi :span="2">
          <n-form-item label="项目封面">
            <n-upload
              ref="uploadRef"
              :default-file-list="[]"
              :max="1"
              list-type="image-card"
              accept="image/*"
              @change="handleUploadChange"
            >
              <n-button>上传封面图片</n-button>
            </n-upload>
            <n-text depth="3" style="font-size: 12px; margin-top: 8px; display: block;">
              支持 JPG、PNG 格式，建议尺寸 16:9，文件大小不超过 2MB
            </n-text>
          </n-form-item>
        </n-gi>
      </n-grid>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button v-if="mode === 'create'" @click="resetForm">重置</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ submitText }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<style lang="less" scoped>
</style>
