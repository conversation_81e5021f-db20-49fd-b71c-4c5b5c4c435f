<template>
  <div class="document-upload-node">
    <div class="node-header">
      <h4 class="node-title">{{ title }}</h4>
      <n-tag :type="statusType" size="small">{{ statusText }}</n-tag>
    </div>

    <div class="document-list" v-if="documents.length > 0">
      <div class="document-grid">
        <div
          v-for="doc in documents"
          :key="doc.id"
          class="document-item"
        >
          <div class="document-icon">
            <n-icon :component="getFileIcon(doc.name)" size="24" />
          </div>
          <div class="document-info">
            <div class="document-name">{{ doc.name }}</div>
            <div class="document-meta">
              <span class="document-size">{{ formatFileSize(doc.size) }}</span>
              <span class="document-time">{{ formatTime(doc.uploadTime) }}</span>
            </div>
          </div>
          <div class="document-actions">
            <n-button size="small" @click="downloadDocument(doc)">
              <template #icon>
                <n-icon :component="DownloadOutlined" />
              </template>
            </n-button>
            <n-button size="small" type="error" @click="removeDocument(doc.id)" v-if="!readonly">
              <template #icon>
                <n-icon :component="DeleteOutlined" />
              </template>
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <div class="upload-area" v-if="!readonly">
      <n-upload
        v-model:file-list="fileList"
        multiple
        :max="10"
        :on-before-upload="beforeUpload"
        :on-finish="handleUploadFinish"
        :on-remove="handleRemove"
        list-type="text"
        class="upload-component"
      >
        <n-upload-dragger class="upload-dragger">
          <div class="upload-content">
            <n-icon size="48" :depth="3" class="upload-icon">
              <CloudUploadOutlined />
            </n-icon>
            <n-text class="upload-text">点击或拖拽文件到此处上传</n-text>
            <n-p depth="3" class="upload-hint">
              支持多种文档格式，单个文件不超过 50MB
            </n-p>
          </div>
        </n-upload-dragger>
      </n-upload>
    </div>

    <div class="empty-state" v-if="documents.length === 0 && readonly">
      <n-empty description="暂无文档" />
    </div>

    <div class="node-actions" v-if="!readonly">
      <n-button type="primary" @click="submitNode" :loading="submitting" :disabled="documents.length === 0">
        提交
      </n-button>
      <n-button @click="resetNode">重置</n-button>
    </div>

    <div class="node-actions" v-else>
      <n-button @click="editNode">编辑文档</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  NTag, NIcon, NButton, NUpload, NUploadDragger, NText, NP, NEmpty,
  useMessage, type UploadFileInfo 
} from 'naive-ui'
import { 
  CloudUploadOutlined, DownloadOutlined, DeleteOutlined,
  FileTextOutlined, FilePdfOutlined, FileWordOutlined, 
  FileExcelOutlined, FilePptOutlined, FileOutlined
} from '@vicons/antd'
import type { DocumentUploadData } from '@/types/project_process'

interface Props {
  title: string
  status: 'pending' | 'current' | 'completed'
  data?: DocumentUploadData
  readonly?: boolean
}

interface Emits {
  (e: 'submit', data: DocumentUploadData): void
  (e: 'edit'): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<Emits>()
const message = useMessage()

const submitting = ref(false)
const fileList = ref<UploadFileInfo[]>([])
const documents = ref<DocumentUploadData['documents']>(props.data?.documents || [])

// 状态映射
const statusMap = {
  pending: { type: 'default', text: '待处理' },
  current: { type: 'info', text: '进行中' },
  completed: { type: 'success', text: '已完成' }
}

const statusType = computed(() => statusMap[props.status].type as 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary')
const statusText = computed(() => statusMap[props.status].text)

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData?.documents) {
    documents.value = [...newData.documents]
  }
}, { immediate: true })

// 获取文件图标
const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'pdf': return FilePdfOutlined
    case 'doc':
    case 'docx': return FileWordOutlined
    case 'xls':
    case 'xlsx': return FileExcelOutlined
    case 'ppt':
    case 'pptx': return FilePptOutlined
    case 'txt': return FileTextOutlined
    default: return FileOutlined
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 上传前验证
const beforeUpload = (data: { file: UploadFileInfo }) => {
  const maxSize = 50 * 1024 * 1024 // 50MB
  if (data.file.file?.size && data.file.file.size > maxSize) {
    message.error('文件大小不能超过50MB')
    return false
  }
  return true
}

// 上传完成
const handleUploadFinish = ({ file }: { file: UploadFileInfo }) => {
  if (file.status === 'finished') {
    const newDoc = {
      id: Date.now().toString(),
      name: file.name,
      url: file.url || '',
      uploadTime: new Date().toISOString(),
      size: file.file?.size || 0
    }
    documents.value.push(newDoc)
    message.success(`${file.name} 上传成功`)
  }
}

// 移除文件
const handleRemove = ({ file }: { file: UploadFileInfo }) => {
  const index = documents.value.findIndex(doc => doc.name === file.name)
  if (index > -1) {
    documents.value.splice(index, 1)
  }
}

// 下载文档
const downloadDocument = (doc: DocumentUploadData['documents'][0]) => {
  // 这里实现下载逻辑
  const link = document.createElement('a')
  link.href = doc.url
  link.download = doc.name
  link.click()
  message.info(`正在下载 ${doc.name}`)
}

// 删除文档
const removeDocument = (docId: string) => {
  const index = documents.value.findIndex(doc => doc.id === docId)
  if (index > -1) {
    documents.value.splice(index, 1)
    message.success('文档已删除')
  }
}

// 提交节点
const submitNode = async () => {
  if (documents.value.length === 0) {
    message.error('请至少上传一个文档')
    return
  }

  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const data: DocumentUploadData = {
      documents: documents.value
    }
    
    emit('submit', data)
    message.success('文档提交成功')
  } catch (error) {
    message.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 重置节点
const resetNode = () => {
  documents.value = []
  fileList.value = []
  message.info('已重置')
}

// 编辑节点
const editNode = () => {
  emit('edit')
}
</script>

<style scoped>
.document-upload-node {
  padding: 20px;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.node-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.document-grid {
  display: grid;
  gap: 12px;
  margin-bottom: 20px;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
  transition: all 0.2s ease;
}

.document-item:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.document-icon {
  margin-right: 12px;
  color: #6b7280;
}

.document-info {
  flex: 1;
  min-width: 0;
}

.document-name {
  font-weight: 500;
  color: #374151;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.document-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.document-actions {
  display: flex;
  gap: 8px;
}

.upload-area {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
  min-height: 120px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.upload-icon {
  margin-bottom: 12px;
  color: #9ca3af;
}

.upload-text {
  font-size: 14px;
  color: #374151;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.empty-state {
  margin: 40px 0;
}

.node-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* 暗黑模式适配 */
.dark .node-title {
  color: #f3f4f6;
}

.dark .document-item {
  border-color: #374151;
  background: #1f2937;
}

.dark .document-item:hover {
  border-color: #3b82f6;
  background: #1e3a8a;
}

.dark .document-name {
  color: #f3f4f6;
}

.dark .node-actions {
  border-top-color: #374151;
}
</style>
