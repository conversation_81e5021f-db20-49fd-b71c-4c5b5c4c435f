<script setup lang="ts">
import type { Project } from "@/types";
import ProjectCard from "./ProjectCard.vue";

interface Props {
  projects: Project[];
  showCreateCard?: boolean;
}

interface Emits {
  (e: 'click', projectId: string | number): void;
  (e: 'star', projectId: string | number): void;
  (e: 'edit', projectId: string | number): void;
  (e: 'delete', projectId: string | number): void;
  (e: 'create'): void;
}

const props = withDefaults(defineProps<Props>(), {
  showCreateCard: true
});

const emit = defineEmits<Emits>();
</script>

<template>
  <div class="project-grid">
    <!-- 项目卡片 -->
    <ProjectCard
      v-for="project in projects"
      :key="project.id"
      :project="project"
      @click="emit('click', $event)"
      @star="emit('star', $event)"
      @edit="emit('edit', $event)"
      @delete="emit('delete', $event)"
    />

    <!-- 新建项目卡片 -->
    <div v-if="showCreateCard" class="project-card new-project" @click="emit('create')">
      <div class="new-project-content">
        <n-icon :size="32" color="#8c8c8c">
          <IconAntDesignPlusOutlined />
        </n-icon>
        <div class="new-project-text">新建项目</div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import "@/assets/styles/variables.less";

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 8px;
  width: 100%;
  box-sizing: border-box;
}

.new-project {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 180px;
  border: 2px dashed #e0e0e0;
  background: #fafafa;
  transition: all 0.3s;
  border-radius: 12px;

  &:hover {
    border-color: var(--primary-color);
    background: rgba(24, 144, 255, 0.02);
    color: var(--primary-color);

    .new-project-content {
      transform: scale(1.05);
    }
  }

  .new-project-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    transition: transform 0.3s;

    .n-icon {
      transition: all 0.3s;
    }
  }

  .new-project-text {
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s;
  }
}
</style>
