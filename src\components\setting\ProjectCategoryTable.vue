<template>
  <div class="project-category-table">
    <!-- 搜索和操作栏 -->
    <div class="table-header">
      <div class="search-section">
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索分类名称或代码"
          clearable
          style="width: 240px"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <n-icon :component="IconAntDesignSearchOutlined" />
          </template>
        </n-input>
        
        <n-select
          v-model:value="enabledFilter"
          placeholder="启用状态"
          clearable
          style="width: 120px"
          :options="enabledOptions"
          value-field="value"
          label-field="label"
        />
        
        <n-button @click="handleSearch">
          <template #icon>
            <n-icon :component="IconAntDesignSearchOutlined" />
          </template>
          搜索
        </n-button>
        
        <n-button @click="handleReset">
          <template #icon>
            <n-icon :component="IconAntDesignReloadOutlined" />
          </template>
          重置
        </n-button>
      </div>
      
      <div class="action-section">
        <n-button type="primary" @click="handleCreate">
          <template #icon>
            <n-icon :component="IconAntDesignPlusOutlined" />
          </template>
          新增项目分类
        </n-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: ProjectCategory) => row.id"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <!-- 编辑弹窗 -->
    <ProjectCategoryFormModal
      v-model:show="showFormModal"
      :mode="formMode"
      :category-data="editingCategory"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, defineAsyncComponent } from 'vue'
import { NButton, NTag, NSpace, useMessage, useDialog, type DataTableColumns } from 'naive-ui'
import { ProjectCategoryApi, type ProjectCategory, type ProjectCategoryQueryParams } from '@/apis/projectCategory'
import ProjectCategoryFormModal from './ProjectCategoryFormModal.vue'

// 图标
const IconAntDesignSearchOutlined = defineAsyncComponent(() => import('@vicons/antd/SearchOutlined'))
const IconAntDesignReloadOutlined = defineAsyncComponent(() => import('@vicons/antd/ReloadOutlined'))
const IconAntDesignPlusOutlined = defineAsyncComponent(() => import('@vicons/antd/PlusOutlined'))
const IconAntDesignEditOutlined = defineAsyncComponent(() => import('@vicons/antd/EditOutlined'))
const IconAntDesignDeleteOutlined = defineAsyncComponent(() => import('@vicons/antd/DeleteOutlined'))

const message = useMessage()
const dialog = useDialog()

// 搜索条件
const searchKeyword = ref('')
const enabledFilter = ref<string | undefined>(undefined)

// 表格数据
const tableData = ref<ProjectCategory[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 表单弹窗
const showFormModal = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const editingCategory = ref<Partial<ProjectCategory>>({})

// 启用状态选项
const enabledOptions = [
  { label: '启用', value: 'true' },
  { label: '禁用', value: 'false' }
]

// 表格列定义
const columns: DataTableColumns<ProjectCategory> = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '分类代码',
    key: 'code',
    width: 150
  },
  {
    title: '分类名称',
    key: 'name',
    width: 150
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '排序',
    key: 'sortOrder',
    width: 80
  },
  {
    title: '状态',
    key: 'enabled',
    width: 80,
    render(row) {
      return h(NTag, {
        type: row.enabled ? 'success' : 'error'
      }, {
        default: () => row.enabled ? '启用' : '禁用'
      })
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render(row) {
      return h(NSpace, {}, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleEdit(row)
          }, {
            default: () => '编辑',
            icon: () => h(IconAntDesignEditOutlined)
          }),
          h(NButton, {
            size: 'small',
            type: 'error',
            secondary: true,
            onClick: () => handleDelete(row)
          }, {
            default: () => '删除',
            icon: () => h(IconAntDesignDeleteOutlined)
          })
        ]
      })
    }
  }
]

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const params: ProjectCategoryQueryParams = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      keyword: searchKeyword.value || undefined,
      enabled: enabledFilter.value ? enabledFilter.value === 'true' : undefined
    }
    
    const response = await ProjectCategoryApi.getProjectCategoryPage(params)
    tableData.value = response.data.data
    pagination.itemCount = response.data.total
  } catch (error) {
    console.error('获取项目分类列表失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  searchKeyword.value = ''
  enabledFilter.value = undefined
  pagination.page = 1
  fetchData()
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.page = page
  fetchData()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchData()
}

// 新增
const handleCreate = () => {
  formMode.value = 'create'
  editingCategory.value = {}
  showFormModal.value = true
}

// 编辑
const handleEdit = (category: ProjectCategory) => {
  formMode.value = 'edit'
  editingCategory.value = { ...category }
  showFormModal.value = true
}

// 删除
const handleDelete = (category: ProjectCategory) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除项目类型"${category.name}"吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await ProjectCategoryApi.deleteProjectCategory(category.id)
        message.success('删除成功')
        fetchData()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 表单提交成功
const handleFormSuccess = () => {
  showFormModal.value = false
  fetchData()
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.project-category-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>
