import { httpClient } from './index'
import type { ApiResponse, PaginationResponse } from '@/types'

export interface SystemDictOption {
  label: string
  value: string
}

export interface SystemDict {
  id: number
  dictType: string
  dictCode: string
  dictName: string
  description?: string
  sortOrder: number
  enabled: boolean
  parentId?: number
  extraProps?: string
  createTime: string
  updateTime: string
  createBy?: string
  updateBy?: string
}

export interface SystemDictQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  dictType?: string
  enabled?: boolean
}

export interface CreateSystemDictParams {
  dictType: string
  dictCode: string
  dictName: string
  description?: string
  sortOrder?: number
  enabled?: boolean
  parentId?: number
  extraProps?: string
}

export interface UpdateSystemDictParams extends CreateSystemDictParams {
  id: number
}

/**
 * 系统字典 API
 */
export class SystemDictApi {
  private static readonly BASE_PATH = '/system/dict'

  /**
   * 根据字典类型获取字典选项列表
   */
  static async getDictOptionsByType(dictType: string): Promise<ApiResponse<SystemDictOption[]>> {
    return httpClient.get<SystemDictOption[]>(`${this.BASE_PATH}/options/${dictType}`)
  }

  /**
   * 获取所有字典类型
   */
  static async getAllDictTypes(): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>(`${this.BASE_PATH}/types`)
  }

  /**
   * 获取区域选项
   */
  static async getRegionOptions(): Promise<ApiResponse<SystemDictOption[]>> {
    return httpClient.get<SystemDictOption[]>(`${this.BASE_PATH}/regions`)
  }

  /**
   * 获取资产状态选项
   */
  static async getAssetStateOptions(): Promise<ApiResponse<SystemDictOption[]>> {
    return httpClient.get<SystemDictOption[]>(`${this.BASE_PATH}/asset-states`)
  }

  /**
   * 获取业务部门选项
   */
  static async getBusinessDepartmentOptions(): Promise<ApiResponse<SystemDictOption[]>> {
    return httpClient.get<SystemDictOption[]>(`${this.BASE_PATH}/business-departments`)
  }

  /**
   * 获取使用状态选项
   */
  static async getUsageStatusOptions(): Promise<ApiResponse<SystemDictOption[]>> {
    return httpClient.get<SystemDictOption[]>(`${this.BASE_PATH}/usage-statuses`)
  }

  /**
   * 获取优先级选项
   */
  static async getPriorityOptions(): Promise<ApiResponse<SystemDictOption[]>> {
    return httpClient.get<SystemDictOption[]>(`${this.BASE_PATH}/priorities`)
  }

  /**
   * 获取站点选项
   */
  static async getSiteOptions(): Promise<ApiResponse<SystemDictOption[]>> {
    return httpClient.get<SystemDictOption[]>(`${this.BASE_PATH}/sites`)
  }

  /**
   * 分页查询系统字典
   */
  static async getSystemDictPage(params?: SystemDictQueryParams): Promise<ApiResponse<PaginationResponse<SystemDict>>> {
    return httpClient.get<PaginationResponse<SystemDict>>(`${this.BASE_PATH}/page`, params)
  }

  /**
   * 根据ID查询系统字典
   */
  static async getSystemDict(id: number): Promise<ApiResponse<SystemDict>> {
    return httpClient.get<SystemDict>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建系统字典
   */
  static async createSystemDict(data: CreateSystemDictParams): Promise<ApiResponse<SystemDict>> {
    return httpClient.post<SystemDict>(this.BASE_PATH, data)
  }

  /**
   * 更新系统字典
   */
  static async updateSystemDict(data: UpdateSystemDictParams): Promise<ApiResponse<SystemDict>> {
    const { id, ...updateData } = data
    return httpClient.put<SystemDict>(`${this.BASE_PATH}/${id}`, updateData)
  }

  /**
   * 删除系统字典
   */
  static async deleteSystemDict(id: number): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/${id}`)
  }
}
