// 流程定义API接口

import { httpClient } from './index'
import type { ApiResponse, PageResult } from '@/types'
import type {
  ProcessDefinition,
  ProcessVersion,
  ProcessDefinitionQuery,
  ProcessVersionQuery,
  CreateProcessDefinitionParams,
  UpdateProcessDefinitionParams
} from '@/types/process-definition'

/**
 * 流程定义API类
 */
export class ProcessDefinitionApi {
  private static readonly BASE_PATH = '/process-definitions'

  /**
   * 分页查询流程定义
   */
  static async getProcessDefinitions(params: ProcessDefinitionQuery): Promise<ApiResponse<PageResult<ProcessDefinition>>> {
    return httpClient.get(this.BASE_PATH, params)
  }

  /**
   * 根据ID查询流程定义
   */
  static async getProcessDefinition(id: number): Promise<ApiResponse<ProcessDefinition>> {
    return httpClient.get(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建流程定义
   */
  static async createProcessDefinition(file: File, params: CreateProcessDefinitionParams): Promise<ApiResponse<ProcessDefinition>> {
    return await httpClient.upload(this.BASE_PATH, file, params, 'bpmnFile')
  }

  /**
   * 更新流程定义基本信息
   */
  static async updateProcessDefinition(params: UpdateProcessDefinitionParams): Promise<ApiResponse<ProcessDefinition>> {
    const queryParams = new URLSearchParams()
    queryParams.append('name', params.name)
    if (params.description) {
      queryParams.append('description', params.description)
    }

    return httpClient.put(`${this.BASE_PATH}/${params.id}?${queryParams.toString()}`)
  }

  /**
   * 发布流程（创建新版本并发布）
   */
  static async publishProcessWithNewVersion(file: File, params: { processDefinitionId: number; description?: string }): Promise<ApiResponse<void>> {
    return await httpClient.upload(`${this.BASE_PATH}/${params.processDefinitionId}/publish`, file, params, 'bpmnFile')
  }



  /**
   * 根据流程Key查询
   */
  static async getByProcessKey(processKey: string): Promise<ApiResponse<ProcessDefinition>> {
    return httpClient.get(`${this.BASE_PATH}/by-key/${processKey}`)
  }
}

/**
 * 流程版本API类
 */
export class ProcessVersionApi {
  private static readonly BASE_PATH = '/process-versions'

  /**
   * 分页查询流程版本
   */
  static async getProcessVersions(params: ProcessVersionQuery): Promise<ApiResponse<PageResult<ProcessVersion>>> {
    return httpClient.get(this.BASE_PATH, params)
  }

  /**
   * 根据ID查询流程版本
   */
  static async getProcessVersion(id: number): Promise<ApiResponse<ProcessVersion>> {
    return httpClient.get(`${this.BASE_PATH}/${id}`)
  }



  /**
   * 删除流程版本
   */
  static async deleteProcessVersion(id: number): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 根据流程定义ID和版本号查询
   */
  static async getByProcessDefinitionIdAndVersion(
    processDefinitionId: number,
    version: number
  ): Promise<ApiResponse<ProcessVersion>> {
    return httpClient.get(`${this.BASE_PATH}/by-definition/${processDefinitionId}/version/${version}`)
  }

  /**
   * 根据流程定义ID查询当前发布版本
   */
  static async getPublishedVersion(processDefinitionId: number): Promise<ApiResponse<ProcessVersion>> {
    return httpClient.get(`${this.BASE_PATH}/published/${processDefinitionId}`)
  }

  /**
   * 获取BPMN文件内容
   */
  static async getBpmnContent(id: number): Promise<ApiResponse<string>> {
    return httpClient.get(`${this.BASE_PATH}/${id}/bpmn-content`)
  }

  /**
   * 下载BPMN文件
   */
  static async downloadBpmnFile(id: number): Promise<Blob> {
    const response = await httpClient.axios.get(`${this.BASE_PATH}/${id}/download`, {
      responseType: 'blob'
    })
    return response.data
  }

  /**
   * 激活版本
   */
  static async activateVersion(id: number): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_PATH}/${id}/activate`)
  }

  /**
   * 挂起版本
   */
  static async suspendVersion(id: number): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_PATH}/${id}/suspend`)
  }
}

// 导出默认实例
export const processDefinitionApi = ProcessDefinitionApi
export const processVersionApi = ProcessVersionApi
