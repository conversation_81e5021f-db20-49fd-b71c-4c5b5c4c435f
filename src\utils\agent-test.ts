// Agent功能测试工具
import { Agent<PERSON><PERSON>, Chat<PERSON><PERSON> } from '@/apis'
import type { CreateAgentParams, ChatRequestParams } from '@/types'

/**
 * Agent功能测试类
 */
export class AgentTester {
  
  /**
   * 测试创建Agent
   */
  static async testCreateAgent(): Promise<void> {
    console.log('🧪 测试创建Agent...')
    
    const testAgent: CreateAgentParams = {
      name: '测试AI助手',
      description: '这是一个用于测试的AI助手',
      status: 1,
      type: 'general',
      config: '{}',
      llmProvider: 'deepseek',
      llmModel: 'deepseek-chat',
      llmApiUrl: 'https://api.deepseek.com/v1',
      llmApiKey: 'test-api-key',
      llmParams: '{"temperature": 0.7, "max_tokens": 2000}',
      systemPrompt: '你是一个智能助手，可以帮助用户处理各种任务。',
      userPromptTemplate: '{user_input}',
      functionConfig: '{"enabled_functions": []}'
    }

    try {
      const response = await AgentApi.createAgent(testAgent)
      if (response.code === 200) {
        console.log('✅ Agent创建成功:', response.data)
        return response.data
      } else {
        console.error('❌ Agent创建失败:', response.message)
      }
    } catch (error) {
      console.error('❌ Agent创建异常:', error)
    }
  }

  /**
   * 测试获取Agent列表
   */
  static async testGetAgents(): Promise<void> {
    console.log('🧪 测试获取Agent列表...')
    
    try {
      const response = await AgentApi.getAgents({
        page: 1,
        pageSize: 10
      })
      
      if (response.code === 200) {
        console.log('✅ Agent列表获取成功:', response.data)
        return response.data.data
      } else {
        console.error('❌ Agent列表获取失败:', response.message)
      }
    } catch (error) {
      console.error('❌ Agent列表获取异常:', error)
    }
  }

  /**
   * 测试聊天功能
   */
  static async testChat(agentId: number): Promise<void> {
    console.log('🧪 测试聊天功能...')

    const chatRequest = {
      agentId,
      message: '你好，请介绍一下你的功能',
      sessionId: `test-session-${Date.now()}`,
      userId: 'test-user',
      enableFunctionCalling: true
    }

    try {
      const response = await AgentApi.chat(chatRequest)
      if (response.code === 200) {
        console.log('✅ 聊天测试成功:', response.data)
        return response.data
      } else {
        console.error('❌ 聊天测试失败:', response.message)
      }
    } catch (error) {
      console.error('❌ 聊天测试异常:', error)
    }
  }

  /**
   * 测试流式聊天功能
   */
  static async testStreamChat(agentId: number): Promise<void> {
    console.log('🧪 测试流式聊天功能...')

    const chatRequest = {
      agentId,
      message: '请用流式方式回复：介绍一下你的功能',
      sessionId: `test-stream-session-${Date.now()}`,
      userId: 'test-user',
      enableFunctionCalling: true
    }

    let receivedContent = ''

    try {
      await AgentApi.streamChat(
        chatRequest,
        // onMessage
        (chunk: string) => {
          receivedContent += chunk
          console.log('📥 接收到流式数据块:', chunk)
        },
        // onComplete
        () => {
          console.log('✅ 流式聊天测试成功，完整内容:', receivedContent)
        },
        // onError
        (error: Error) => {
          console.error('❌ 流式聊天测试失败:', error)
        }
      )
    } catch (error) {
      console.error('❌ 流式聊天测试异常:', error)
    }
  }

  /**
   * 测试快速聊天
   */
  static async testQuickChat(agentId: number): Promise<void> {
    console.log('🧪 测试快速聊天...')

    try {
      const response = await AgentApi.quickChat({
        agentId,
        message: '简单测试消息',
        userId: 'test-user'
      })

      if (response.code === 200) {
        console.log('✅ 快速聊天测试成功:', response.data)
        return response.data
      } else {
        console.error('❌ 快速聊天测试失败:', response.message)
      }
    } catch (error) {
      console.error('❌ 快速聊天测试异常:', error)
    }
  }

  /**
   * 测试Agent验证
   */
  static async testValidateAgent(agentId: number): Promise<void> {
    console.log('🧪 测试Agent验证...')

    try {
      const response = await AgentApi.validateAgent(agentId)
      if (response.code === 200) {
        console.log('✅ Agent验证成功:', response.data)
        return response.data
      } else {
        console.error('❌ Agent验证失败:', response.message)
      }
    } catch (error) {
      console.error('❌ Agent验证异常:', error)
    }
  }

  /**
   * 测试Agent初始化
   */
  static async testInitializeAgent(agentId: number): Promise<void> {
    console.log('🧪 测试Agent初始化...')

    try {
      const response = await AgentApi.initializeAgent(agentId)
      if (response.code === 200) {
        console.log('✅ Agent初始化成功')
      } else {
        console.error('❌ Agent初始化失败:', response.message)
      }
    } catch (error) {
      console.error('❌ Agent初始化异常:', error)
    }
  }

  /**
   * 测试获取Agent功能
   */
  static async testGetAgentFunctions(agentId: number): Promise<void> {
    console.log('🧪 测试获取Agent功能...')

    try {
      const response = await AgentApi.getAgentFunctions(agentId)
      if (response.code === 200) {
        console.log('✅ Agent功能获取成功:', response.data)
        return response.data
      } else {
        console.error('❌ Agent功能获取失败:', response.message)
      }
    } catch (error) {
      console.error('❌ Agent功能获取异常:', error)
    }
  }

  /**
   * 测试获取支持的提供商
   */
  static async testGetProviders(): Promise<void> {
    console.log('🧪 测试获取支持的提供商...')

    try {
      const response = await AgentApi.getSupportedProviders()
      if (response.code === 200) {
        console.log('✅ 提供商列表获取成功:', response.data)
        return response.data
      } else {
        console.error('❌ 提供商列表获取失败:', response.message)
      }
    } catch (error) {
      console.error('❌ 提供商列表获取异常:', error)
    }
  }

  /**
   * 运行完整测试套件
   */
  static async runFullTest(): Promise<void> {
    console.log('🚀 开始运行Agent功能完整测试...')
    
    try {
      // 1. 测试获取Agent列表
      const agents = await this.testGetAgents()
      
      // 2. 测试创建Agent
      const newAgent = await this.testCreateAgent()
      
      // 3. 如果有Agent，测试聊天功能
      if (agents && agents.length > 0) {
        const testAgentId = agents[0].id
        await this.testValidateAgent(testAgentId)
        await this.testInitializeAgent(testAgentId)
        await this.testGetAgentFunctions(testAgentId)
        await this.testChat(testAgentId)
        await this.testStreamChat(testAgentId)
        await this.testQuickChat(testAgentId)
      }
      
      // 4. 测试获取提供商
      await this.testGetProviders()
      
      console.log('🎉 所有测试完成！')
      
    } catch (error) {
      console.error('💥 测试过程中发生错误:', error)
    }
  }
}

// 在浏览器控制台中可以使用的快捷方法
if (typeof window !== 'undefined') {
  (window as any).testAgent = AgentTester
  console.log('💡 在控制台中使用 testAgent.runFullTest() 来运行完整测试')
}
