<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import type { Task } from '@/types/task'
import type { Project } from '@/types/project'
import type { ProjectProcess } from '@/types/project_process'
import {
  TASK_STATUS_OPTIONS
} from '@/types/task'
import { convertTaskTimesToTimestamp } from '@/utils/time'
import { UserApi } from '@/apis/user'
import { ProjectApi } from '@/apis/project'
import { ProjectProcessApi } from '@/apis/project_process'
import { useSystemOptions } from '@/composables/useSystemOptions'
import RichTextEditor from '@/components/common/RichTextEditor.vue'

interface Props {
  show: boolean
  mode: 'create' | 'edit'
  taskData?: Partial<Task>
  projectId?: number
  projectName?: string
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success', data: Partial<Task>): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  mode: 'create',
  taskData: () => ({})
})

const emit = defineEmits<Emits>()

const message = useMessage()

// 项目数据
const projects = ref<Project[]>([])
const projectsLoading = ref(false)

// 用户选项
const userOptions = ref<Array<{label: string, value: string}>>([])
const loadingUsers = ref(false)

// 任务类型选项
const taskIssueTypeOptions = ref<Array<{label: string, value: string}>>([])
const loadingTaskIssueTypes = ref(false)

// 优先级选项
const priorityOptions = ref<Array<{label: string, value: string}>>([])
const loadingPriorities = ref(false)

// 项目阶段选项
const stageOptions = ref<Array<{label: string, value: string}>>([])
const loadingStages = ref(false)

// 表单数据
const formRef = ref()
const formData = reactive<Partial<Task & {
  plannedStartTime?: number | null
  plannedEndTime?: number | null
  actualStartTime?: number | null
  actualEndTime?: number | null
  stageName?: string
}>>({
  title: '',
  issueType: '任务',
  assignee: '',
  reporter: '',
  description: '',
  status: 'open',
  plannedStartTime: undefined,
  plannedEndTime: undefined,
  actualStartTime: undefined,
  actualEndTime: undefined,
  duration: undefined,
  priority: 'Medium',
  projectId: props.projectId || undefined,
  projectName: props.projectName || '',
  stageName: ''
})

// 项目选项
const projectOptions = computed(() => {
  if (projects.value.length === 0) return []
  return projects.value.map(project => ({
    label: project.name,
    value: project.id
  }))
})

// 表单验证规则
const rules = computed(() => {
  const baseRules: any = {
    title: {
      required: true,
      message: '请输入任务标题',
      trigger: ['input', 'blur']
    },
    issueType: {
      required: true,
      message: '请选择问题类型',
      trigger: ['change', 'blur']
    },
    stageName: {
      required: true,
      message: '请选择任务阶段',
      trigger: ['change', 'blur']
    },
    priority: {
      required: true,
      message: '请选择优先级',
      trigger: ['change', 'blur']
    }
  }

  // 只有当显示项目选择器时，projectId才是必填的
  if (projects.value.length > 0) {
    baseRules.projectId = {
      required: true,
      validator: (_rule: any, value: any) => {
        if (!value && !props.projectId) {
          return new Error('请选择项目')
        }
        return true
      },
      trigger: ['change', 'blur']
    }
  }

  return baseRules
})

const loading = ref(false)

// 计算属性
const modalTitle = computed(() => props.mode === 'create' ? '新建任务' : '编辑任务')
const submitText = computed(() => props.mode === 'create' ? '创建任务' : '保存更改')

// 监听显示状态变化
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 获取项目阶段选项
const fetchStageOptions = async (projectId: number) => {
  try {
    loadingStages.value = true
    const response = await ProjectProcessApi.getStagesByProjectId(projectId)

    if (response.data && Array.isArray(response.data)) {
      // 只获取主要流程阶段
      const mainStages = response.data.filter((stage: ProjectProcess) => stage.isMainProcess)
      stageOptions.value = mainStages.map((stage: ProjectProcess) => ({
        label: stage.stageName,
        value: stage.stageName
      }))
    } else {
      stageOptions.value = []
    }
  } catch (error: any) {
    console.error('获取项目阶段失败:', error)
    message.error('获取项目阶段失败')
    stageOptions.value = []
  } finally {
    loadingStages.value = false
  }
}

// 监听任务数据变化
watch(() => props.taskData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, newData)
    // 如果是编辑模式且有项目ID，获取阶段选项
    if (props.mode === 'edit' && newData.projectId) {
      fetchStageOptions(newData.projectId)
    }
  }
}, { immediate: true, deep: true })

// 监听项目ID变化
watch(() => props.projectId, (newProjectId) => {
  if (newProjectId && props.mode === 'create') {
    formData.projectId = newProjectId
    formData.projectName = props.projectName
    // 获取阶段选项
    fetchStageOptions(newProjectId)
  }
}, { immediate: true })

// 监听模态框显示状态
watch(() => props.show, (show) => {
  if (show && props.mode === 'create') {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formRef.value?.restoreValidation()

  Object.assign(formData, {
    title: '',
    issueType: '任务',
    assignee: '',
    reporter: '',
    description: '',
    status: 'open',
    plannedStartTime: null,
    plannedEndTime: null,
    actualStartTime: null,
    actualEndTime: null,
    duration: undefined,
    priority: 'Medium',
    projectId: props.projectId || undefined,
    projectName: props.projectName || '',
    stageName: ''
  })
}

// 监听项目选择变化
const handleProjectChange = (projectId: number) => {
  const selectedProject = projects.value.find(p => p.id === projectId)
  if (selectedProject) {
    formData.projectName = selectedProject.name
  }
  // 项目变化时重新获取阶段选项
  if (projectId) {
    fetchStageOptions(projectId)
  } else {
    stageOptions.value = []
    formData.stageName = ''
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 调试信息
    console.log('提交前的表单数据:', {
      projectId: formData.projectId,
      projectName: formData.projectName,
      propsProjectId: props.projectId,
      projectsLength: projects.value.length,
      rules: rules.value
    })

    // 如果不显示项目选择器但有projectId，确保formData中有值
    if (projects.value.length === 0 && props.projectId) {
      formData.projectId = props.projectId
      formData.projectName = props.projectName || ''
    }

    await formRef.value?.validate()
    loading.value = true

    // 准备提交数据，确保时间格式正确
    const submitData = convertTaskTimesToTimestamp(formData)

    // 调试信息：检查提交数据
    console.log('表单提交数据:', {
      ...submitData,
      stageName: submitData.stageName,
      projectId: submitData.projectId
    })

    // 不在这里显示成功提示，由父组件处理
    emit('success', submitData)
    emit('update:show', false)
  } catch (error: any) {
    console.error('表单提交失败:', error)
    console.log('验证失败时的表单数据:', {
      projectId: formData.projectId,
      projectName: formData.projectName,
      propsProjectId: props.projectId,
      projectsLength: projects.value.length
    })
    message.error(error.message || '请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 处理任务类型选择器点击事件
const handleTaskIssueTypeClick = async () => {
  if (taskIssueTypeOptions.value.length === 0 && !loadingTaskIssueTypes.value) {
    loadingTaskIssueTypes.value = true
    try {
      const { loadTaskIssueTypeOptions } = useSystemOptions()
      const types = await loadTaskIssueTypeOptions()
      taskIssueTypeOptions.value = types
    } catch (error) {
      console.error('获取任务类型选项失败:', error)
      message.error('获取任务类型选项失败')
    } finally {
      loadingTaskIssueTypes.value = false
    }
  }
}

// 处理优先级选择器点击事件
const handlePriorityClick = async () => {
  if (priorityOptions.value.length === 0 && !loadingPriorities.value) {
    loadingPriorities.value = true
    try {
      const { loadPriorityOptions } = useSystemOptions()
      const priorities = await loadPriorityOptions()
      priorityOptions.value = priorities
    } catch (error) {
      console.error('获取优先级选项失败:', error)
      message.error('获取优先级选项失败')
    } finally {
      loadingPriorities.value = false
    }
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:show', false)
}

// 获取用户选项
const fetchUserOptions = async () => {
  try {
    loadingUsers.value = true
    const response = await UserApi.getUserOptions()
    if (response.data) {
      userOptions.value = response.data
    }
  } catch (error: any) {
    console.error('获取用户选项失败:', error)
    message.error('获取用户选项失败')
  } finally {
    loadingUsers.value = false
  }
}

// 处理用户选择器点击事件
const handleUserClick = async () => {
  if (userOptions.value.length === 0 && !loadingUsers.value) {
    await fetchUserOptions()
  }
}



// 获取项目数据
const fetchProjects = async () => {
  try {
    projectsLoading.value = true

    const response = await ProjectApi.getProjects({
      page: 1,
      pageSize: 1000 // 获取大量项目
    })

    if (response.data) {
      let projectList: Project[] = []

      // 处理不同的响应格式
      if (response.data.data && Array.isArray(response.data.data)) {
        // 分页格式：PaginationResponse<Project>
        projectList = response.data.data
      } else if (Array.isArray(response.data)) {
        // 数组格式
        projectList = response.data
      } else {
        console.warn('未知的项目响应格式:', response.data)
        projectList = []
      }

      projects.value = projectList
    }
  } catch (error: any) {
    console.error('获取项目列表失败:', error)
    message.error(error.message || '获取项目列表失败')
  } finally {
    projectsLoading.value = false
  }
}

// 组件挂载时获取项目数据
onMounted(() => {
  fetchProjects()
  // 如果有项目ID，获取阶段选项
  if (props.projectId) {
    fetchStageOptions(props.projectId)
  }
})
</script>

<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="modalTitle"
    class="task-form-modal"
    style="width: 900px"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="120px"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="2" :x-gap="24">
        <n-gi>
          <n-form-item label="任务标题" path="title">
            <n-input
              v-model:value="formData.title"
              placeholder="请输入任务标题"
              clearable
            />
          </n-form-item>
        </n-gi>
        
        <n-gi>
          <n-form-item label="问题类型" path="issueType">
            <n-select
              v-model:value="formData.issueType"
              :options="taskIssueTypeOptions"
              :loading="loadingTaskIssueTypes"
              placeholder="请选择问题类型"
              @click="handleTaskIssueTypeClick"
            />
          </n-form-item>
        </n-gi>
        
        <n-gi v-if="projects.length > 0">
          <n-form-item label="所属项目" path="projectId">
            <n-select
              v-model:value="formData.projectId"
              :options="projectOptions"
              :loading="projectsLoading"
              placeholder="请选择项目"
              @update:value="handleProjectChange"
            />
          </n-form-item>
        </n-gi>
        
        <n-gi>
          <n-form-item label="阶段" path="stageName">
            <n-select
              v-model:value="formData.stageName"
              :options="stageOptions"
              :loading="loadingStages"
              placeholder="请先选择项目，然后选择阶段"
              :disabled="!formData.projectId"
              clearable
            />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="优先级" path="priority">
            <n-select
              v-model:value="formData.priority"
              :options="priorityOptions"
              :loading="loadingPriorities"
              placeholder="请选择优先级"
              @click="handlePriorityClick"
            />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="预估时长(小时)">
            <n-input-number
              v-model:value="formData.duration"
              placeholder="请输入预估时长"
              :min="0"
              :step="0.5"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>
        
        <n-gi>
          <n-form-item label="指派人" path="assignee">
            <n-select
              v-model:value="formData.assignee"
              :options="userOptions"
              :loading="loadingUsers"
              placeholder="请选择指派人"
              filterable
              clearable
              @click="handleUserClick"
            />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="报告人" path="reporter">
            <n-select
              v-model:value="formData.reporter"
              :options="userOptions"
              :loading="loadingUsers"
              placeholder="请选择报告人"
              filterable
              clearable
              @click="handleUserClick"
            />
          </n-form-item>
        </n-gi>
        
        <n-gi>
          <n-form-item label="计划开始时间">
            <n-date-picker
              v-model:value="formData.plannedStartTime"
              type="date"
              placeholder="请选择计划开始时间"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="计划结束时间">
            <n-date-picker
              v-model:value="formData.plannedEndTime"
              type="date"
              placeholder="请选择计划结束时间"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="实际开始时间">
            <n-date-picker
              v-model:value="formData.actualStartTime"
              type="date"
              placeholder="请选择实际开始时间"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="实际结束时间">
            <n-date-picker
              v-model:value="formData.actualEndTime"
              type="date"
              placeholder="请选择实际结束时间"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>

        <n-gi v-if="mode === 'edit'">
          <n-form-item label="状态" path="status">
            <n-select
              v-model:value="formData.status"
              :options="TASK_STATUS_OPTIONS"
              placeholder="请选择状态"
            />
          </n-form-item>
        </n-gi>
        
        <n-gi :span="2">
          <n-form-item label="任务描述">
            <RichTextEditor
              v-model="formData.description"
              placeholder="请输入任务描述..."
              height="180px"
              max-height="300px"
              toolbar="essential"
              show-word-count
              :max-length="2000"
            />
          </n-form-item>
        </n-gi>
      </n-grid>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button v-if="mode === 'create'" @click="resetForm">重置</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ submitText }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<style lang="less" scoped>
:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-modal) {
  max-height: 90vh;
}

:deep(.n-dialog__content) {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
