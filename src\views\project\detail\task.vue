<script setup lang="ts">
import TaskView from '@/components/task/TaskView.vue'

const props = defineProps<{
  projectInfo: {
    id: number
    name: string
    [key: string]: any
  }
}>()

// TaskView 组件现在独立管理数据和事件处理
</script>

<template>
  <div class="project-task-container p-4">
    <TaskView
      :show-project-filter="false"
      :project-id="projectInfo.id"
      :title="`${projectInfo.name} - 任务列表`"
      :editable="true"
    />
  </div>
</template>

<style scoped>
.project-task-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素可以收缩 */
}
</style>
