package com.vwatj.ppms.agent.intent;

import lombok.Data;

import java.util.Map;

/**
 * 意图识别结果
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
public class IntentResult {
    
    /**
     * 识别的意图/功能名称
     * 如: project_management, task_management
     */
    private String intent;
    
    /**
     * 具体的操作动作
     * 如: query_project, query_task, get_stats
     */
    private String action;
    
    /**
     * 置信度 (0.0 - 1.0)
     */
    private Double confidence;
    
    /**
     * 提取的参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 识别理由/推理过程
     */
    private String reasoning;
    
    /**
     * 是否需要降级到硬编码规则
     */
    private Boolean needsFallback = false;
    
    /**
     * 检查是否为有效的意图识别结果
     */
    public boolean isValid() {
        return intent != null && 
               !intent.equals("unknown") && 
               action != null && 
               !action.equals("fallback") &&
               confidence != null && 
               confidence > 0.0;
    }
    
    /**
     * 检查是否为高置信度结果
     */
    public boolean isHighConfidence() {
        return confidence != null && confidence >= 0.7;
    }
    
    /**
     * 获取参数值
     */
    public Object getParameter(String key) {
        return parameters != null ? parameters.get(key) : null;
    }
    
    /**
     * 获取字符串类型参数
     */
    public String getStringParameter(String key) {
        Object value = getParameter(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 获取长整型参数
     */
    public Long getLongParameter(String key) {
        Object value = getParameter(key);
        if (value == null) return null;
        
        try {
            if (value instanceof Number) {
                return ((Number) value).longValue();
            }
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 检查是否包含指定参数
     */
    public boolean hasParameter(String key) {
        return parameters != null && parameters.containsKey(key);
    }
}
