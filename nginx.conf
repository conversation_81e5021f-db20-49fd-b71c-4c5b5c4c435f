user root;
worker_processes auto;

events {
  worker_connections 768;
}

http {
  server_tokens off;
  default_type application/octet-stream;
  include /etc/nginx/mime.types;
  gzip on;
  server {
    listen 80;
    server_name localhost;
    add_header Cache-Control no-cache;
    add_header Cache-Control private;

    location / {
      root /usr/share/nginx/html;
      index index.html;
      try_files $uri $uri/ /index.html;
    }
    location /api {
      client_max_body_size 100m;
      proxy_pass http://ppms-server:8080/api;
    }
    location /public-assets {
      client_max_body_size 100m;
      proxy_pass http://*************:33447/public-assets;
    }
    location /public-logo {
      client_max_body_size 100m;
      proxy_pass http://*************:33447/public-logo;
    }
  }
}
