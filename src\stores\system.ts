// 系统管理状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { systemApi, type CreateSystemAssetParams, type UpdateSystemAssetParams } from '@/apis'
import type { SystemAsset, SystemAssetQuery } from '@/types/system'
import { handleApiError, showMessage } from '@/utils/errorHandler'

export const useSystemStore = defineStore('system', () => {
  // 状态
  const assets = ref<SystemAsset[]>([])
  const currentAsset = ref<SystemAsset | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 12,
    total: 0
  })

  // 选项数据
  const pmOwnerOptions = ref<Array<{ value: string; label: string }>>([])

  // 系统配置
  const systemConfig = ref({
    siteName: '',
    siteDescription: '',
    logo: '',
    theme: 'light' as 'light' | 'dark' | 'auto',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm:ss'
  })

  // 计算属性
  const activeAssets = computed(() => assets.value.filter((a: SystemAsset) => a.assetState === 'running'))
  const inactiveAssets = computed(() => assets.value.filter((a: SystemAsset) => a.assetState !== 'running'))

  // 获取系统资产列表
  const fetchAssets = async (params?: SystemAssetQuery) => {
    try {
      loading.value = true
      const response = await systemApi.getSystemAssets({
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...params
      })

      assets.value = response.data.data
      pagination.value.total = response.data.total
      pagination.value.page = response.data.page
      pagination.value.pageSize = response.data.pageSize
    } catch (error: any) {
      const errorMessage = handleApiError(error, '获取系统资产列表失败')
      showMessage('error', errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取系统资产详情
  const fetchAsset = async (id: number) => {
    try {
      loading.value = true
      const response = await systemApi.getSystemAsset(id)
      currentAsset.value = response.data
      return response.data
    } catch (error: any) {
      const errorMessage = handleApiError(error, '获取系统资产详情失败')
      showMessage('error', errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建系统资产
  const createAsset = async (data: CreateSystemAssetParams) => {
    try {
      loading.value = true
      const response = await systemApi.createSystemAsset(data)
      assets.value.unshift(response.data)
      pagination.value.total += 1
      showMessage('success', '系统资产创建成功')
      return response.data
    } catch (error: any) {
      const errorMessage = handleApiError(error, '创建系统资产失败')
      showMessage('error', errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新系统资产
  const updateAsset = async (data: UpdateSystemAssetParams) => {
    try {
      loading.value = true
      const response = await systemApi.updateSystemAsset(data)

      // 更新列表中的资产
      const index = assets.value.findIndex((a: SystemAsset) => a.id === data.id)
      if (index !== -1) {
        assets.value[index] = response.data
      }

      // 更新当前资产
      if (currentAsset.value?.id === data.id) {
        currentAsset.value = response.data
      }

      showMessage('success', '系统资产更新成功')
      return response.data
    } catch (error: any) {
      const errorMessage = handleApiError(error, '更新系统资产失败')
      showMessage('error', errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除系统资产
  const deleteAsset = async (id: number) => {
    try {
      loading.value = true
      await systemApi.deleteSystemAsset(id)
      
      // 从列表中移除
      const index = assets.value.findIndex((a: SystemAsset) => a.id === id)
      if (index !== -1) {
        assets.value.splice(index, 1)
        pagination.value.total -= 1
      }
      
      // 清空当前资产
      if (currentAsset.value?.id === id) {
        currentAsset.value = null
      }
      
      showMessage('success', '系统资产删除成功')
    } catch (error: any) {
      const errorMessage = handleApiError(error, '删除系统资产失败')
      showMessage('error', errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取选项数据
  const fetchOptions = async () => {
    try {
      // 使用静态选项数据

      // 如果需要从API获取PM负责人选项
      // const owners = await optionsApi.getPmOwnerOptions()
      // pmOwnerOptions.value = owners.data
    } catch (error: any) {
      console.error('获取选项数据失败:', error)
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.page = page
    pagination.value.pageSize = pageSize
  }

  // 下载导入模板
  const downloadTemplate = async () => {
    try {
      await systemApi.exportAssets(undefined, true)
      showMessage('success', '模板下载成功')
    } catch (error: any) {
      const errorMessage = handleApiError(error, '下载模板失败')
      showMessage('error', errorMessage)
      throw error
    }
  }

  // 导出系统资产数据
  const exportAssets = async (queryParams?: any) => {
    try {
      await systemApi.exportAssets(queryParams, false)
      showMessage('success', '数据导出成功')
    } catch (error: any) {
      const errorMessage = handleApiError(error, '导出数据失败')
      showMessage('error', errorMessage)
      throw error
    }
  }

  // 导入系统资产（SSE方式）
  const importAssets = async (file: File, mode: 'incremental' | 'overwrite') => {
    return new Promise((resolve, reject) => {
      loading.value = true

      systemApi.importAssets(
        file,
        mode,
        // onProgress
        (data) => {
          // 进度更新可以在这里处理，如果需要的话
        },
        // onComplete
        async (result) => {
          try {
            // 导入成功后刷新列表
            if (result.success > 0) {
              // 重新获取第一页数据
              await fetchAssets({ page: 1, pageSize: pagination.value.pageSize })
            }
            resolve(result)
          } catch (error) {
            reject(error)
          } finally {
            loading.value = false
          }
        },
        // onError
        (error) => {
          loading.value = false
          const errorMessage = handleApiError(error, '导入系统资产失败')
          showMessage('error', errorMessage)
          reject(error)
        }
      ).catch((error) => {
        loading.value = false
        const errorMessage = handleApiError(error, '导入系统资产失败')
        showMessage('error', errorMessage)
        reject(error)
      })
    })
  }

  // 重置状态
  const reset = () => {
    assets.value = []
    currentAsset.value = null
    loading.value = false
    pagination.value = {
      page: 1,
      pageSize: 12,
      total: 0
    }
  }

  return {
    // 状态
    assets,
    currentAsset,
    loading,
    pagination,
    pmOwnerOptions,
    systemConfig,
    
    // 计算属性
    activeAssets,
    inactiveAssets,
    
    // 方法
    fetchAssets,
    fetchAsset,
    createAsset,
    updateAsset,
    deleteAsset,
    fetchOptions,
    downloadTemplate,
    exportAssets,
    importAssets,
    setPagination,
    reset
  }
})
