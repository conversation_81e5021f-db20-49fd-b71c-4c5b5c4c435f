// 项目流程相关 API 接口
import { httpClient } from './index'
import type {
  ApiResponse
} from '@/types'

/**
 * 项目流程 API 类
 */
export class ProjectProcessApi {
  private static readonly BASE_PATH = '/project-process'

  /**
   * 根据项目ID获取所有流程阶段
   */
  static async getStagesByProjectId(projectId: number): Promise<ApiResponse<any[]>> {
    return httpClient.get<any[]>(`${this.BASE_PATH}/project/${projectId}`)
  }

  /**
   * 提交流程表单数据
   */
  static async submitStageForm(
    projectId: number,
    stageKey: string,
    formKey: string,
    formData: Record<string, any>
  ): Promise<ApiResponse<string>> {
    return httpClient.post<string>(
      `${this.BASE_PATH}/project/${projectId}/stage/${stageKey}/form/${formKey}/submit`,
      formData
    )
  }

  /**
   * 编辑流程表单数据
   */
  static async updateStageFormData(
    projectId: number,
    stageKey: string,
    formKey: string,
    formData: Record<string, any>
  ): Promise<ApiResponse<string>> {
    return httpClient.put<string>(
      `${this.BASE_PATH}/project/${projectId}/stage/${stageKey}/form/${formKey}/data`,
      formData
    )
  }

  /**
   * 获取流程表单数据
   */
  static async getStageFormData(
    projectId: number,
    stageKey: string,
    formKey: string
  ): Promise<ApiResponse<Record<string, any>>> {
    return httpClient.get<Record<string, any>>(
      `${this.BASE_PATH}/project/${projectId}/stage/${stageKey}/form/${formKey}/data`
    )
  }
}

// 导出默认实例
export const projectProcessApi = ProjectProcessApi
