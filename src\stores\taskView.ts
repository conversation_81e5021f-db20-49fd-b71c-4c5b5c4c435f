import { defineStore } from 'pinia'
import { ref } from 'vue'

export type TaskViewMode = 'list' | 'table' | 'kanban'

export const useTaskViewStore = defineStore('taskView', () => {
  // 默认视图模式为看板
  const viewMode = ref<TaskViewMode>('kanban')
  
  // 从localStorage加载保存的视图模式
  const loadViewMode = () => {
    const saved = localStorage.getItem('task-view-mode')
    if (saved && ['list', 'table', 'kanban'].includes(saved)) {
      viewMode.value = saved as TaskViewMode
    }
  }
  
  // 设置视图模式并保存到localStorage
  const setViewMode = (mode: TaskViewMode) => {
    viewMode.value = mode
    localStorage.setItem('task-view-mode', mode)
  }
  
  // 初始化时加载保存的设置
  loadViewMode()
  
  return {
    viewMode,
    setViewMode,
    loadViewMode
  }
})
