/**
 * 用户相关工具函数
 */

// 用户信息接口
export interface UserInfo {
  id: number
  name: string
  email?: string
  avatar?: string
  role?: string
}

// 默认用户信息（开发环境使用）
const DEFAULT_USER: UserInfo = {
  id: 1,
  name: '当前用户',
  email: '<EMAIL>',
  role: 'developer'
}

/**
 * 获取当前用户信息
 * TODO: 这里应该从实际的用户管理系统获取
 */
export const getCurrentUser = (): UserInfo => {
  // 尝试从localStorage获取用户信息
  try {
    const userStr = localStorage.getItem('currentUser')
    if (userStr) {
      const user = JSON.parse(userStr)
      return {
        id: user.id || DEFAULT_USER.id,
        name: user.name || user.username || DEFAULT_USER.name,
        email: user.email,
        avatar: user.avatar,
        role: user.role
      }
    }
  } catch (error) {
    console.warn('获取用户信息失败:', error)
  }

  // 尝试从sessionStorage获取
  try {
    const userStr = sessionStorage.getItem('user')
    if (userStr) {
      const user = JSON.parse(userStr)
      return {
        id: user.id || DEFAULT_USER.id,
        name: user.name || user.username || DEFAULT_USER.name,
        email: user.email,
        avatar: user.avatar,
        role: user.role
      }
    }
  } catch (error) {
    console.warn('从sessionStorage获取用户信息失败:', error)
  }

  // 返回默认用户信息
  return DEFAULT_USER
}

/**
 * 设置当前用户信息
 */
export const setCurrentUser = (user: UserInfo): void => {
  try {
    localStorage.setItem('currentUser', JSON.stringify(user))
  } catch (error) {
    console.error('保存用户信息失败:', error)
  }
}

/**
 * 清除当前用户信息
 */
export const clearCurrentUser = (): void => {
  try {
    localStorage.removeItem('currentUser')
    sessionStorage.removeItem('user')
  } catch (error) {
    console.error('清除用户信息失败:', error)
  }
}

/**
 * 检查用户是否有指定权限
 */
export const hasPermission = (permission: string): boolean => {
  const user = getCurrentUser()
  // TODO: 实现实际的权限检查逻辑
  return user.role === 'admin' || user.role === 'developer'
}

/**
 * 检查是否为管理员
 */
export const isAdmin = (): boolean => {
  const user = getCurrentUser()
  return user.role === 'admin'
}

/**
 * 获取用户显示名称
 */
export const getUserDisplayName = (user?: UserInfo): string => {
  const currentUser = user || getCurrentUser()
  return currentUser.name || currentUser.email || '未知用户'
}
