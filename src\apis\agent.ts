// Agent相关 API 接口
import { httpClient, buildPaginationParams } from './index'
import type {
  Agent,
  AgentQueryParams,
  CreateAgentParams,
  UpdateAgentParams,
  ApiResponse,
  PaginationResponse
} from '@/types'

/**
 * Agent API 类
 */
export class AgentApi {
  private static readonly BASE_PATH = '/agents'

  /**
   * 获取Agent列表
   */
  static async getAgents(params?: AgentQueryParams): Promise<ApiResponse<PaginationResponse<Agent>>> {
    return httpClient.get<PaginationResponse<Agent>>(this.BASE_PATH, params)
  }

  /**
   * 根据ID获取Agent详情
   */
  static async getAgent(id: number): Promise<ApiResponse<Agent>> {
    return httpClient.get<Agent>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建Agent
   */
  static async createAgent(params: CreateAgentParams): Promise<ApiResponse<Agent>> {
    return httpClient.post<Agent>(this.BASE_PATH, params)
  }

  /**
   * 更新Agent
   */
  static async updateAgent(id: number, params: Partial<UpdateAgentParams>): Promise<ApiResponse<Agent>> {
    return httpClient.put<Agent>(`${this.BASE_PATH}/${id}`, params)
  }

  /**
   * 删除Agent
   */
  static async deleteAgent(id: number): Promise<ApiResponse<string>> {
    return httpClient.delete<string>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 切换Agent状态
   */
  static async toggleAgentStatus(id: number): Promise<ApiResponse<string>> {
    return httpClient.patch<string>(`${this.BASE_PATH}/${id}/toggle-status`)
  }

  // ==================== 聊天相关接口 ====================

  /**
   * 发送聊天消息
   */
  static async chat(params: any): Promise<ApiResponse<any>> {
    return httpClient.post<any>(`${this.BASE_PATH}/chat`, params)
  }

  /**
   * 发送流式聊天消息
   */
  static async streamChat(params: any, onMessage: (chunk: string) => void, onComplete?: () => void, onError?: (error: Error) => void): Promise<void> {
    try {
      const response = await fetch(`${httpClient.axios.defaults.baseURL}${this.BASE_PATH}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        },
        body: JSON.stringify(params)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          onComplete?.()
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        onMessage(chunk)
      }
    } catch (error) {
      onError?.(error as Error)
      throw error
    }
  }

  /**
   * 发送SSE流式聊天消息
   */
  static streamChatSSE(params: any, onMessage: (data: string) => void, onComplete?: () => void, onError?: (error: Error) => void): EventSource {
    const url = new URL(`${httpClient.axios.defaults.baseURL}${this.BASE_PATH}/chat/stream`)

    // 将参数作为查询参数传递（对于SSE）
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, typeof params[key] === 'object' ? JSON.stringify(params[key]) : params[key])
      }
    })

    const eventSource = new EventSource(url.toString())

    eventSource.onmessage = (event) => {
      try {
        onMessage(event.data)
      } catch (error) {
        onError?.(error as Error)
      }
    }

    eventSource.onerror = () => {
      const error = new Error('SSE连接错误')
      onError?.(error)
      eventSource.close()
    }

    eventSource.addEventListener('complete', () => {
      onComplete?.()
      eventSource.close()
    })

    return eventSource
  }

  /**
   * 快速问答
   */
  static async quickChat(params: any): Promise<ApiResponse<string>> {
    return httpClient.post<string>(`${this.BASE_PATH}/chat/quick`, params)
  }

  // ==================== Agent管理接口 ====================

  /**
   * 初始化Agent
   */
  static async initializeAgent(agentId: number): Promise<ApiResponse<void>> {
    return httpClient.post<void>(`${this.BASE_PATH}/${agentId}/initialize`)
  }

  /**
   * 验证Agent配置
   */
  static async validateAgent(agentId: number): Promise<ApiResponse<boolean>> {
    return httpClient.post<boolean>(`${this.BASE_PATH}/${agentId}/validate`)
  }

  /**
   * 获取Agent功能列表
   */
  static async getAgentFunctions(agentId: number): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>(`${this.BASE_PATH}/${agentId}/functions`)
  }

  /**
   * 执行Agent功能
   */
  static async executeFunction(agentId: number, functionName: string, parameters: Record<string, any>): Promise<ApiResponse<any>> {
    return httpClient.post<any>(`${this.BASE_PATH}/${agentId}/functions/${functionName}`, parameters)
  }

  /**
   * 获取Agent状态
   */
  static async getAgentStatus(agentId: number): Promise<ApiResponse<Record<string, any>>> {
    return httpClient.get<Record<string, any>>(`${this.BASE_PATH}/${agentId}/status`)
  }

  // ==================== 系统功能接口 ====================

  /**
   * 获取所有可用的业务功能
   */
  static async getAllAvailableFunctions(): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>(`${this.BASE_PATH}/functions`)
  }

  /**
   * 获取业务功能的Schema定义
   */
  static async getFunctionSchemas(): Promise<ApiResponse<Record<string, Record<string, any>>>> {
    return httpClient.get<Record<string, Record<string, any>>>(`${this.BASE_PATH}/functions/schemas`)
  }

  /**
   * 获取支持的LLM提供商列表
   */
  static async getSupportedProviders(): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>(`${this.BASE_PATH}/providers`)
  }

  /**
   * 检查LLM提供商是否支持
   */
  static async isProviderSupported(provider: string): Promise<ApiResponse<boolean>> {
    return httpClient.get<boolean>(`${this.BASE_PATH}/providers/${provider}/supported`)
  }

  /**
   * 健康检查
   */
  static async health(): Promise<ApiResponse<Record<string, any>>> {
    return httpClient.get<Record<string, any>>(`${this.BASE_PATH}/health`)
  }
}
