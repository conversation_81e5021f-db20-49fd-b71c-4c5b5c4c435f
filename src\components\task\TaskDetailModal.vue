<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    class="task-detail-modal"
    style="width: 800px; max-height: 80vh"
  >
    <template #header>
      <div class="modal-title">
        <span v-if="task?.taskNo" class="task-no">{{ task.taskNo }}</span>
        {{ task?.title || '任务详情' }}
      </div>
    </template>
    <div v-if="task" class="task-detail-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <n-grid :cols="2" :x-gap="24" :y-gap="16">
          <n-gi>
            <div class="detail-item">
              <span class="label">任务编号：</span>
              <span class="value">{{ task.taskNo }}</span>
            </div>
          </n-gi>
          <n-gi>
            <div class="detail-item">
              <span class="label">问题类型：</span>
              <n-tag :type="getIssueTypeInfo(task.issueType).type" size="small">
                {{ getIssueTypeInfo(task.issueType).text }}
              </n-tag>
            </div>
          </n-gi>
          <n-gi>
            <div class="detail-item">
              <span class="label">指派人：</span>
              <span class="value">{{ task.assignee }}</span>
            </div>
          </n-gi>
          <n-gi>
            <div class="detail-item">
              <span class="label">报告人：</span>
              <span class="value">{{ task.reporter }}</span>
            </div>
          </n-gi>
          <n-gi>
            <div class="detail-item">
              <span class="label">状态：</span>
              <n-tag :type="getStatusInfo(task.status).type" size="small">
                {{ getStatusInfo(task.status).text }}
              </n-tag>
            </div>
          </n-gi>
          <n-gi>
            <div class="detail-item">
              <span class="label">优先级：</span>
              <n-tag :type="getPriorityInfo(task.priority).type" size="small">
                {{ getPriorityInfo(task.priority).text }}
              </n-tag>
            </div>
          </n-gi>
        </n-grid>
      </div>

      <!-- 时间信息 -->
      <div class="detail-section">
        <h3 class="section-title">时间信息</h3>
        <n-grid :cols="2" :x-gap="24" :y-gap="16">
          <n-gi>
            <div class="detail-item">
              <span class="label">计划开始时间：</span>
              <span class="value">{{ formatDate(task.plannedStartTime) }}</span>
            </div>
          </n-gi>
          <n-gi>
            <div class="detail-item">
              <span class="label">计划结束时间：</span>
              <span class="value">{{ formatDate(task.plannedEndTime) }}</span>
            </div>
          </n-gi>
          <n-gi v-if="task.actualStartTime">
            <div class="detail-item">
              <span class="label">实际开始时间：</span>
              <span class="value">{{ formatDate(task.actualStartTime) }}</span>
            </div>
          </n-gi>
          <n-gi v-if="task.actualEndTime">
            <div class="detail-item">
              <span class="label">实际结束时间：</span>
              <span class="value">{{ formatDate(task.actualEndTime) }}</span>
            </div>
          </n-gi>
          <n-gi v-if="task.duration">
            <div class="detail-item">
              <span class="label">预估时长：</span>
              <span class="value">{{ task.duration }}小时</span>
            </div>
          </n-gi>
        </n-grid>
      </div>

      <!-- 任务描述 -->
      <div v-if="task.description" class="detail-section">
        <h3 class="section-title">任务描述</h3>
        <div class="description-content" v-html="task.description"></div>
      </div>
    </div>

    <template #action>
      <n-space>
        <n-button @click="handleClose">关闭</n-button>
        <n-button type="primary" @click="handleEdit">编辑</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Task } from '@/types/task'
import { getIssueTypeInfo, getStatusInfo, getPriorityInfo, formatDate } from '@/types/task'

interface Props {
  show: boolean
  task?: Task | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'edit', task: Task): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  task: null
})

const emit = defineEmits<Emits>()

// 双向绑定显示状态
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 关闭模态框
const handleClose = () => {
  emit('update:show', false)
}

// 编辑任务
const handleEdit = () => {
  if (props.task) {
    emit('edit', props.task)
  }
}
</script>

<style lang="less" scoped>
.modal-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-no {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.task-detail-content {
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;

  .label {
    font-weight: 500;
    color: #666;
    min-width: 100px;
    flex-shrink: 0;
  }

  .value {
    color: #262626;
    flex: 1;
  }
}

.description-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  line-height: 1.6;

  :deep(p) {
    margin: 0 0 12px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  :deep(ul), :deep(ol) {
    margin: 12px 0;
    padding-left: 20px;
  }

  :deep(blockquote) {
    border-left: 4px solid #1890ff;
    margin: 12px 0;
    padding: 8px 12px;
    background-color: #f6f8fa;
  }

  :deep(code) {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 85%;
  }

  :deep(pre) {
    background-color: #f6f8fa;
    border-radius: 6px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;

    th, td {
      border: 1px solid #e8e8e8;
      padding: 8px 12px;
      text-align: left;
    }

    th {
      background-color: #fafafa;
      font-weight: 600;
    }
  }
}
</style>
