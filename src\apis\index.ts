// API 基础配置和工具函数
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { ApiResponse, PaginationParams } from '@/types'

// API 基础配置
export const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || (import.meta.env.DEV ? '/api' : '/api'),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
}

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const

// API 错误类型
export class ApiError extends Error {
  constructor(
    public code: number,
    public message: string,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}



// 基础 HTTP 客户端
class HttpClient {
  public axios: AxiosInstance

  constructor(baseURL: string = API_CONFIG.baseURL) {
    this.axios = axios.create({
      baseURL,
      timeout: API_CONFIG.timeout,
      headers: API_CONFIG.headers
    })

    // 请求拦截器
    this.axios.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.axios.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(response.data.code);

        if (response.data.code !== 200) {
          throw new ApiError(response.data.code, response.data.message, response.data)
        }
        return response.data
      },
      (error) => {
        if (error.response) {
          const { status, data } = error.response
          throw new ApiError(status, data?.message || '请求失败', data)
        } else if (error.request) {
          throw new ApiError(0, '网络连接失败', error)
        } else {
          throw new ApiError(0, '请求配置错误', error)
        }
      }
    )
  }

  private async request<T = any>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.axios.request(config)
      return response as unknown as ApiResponse<T>
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError(0, '网络请求失败', error)
    }
  }

  async get<T = any>(endpoint: string, params?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url: endpoint,
      params,
      ...config
    })
  }

  async post<T = any>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url: endpoint,
      data,
      ...config
    })
  }

  async put<T = any>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url: endpoint,
      data,
      ...config
    })
  }

  async patch<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PATCH',
      url: endpoint,
      data
    })
  }

  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url: endpoint
    })
  }

  // 文件上传
  async upload<T = any>(endpoint: string, file: File, data?: Record<string, any>, fileFieldName: string = 'file'): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append(fileFieldName, file)

    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, String(value))
        }
      })
    }

    return this.request<T>({
      method: 'POST',
      url: endpoint,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 创建默认的 HTTP 客户端实例
export const httpClient = new HttpClient()

// 分页查询工具函数
export const buildPaginationParams = (params: PaginationParams) => {
  return {
    page: params.page,
    pageSize: params.pageSize,
    sortBy: params.sortBy,
    sortOrder: params.sortOrder,
  }
}

// 导出所有 API 模块
export * from './project'
export * from './project_process'
export * from './process_form'
export * from './task'
export * from './document'
export * from './user'
export * from './system'
export * from './process-definition'
export * from './agent'
export * from './chat'


// 导出类型
export type { ApiResponse, PaginationParams }
