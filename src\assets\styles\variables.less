// ===== 基础设计令牌 =====
// 简化的设计系统，只保留必要的变量

:root {
  // 主色调
  --primary-color: #18a058;

  // 文本色
  --text-color: #333333;
  --text-color-secondary: #666666;
  --text-color-disabled: #cccccc;

  // 背景色
  --bg-color: #ffffff;
  --bg-color-page: #f5f7fa;

  // 边框色
  --border-color: #e0e0e6;

  // 基础尺寸
  --border-radius: 6px;
  --spacing: 16px;

  // 动画
  --transition-duration: 0.2s;
}

// ===== Less 变量（向后兼容） =====
// 基础变量，保持简单

@primary-color: var(--primary-color);
@text-color: var(--text-color);
@text-color-secondary: var(--text-color-secondary);
@bg-color: var(--bg-color);
@page-bg-color: var(--bg-color-page);
@border-color: var(--border-color);
@border-radius: var(--border-radius);
@spacing: var(--spacing);
