// 流程定义相关类型定义

// 流程定义状态枚举
export enum ProcessDefinitionStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  SUSPENDED = 'SUSPENDED'
}

// 流程定义实体（主表）
export interface ProcessDefinition {
  id: number
  processKey: string
  name: string
  description?: string
  publishedVersion?: number
  status: ProcessDefinitionStatus
  createdBy: string
  createdTime: string
  updatedBy?: string
  updatedTime?: string
  deleted: boolean
}

// 流程版本实体
export interface ProcessVersion {
  id: number
  processDefinitionId: number
  version: number
  description?: string
  fileName: string
  filePath: string
  fileSize: number
  deploymentId?: string
  status: ProcessDefinitionStatus
  isPublished: boolean
  publishTime?: string
  createdBy: string
  createdTime: string
  updatedBy?: string
  updatedTime?: string
  deleted: boolean
}

// 流程定义查询参数
export interface ProcessDefinitionQuery {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: string
  name?: string
  processKey?: string
  status?: ProcessDefinitionStatus
  createdBy?: string
}

// 流程版本查询参数
export interface ProcessVersionQuery {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: string
  processDefinitionId?: number
  version?: number
  status?: ProcessDefinitionStatus
  createdBy?: string
}

// 创建流程定义参数
export interface CreateProcessDefinitionParams {
  processKey: string
  name: string
  description?: string
  versionDescription?: string
}

// 创建流程版本参数
export interface CreateProcessVersionParams {
  processDefinitionId: number
  description?: string
}

// 发布流程参数
export interface PublishProcessParams {
  processDefinitionId: number
  version: number
}

// 更新流程定义参数
export interface UpdateProcessDefinitionParams {
  id: number
  name: string
  description?: string
}

// 流程定义表格列配置
export interface ProcessDefinitionTableColumn {
  key: string
  title: string
  width?: number
  align?: 'left' | 'center' | 'right'
  ellipsis?: boolean
  render?: (row: ProcessDefinition) => any
}

// 流程版本表格列配置
export interface ProcessVersionTableColumn {
  key: string
  title: string
  width?: number
  align?: 'left' | 'center' | 'right'
  ellipsis?: boolean
  render?: (row: ProcessVersion) => any
}

// 流程定义操作类型
export type ProcessDefinitionAction = 'view' | 'edit' | 'publish' | 'activate' | 'suspend' | 'delete' | 'versions'

// 流程版本操作类型
export type ProcessVersionAction = 'view' | 'delete' | 'download'

// 文件上传状态
export interface FileUploadStatus {
  status: 'uploading' | 'success' | 'error' | 'removed'
  percentage: number
  file?: File
  response?: any
  error?: string
}

// 流程状态选项
export interface ProcessStatusOption {
  label: string
  value: ProcessDefinitionStatus | undefined
}

// 流程定义扩展信息（包含版本信息）
export interface ProcessDefinitionWithVersions extends ProcessDefinition {
  versions?: ProcessVersion[]
  versionCount?: number
}
