// 流程表单相关 API 接口
import { httpClient } from './index'
import type {
  ApiResponse,
  ProcessForm
} from '@/types'

/**
 * 流程表单 API 类
 */
export class ProcessFormApi {
  private static readonly BASE_PATH = '/process-form'

  /**
   * 根据表单Key获取表单信息
   */
  static async getFormByKey(formKey: string): Promise<ApiResponse<ProcessForm>> {
    return httpClient.get<ProcessForm>(`${this.BASE_PATH}/${formKey}`)
  }

  /**
   * 根据表单Key列表批量获取表单信息
   */
  static async getFormsByKeys(formKeys: string[]): Promise<ApiResponse<ProcessForm[]>> {
    return httpClient.post<ProcessForm[]>(`${this.BASE_PATH}/batch`, formKeys)
  }

  /**
   * 获取表单结构模板
   */
  static async getFormStruct(formKey: string): Promise<ApiResponse<string>> {
    return httpClient.get<string>(`${this.BASE_PATH}/${formKey}/struct`)
  }

  /**
   * 获取所有启用的表单模板
   */
  static async getAllEnabled(): Promise<ApiResponse<ProcessForm[]>> {
    return httpClient.get<ProcessForm[]>(`${this.BASE_PATH}/enabled`)
  }

  /**
   * 检查表单模板是否存在
   */
  static async checkFormExists(formKey: string): Promise<ApiResponse<boolean>> {
    return httpClient.get<boolean>(`${this.BASE_PATH}/${formKey}/exists`)
  }
}

// 导出默认实例
export const processFormApi = ProcessFormApi
