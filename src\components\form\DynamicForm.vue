<template>
  <div class="dynamic-form">
    <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="top"
      require-mark-placement="right-hanging">
      <div v-for="field in fields" :key="field.key" class="form-field mb-4">
        <!-- 文件上传字段 -->
        <n-form-item v-if="field.type === 'upload'" :label="field.label" :path="field.key" :rule="getFieldRule(field)">
          <div class="upload-grid-container">
            <!-- 已上传文件网格 -->
            <div class="upload-grid">
              <!-- 已上传的文件项 -->
              <div v-for="(file, index) in formData[field.key] || []" :key="getFileKey(file, index)"
                class="upload-item file-item" :class="{
                  'upload-success': file.status === 'finished',
                  'upload-error': file.status === 'error',
                  'upload-uploading': file.status === 'uploading'
                }">
                <!-- 文件图标 -->
                <div class="file-icon">
                  <n-icon :size="32" :color="getFileIconColor(file)">
                    <component :is="getFileIcon(file)" />
                  </n-icon>
                  <!-- 临时文件标记图标 -->
                  <div v-if="file.isTemporary" class="temp-icon">
                    <n-icon :size="12" color="#faad14">
                      <ClockCircleOutlined />
                    </n-icon>
                  </div>
                </div>

                <!-- 文件名 -->
                <div class="file-name" :title="file.name">
                  {{ file.name }}
                </div>

                <!-- 上传进度 -->
                <div v-if="file.status === 'uploading'" class="upload-progress">
                  <n-progress type="circle" :percentage="file.percentage || 0" :size="20" :show-indicator="false" />
                </div>

                <!-- 操作按钮 -->
                <div class="file-actions">
                  <!-- 预览按钮 -->
                  <n-button v-if="file.status === 'finished' && canPreview(file)" size="tiny" type="primary" ghost
                    @click="previewFile(file)">
                    <template #icon>
                      <n-icon>
                        <EyeOutlined />
                      </n-icon>
                    </template>
                  </n-button>

                  <!-- 删除按钮 -->
                  <n-button v-if="!readonly" size="tiny" type="error" ghost @click="removeFile(field.key, index)">
                    <template #icon>
                      <n-icon>
                        <DeleteOutlined />
                      </n-icon>
                    </template>
                  </n-button>
                </div>
              </div>

              <!-- 添加文件按钮 -->
              <div v-if="!readonly && canAddMore(field)" class="upload-item add-item"
                @click="triggerFileSelect(field.key)">
                <div class="add-icon">
                  <n-icon :size="32" color="#d9d9d9">
                    <PlusOutlined />
                  </n-icon>
                </div>
                <div class="add-text">添加文件</div>
              </div>
            </div>

            <!-- 隐藏的文件选择器 -->
            <div :data-field="field.key" style="display: none">
              <n-upload :multiple="field.multiple || false" :accept="field.accept"
                :max="field.max || (field.multiple ? 10 : 1)" :show-file-list="false"
                :custom-request="(options) => handleCustomUpload(options, field.key)" @change="handleUploadChange">
                <n-button>选择文件</n-button>
              </n-upload>
            </div>

            <!-- 上传提示信息 -->
            <div class="upload-tips">
              <n-text depth="3" style="font-size: 12px">
                {{ field.accept ? `支持格式：${field.accept}` : '支持所有格式' }}
                {{ field.multiple ? `，最多上传${field.max || 10}个文件` : '' }}
              </n-text>
            </div>
          </div>
        </n-form-item>

        <!-- 文本域字段 -->
        <n-form-item v-else-if="field.type === 'textarea'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-input v-model:value="formData[field.key]" type="textarea"
            :placeholder="field.placeholder || `请输入${field.label}`" :rows="field.rows || 4" :maxlength="field.maxlength"
            :disabled="readonly" show-count />
        </n-form-item>

        <!-- 输入框字段 -->
        <n-form-item v-else-if="field.type === 'input' || field.type === 'text'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-input v-model:value="formData[field.key]" :placeholder="field.placeholder || `请输入${field.label}`"
            :maxlength="field.maxlength" :disabled="readonly" clearable />
        </n-form-item>

        <!-- 选择器字段 -->
        <n-form-item v-else-if="field.type === 'select'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-select v-model:value="formData[field.key]" :options="field.options || []"
            :placeholder="field.placeholder || `请选择${field.label}`" :multiple="field.multiple || false"
            :disabled="readonly" clearable />
        </n-form-item>

        <!-- 单选框字段 -->
        <n-form-item v-else-if="field.type === 'radio'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-radio-group v-model:value="formData[field.key]" :disabled="readonly">
            <n-space>
              <n-radio v-for="option in field.options || []" :key="option.value" :value="option.value">
                {{ option.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>

        <!-- 复选框字段 -->
        <n-form-item v-else-if="field.type === 'checkbox'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-checkbox-group v-model:value="formData[field.key]" :disabled="readonly">
            <n-space item-style="display: flex;">
              <n-checkbox v-for="option in field.options || []" :key="option.value" :value="option.value">
                {{ option.label }}
              </n-checkbox>
            </n-space>
          </n-checkbox-group>
        </n-form-item>

        <!-- 日期字段 -->
        <n-form-item v-else-if="field.type === 'date'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-date-picker v-model:value="formData[field.key]" type="date"
            :placeholder="field.placeholder || `请选择${field.label}`" :disabled="readonly" clearable />
        </n-form-item>

        <!-- 日期时间字段 -->
        <n-form-item v-else-if="field.type === 'datetime'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-date-picker v-model:value="formData[field.key]" type="datetime"
            :placeholder="field.placeholder || `请选择${field.label}`" :disabled="readonly" clearable />
        </n-form-item>

        <!-- 数字输入字段 -->
        <n-form-item v-else-if="field.type === 'number'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-input-number v-model:value="formData[field.key]" :placeholder="field.placeholder || `请输入${field.label}`"
            :min="field.min" :max="field.max" :step="field.step || 1" :disabled="readonly" clearable />
        </n-form-item>

        <!-- 时间选择字段 -->
        <n-form-item v-else-if="field.type === 'time'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-time-picker v-model:value="formData[field.key]" :placeholder="field.placeholder || `请选择${field.label}`"
            :disabled="readonly" clearable />
        </n-form-item>

        <!-- 开关字段 -->
        <n-form-item v-else-if="field.type === 'switch'" :label="field.label" :path="field.key"
          :rule="getFieldRule(field)">
          <n-switch v-model:value="formData[field.key]" :disabled="readonly">
            <template #checked>{{ field.activeText || '是' }}</template>
            <template #unchecked>{{ field.inactiveText || '否' }}</template>
          </n-switch>
        </n-form-item>



        <!-- 未知字段类型 -->
        <n-form-item v-else :label="field.label">
          <n-alert type="warning" :show-icon="false">
            不支持的字段类型：{{ field.type }}
          </n-alert>
        </n-form-item>
      </div>
    </n-form>

    <!-- 操作按钮 -->
    <div v-if="!readonly" class="form-actions mt-6 flex justify-end space-x-3">
      <n-button @click="handleReset">重置</n-button>
      <n-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ getSubmitButtonText() }}
      </n-button>
      <!-- 条件渲染取消按钮 -->
      <n-button v-if="cancelField" @click="handleCancel" :disabled="submitting">
        {{ getCancelButtonText() }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
  NRadioGroup,
  NRadio,
  NCheckboxGroup,
  NCheckbox,
  NDatePicker,
  NTimePicker,
  NSwitch,
  NUpload,
  NButton,
  NIcon,
  NText,
  NSpace,
  NAlert,
  NProgress,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  PlusOutlined,
  EyeOutlined,
  DeleteOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileZipOutlined,
  ClockCircleOutlined
} from '@vicons/antd'
import { FileApi } from '@/apis/file'

// 字段类型定义
interface FormField {
  type: string
  key: string
  label: string
  required?: boolean
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  multiple?: boolean
  accept?: string
  max?: number
  min?: number
  step?: number
  rows?: number
  maxlength?: number
  // 开关字段属性
  activeText?: string
  inactiveText?: string
  // 提交按钮属性
  value?: string
}

// Props定义
const props = defineProps<{
  formStruct: string | object // 表单结构JSON字符串或对象
  initialData?: Record<string, any> // 初始数据
  readonly?: boolean // 是否只读
  submitText?: string // 提交按钮文本
  projectId?: number // 项目ID，用于文件转存
  formKey?: string // 表单Key，用于文件转存
}>()

// Emits定义
const emit = defineEmits<{
  submit: [data: Record<string, any>]
  change: [data: Record<string, any>]
}>()

const message = useMessage()
const formRef = ref<FormInst>()
const submitting = ref(false)

// 解析表单字段
const fields = computed<FormField[]>(() => {
  try {
    let struct = props.formStruct
    return (struct as any)?.fields || []
  } catch (error) {
    console.error('解析表单结构失败:', error)
    return []
  }
})

// 表单数据
const formData = ref<Record<string, any>>({})

// 初始化表单数据
const initFormData = () => {
  const data: Record<string, any> = {}

  fields.value.forEach(field => {
    // 跳过submit和cancel类型字段，不在初始数据中创建
    if (field.type === 'submit' || field.type === 'cancel') {
      return
    }

    if (field.type === 'upload') {
      data[field.key] = []
    } else if (field.type === 'checkbox') {
      data[field.key] = []
    } else if (field.type === 'switch') {
      data[field.key] = false
    } else if (field.type === 'number') {
      data[field.key] = field.min || 0
    } else {
      data[field.key] = null
    }
  })

  // 合并初始数据
  if (props.initialData) {
    Object.assign(data, props.initialData)
  }

  formData.value = data
}

// 监听字段变化，重新初始化数据
watch(fields, () => {
  initFormData()
}, { immediate: true })

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    // 处理文件字段的初始数据
    const processedData = { ...newData }

    fields.value.forEach(field => {
      if (field.type === 'upload' && processedData[field.key]) {
        // 确保文件数据包含必要的属性
        processedData[field.key] = processedData[field.key].map((fileData: any) => ({
          ...fileData,
          status: 'finished', // 已存在的文件标记为完成状态
          fieldKey: field.key,
          isTemporary: fileData.isTemporary || false, // 已存在的文件通常已转存，默认为false
          id: fileData.id || `existing_${Date.now()}_${Math.random()}` // 确保有唯一ID
        }))
      }
    })

    Object.assign(formData.value, processedData)
  }
}, { deep: true })

// 表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {}

  fields.value.forEach(field => {
    // 跳过submit和cancel类型字段的验证
    if (field.type === 'submit' || field.type === 'cancel') {
      return
    }

    if (field.required) {
      const fieldRule = getFieldRule(field)
      if (fieldRule) {
        rules[field.key] = fieldRule
      }
    }
  })

  return rules
})

// 获取字段验证规则
const getFieldRule = (field: FormField) => {
  if (!field.required) return undefined

  // 文件上传字段的特殊验证
  if (field.type === 'upload') {
    return {
      required: true,
      message: `请上传${field.label}`,
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: any) => {
        if (!value || !Array.isArray(value) || value.length === 0) {
          return new Error(`请上传${field.label}`)
        }
        // 检查是否有上传成功的文件
        const hasValidFiles = value.some((file: any) => file.status === 'finished')
        if (!hasValidFiles) {
          return new Error(`请确保${field.label}上传成功`)
        }
        return true
      }
    }
  }

  const selectTypes = ['select', 'radio', 'checkbox', 'date', 'datetime', 'time']
  const actionText = selectTypes.includes(field.type) ? '选择' : '输入'

  return {
    required: true,
    message: `请${actionText}${field.label}`,
    trigger: ['blur', 'change']
  }
}

// 自定义文件上传处理
const handleCustomUpload = async (options: any, fieldKey: string) => {
  const { file, onFinish, onError, onProgress } = options

  // 上传开始时就添加文件到formData，显示上传状态
  if (!formData.value[fieldKey]) {
    formData.value[fieldKey] = []
  }

  // 初始化文件对象
  file.id = file.id || `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  file.status = 'uploading'
  file.percentage = 0
  file.isTemporary = true

  // 立即添加到formData并显示
  formData.value[fieldKey].push(file)
  emit('change', formData.value)

  try {
    // 调用文件上传API
    const response = await FileApi.uploadToCache(
      file.file,
      (progress) => {
        file.percentage = progress
        onProgress({ percent: progress })
        // 更新进度时也触发变化事件
        emit('change', formData.value)
      }
    )

    if (response.data) {
      // 上传完成，响应式更新文件状态
      const fileList = formData.value[fieldKey]
      const fileIndex = fileList.findIndex(f => f.id === file.id)

      if (fileIndex >= 0) {
        // 创建新的文件对象，触发响应式更新
        const updatedFile = {
          ...fileList[fileIndex],
          status: 'finished',
          url: response.data.url,
          cacheFileName: response.data.fileName,
          percentage: 100
        }

        // 使用splice替换，触发响应式更新
        fileList.splice(fileIndex, 1, updatedFile)
      }

      onFinish()

      // 触发变化事件
      nextTick(() => {
        emit('change', formData.value)
      })
    } else {
      throw new Error('上传失败')
    }

  } catch (error: any) {
    console.error('文件上传失败:', error)
    file.status = 'error'
    file.percentage = 0
    onError()
    message.error(`文件上传失败: ${error.message || '未知错误'}`)

    // 上传失败也要触发变化事件，显示错误状态
    emit('change', formData.value)
  }
}

// 处理文件上传变化
const handleUploadChange = () => {
  nextTick(() => {
    emit('change', formData.value)
  })
}

// 通用的表单处理方法
const handleFormAction = async (actionType: 'submit' | 'cancel') => {
  if (!formRef.value) return

  try {
    // 只有submit操作需要验证表单
    if (actionType === 'submit') {
      await formRef.value.validate()
    }

    submitting.value = true

    // 处理特殊字段的数据格式
    const submitData = { ...formData.value }

    fields.value.forEach(field => {
      if (field.type === 'upload' && Array.isArray(submitData[field.key])) {
        // 文件上传字段：提取有效文件数据
        const uploadedFiles = submitData[field.key]
          .filter((file: any) => file.status === 'finished')
          .map((file: any) => ({
            name: file.name,
            cacheFileName: file.cacheFileName,
            originalName: file.name,
            fieldKey: field.key,
            size: file.size || 0,
            type: file.type || '',
            isTemporary: file.isTemporary || false,
            url: file.url
          }))

        submitData[field.key] = uploadedFiles
      } else if (field.type === 'time' && submitData[field.key]) {
        // 时间字段：转换为字符串格式
        submitData[field.key] = new Date(submitData[field.key]).toLocaleTimeString()
      } else if (field.type === 'date' && submitData[field.key]) {
        // 日期字段：转换为字符串格式
        submitData[field.key] = new Date(submitData[field.key]).toLocaleDateString()
      } else if (field.type === 'datetime' && submitData[field.key]) {
        // 日期时间字段：转换为字符串格式
        submitData[field.key] = new Date(submitData[field.key]).toLocaleString()
      } else if (field.type === actionType) {
        // 根据操作类型添加对应字段的value
        submitData[field.key] = field.value || actionType
      }
    })

    // 提交数据到后端
    // 后端接收到表单数据后，需要处理文件转存：
    // 1. 遍历所有upload类型字段
    // 2. 根据cacheFileName从缓存区读取文件
    // 3. 转存到项目文档区：/projects/{projectId}/forms/{formKey}/{fieldKey}/
    // 4. 更新文件的最终URL和存储路径
    // 5. 清理缓存区的临时文件

    emit('submit', submitData)
    message.success(actionType === 'submit' ? '表单提交成功' : '操作成功')
  } catch (error) {
    console.error(`${actionType}操作失败:`, error)
    message.error(actionType === 'submit' ? '请检查表单填写是否完整' : '操作失败')
  } finally {
    submitting.value = false
  }
}

// 处理表单提交
const handleSubmit = async () => {
  await handleFormAction('submit')
}

// 处理表单重置
const handleReset = () => {
  formRef.value?.restoreValidation()
  initFormData()
  emit('change', formData.value)
}

// 获取submit字段配置
const submitField = computed(() => {
  return fields.value.find(field => field.type === 'submit')
})

// 获取cancel字段配置
const cancelField = computed(() => {
  return fields.value.find(field => field.type === 'cancel')
})

// 处理取消操作
const handleCancel = async () => {
  if (!cancelField.value) return
  await handleFormAction('cancel')
}

// 获取提交按钮文本
const getSubmitButtonText = () => {
  if (submitField.value && submitField.value.value) {
    return submitField.value.value === 'submit' ? '提交' : submitField.value.value
  }
  return props.submitText || '提交'
}

// 获取取消按钮文本
const getCancelButtonText = () => {
  if (cancelField.value && cancelField.value.value) {
    return cancelField.value.value === 'cancel' ? '取消' : cancelField.value.value
  }
  return '取消'
}

// 获取文件图标
const getFileIcon = (file: any) => {
  const fileName = file.name || ''
  const extension = fileName.split('.').pop()?.toLowerCase()

  switch (extension) {
    case 'pdf':
      return FilePdfOutlined
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
      return FileImageOutlined
    case 'doc':
    case 'docx':
      return FileWordOutlined
    case 'xls':
    case 'xlsx':
      return FileExcelOutlined
    case 'ppt':
    case 'pptx':
      return FilePptOutlined
    case 'zip':
    case 'rar':
    case '7z':
      return FileZipOutlined
    default:
      return FileOutlined
  }
}

// 获取文件图标颜色
const getFileIconColor = (file: any) => {
  if (file.status === 'error') return '#ff4d4f'
  if (file.status === 'uploading') return '#1890ff'
  if (file.status === 'finished') return '#52c41a'
  return '#d9d9d9'
}

// 获取文件的唯一key
const getFileKey = (file: any, index: number) => {
  return file.id || `file_${index}`
}

// 判断文件是否可预览
const canPreview = (file: any) => {
  const fileName = file.name || ''
  const extension = fileName.split('.').pop()?.toLowerCase()
  const previewableTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'pdf']
  return previewableTypes.includes(extension || '')
}

// 预览文件
const previewFile = (file: any) => {
  if (file.url) {
    window.open(file.url, '_blank')
  }
}

// 删除文件
const removeFile = (fieldKey: string, index: number) => {
  const fileList = formData.value[fieldKey] || []
  if (index >= 0 && index < fileList.length) {
    fileList.splice(index, 1)
    nextTick(() => {
      emit('change', formData.value)
    })
  }
}

// 判断是否可以添加更多文件
const canAddMore = (field: FormField) => {
  const currentFiles = formData.value[field.key] || []
  const maxFiles = field.max || (field.multiple ? 10 : 1)
  return currentFiles.length < maxFiles
}

// 触发文件选择
const triggerFileSelect = (fieldKey: string) => {
  // 通过隐藏的upload组件触发文件选择
  const uploadComponent = document.querySelector(`[data-field="${fieldKey}"] input[type="file"]`) as HTMLInputElement
  if (uploadComponent) {
    uploadComponent.click()
  }
}



// 监听表单数据变化
watch(formData, (newData) => {
  emit('change', newData)
}, { deep: true })
</script>

<style scoped>
.dynamic-form {
  width: 100%;
}

.form-field {
  margin-bottom: 1rem;
}

.form-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* 文件上传网格样式 */
.upload-grid-container {
  width: 100%;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 8px;
}

.upload-item {
  position: relative;
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-item:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

/* 文件项样式 */
.file-item {
  border-style: solid;
  padding: 8px;
}

.file-item.upload-success {
  border-color: #52c41a;
  background: #f6ffed;
}

.file-item.upload-error {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.file-item.upload-uploading {
  border-color: #1890ff;
  background: #e6f7ff;
}

.file-icon {
  margin-bottom: 8px;
  position: relative;
}

.temp-icon {
  position: absolute;
  top: -2px;
  right: -2px;
  background: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.file-name {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 8px;
  position: relative;
}



.upload-progress {
  position: absolute;
  top: 8px;
  right: 8px;
}

.file-actions {
  display: flex;
  gap: 4px;
  position: absolute;
  bottom: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-item:hover .file-actions {
  opacity: 1;
}

/* 添加按钮样式 */
.add-item {
  cursor: pointer;
  border-style: dashed;
}

.add-item:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.add-icon {
  margin-bottom: 8px;
}

.add-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.upload-tips {
  margin-top: 8px;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }

  .upload-item {
    width: 100px;
    height: 100px;
  }

  .file-name {
    font-size: 11px;
  }
}
</style>
