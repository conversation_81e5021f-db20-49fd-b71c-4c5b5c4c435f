import { ref } from 'vue'
import { SystemDictApi } from '@/apis/systemDict'
import { TaskCategoryApi } from '@/apis/taskCategory'
import { ProjectCategoryApi } from '@/apis/projectCategory'
import { UserApi } from '@/apis/user'

export interface OptionItem {
  label: string
  value: string
}

/**
 * 系统选项composable
 * 统一管理各种下拉选项的获取和缓存
 */
export function useSystemOptions() {
  
  // 各种选项的状态
  const siteOptions = ref<OptionItem[]>([])
  const regionOptions = ref<OptionItem[]>([])
  const businessDepartmentOptions = ref<OptionItem[]>([])
  const assetStateOptions = ref<OptionItem[]>([])
  const usageStatusOptions = ref<OptionItem[]>([])
  const pmOwnerOptions = ref<OptionItem[]>([])
  const projectTypeOptions = ref<OptionItem[]>([])
  const projectStatusOptions = ref<OptionItem[]>([])
  const priorityOptions = ref<OptionItem[]>([])
  const taskStatusOptions = ref<OptionItem[]>([])
  const taskIssueTypeOptions = ref<OptionItem[]>([])
  
  // 加载状态
  const loading = ref<Record<string, boolean>>({})
  const loaded = ref<Record<string, boolean>>({})

  // 通用的选项获取函数
  const fetchOptions = async (
    type: string, 
    apiCall: () => Promise<any>, 
    targetRef: any
  ) => {
    if (loading.value[type]) return targetRef.value
    
    try {
      loading.value[type] = true
      const response = await apiCall()
      targetRef.value = response.data
      loaded.value[type] = true
      return targetRef.value
    } catch (error) {
      console.error(`获取${type}选项失败:`, error)
      // 抛出错误，让组件层面处理用户提示
      throw error
    } finally {
      loading.value[type] = false
    }
  }

  // 按需加载各种选项
  const loadSiteOptions = () => fetchOptions('sites', () => SystemDictApi.getSiteOptions(), siteOptions)
  const loadRegionOptions = () => fetchOptions('regions', () => SystemDictApi.getRegionOptions(), regionOptions)
  const loadBusinessDepartmentOptions = () => fetchOptions('businessDepartments', () => SystemDictApi.getBusinessDepartmentOptions(), businessDepartmentOptions)
  const loadAssetStateOptions = () => fetchOptions('assetStates', () => SystemDictApi.getAssetStateOptions(), assetStateOptions)
  const loadUsageStatusOptions = () => fetchOptions('usageStatuses', () => SystemDictApi.getUsageStatusOptions(), usageStatusOptions)
  const loadPmOwnerOptions = () => fetchOptions('pmOwners', () => UserApi.getUserOptions(), pmOwnerOptions)
  const loadProjectCategoryOptions = () => fetchOptions('projectCategories', () => ProjectCategoryApi.getProjectCategoryOptions(), projectTypeOptions)
  const loadProjectStatusOptions = () => {
    // 项目状态选项暂时使用常量，因为后端没有专门的接口
    const statusOptions = [
      { label: '需求收集', value: 'request_collect' },
      { label: '规划中', value: 'planning' },
      { label: '开发中', value: 'development' },
      { label: '内测', value: 'inner_test' },
      { label: 'UAT', value: 'uat' },
      { label: 'CAB', value: 'cab' },
      { label: '上线', value: 'go_live' }
    ]
    projectStatusOptions.value = statusOptions
    loaded.value.projectStatuses = true
    return Promise.resolve(statusOptions)
  }
  const loadPriorityOptions = () => fetchOptions('priorities', () => SystemDictApi.getPriorityOptions(), priorityOptions)
  const loadTaskStatusOptions = () => {
    // 任务状态选项暂时使用常量，因为后端没有专门的接口
    const statusOptions = [
      { label: '待办', value: 'open' },
      { label: '进行中', value: 'inprogress' },
      { label: '待验证', value: 'toverify' },
      { label: '已完成', value: 'close' }
    ]
    taskStatusOptions.value = statusOptions
    loaded.value.taskStatuses = true
    return Promise.resolve(statusOptions)
  }
  const loadTaskIssueTypeOptions = () => fetchOptions('taskIssueTypes', () => TaskCategoryApi.getTaskCategoryOptions(), taskIssueTypeOptions)



  // 重新加载所有选项
  const reloadAllOptions = async () => {
    Object.keys(loaded.value).forEach(key => {
      loaded.value[key] = false
    })
    
    // 可以选择性地重新加载已经使用过的选项
    const promises = []
    if (loaded.value.sites) promises.push(loadSiteOptions())
    if (loaded.value.regions) promises.push(loadRegionOptions())
    if (loaded.value.businessDepartments) promises.push(loadBusinessDepartmentOptions())
    if (loaded.value.assetStates) promises.push(loadAssetStateOptions())
    if (loaded.value.usageStatuses) promises.push(loadUsageStatusOptions())
    if (loaded.value.pmOwners) promises.push(loadPmOwnerOptions())
    if (loaded.value.projectCategories) promises.push(loadProjectCategoryOptions())
    if (loaded.value.projectStatuses) promises.push(loadProjectStatusOptions())
    if (loaded.value.priorities) promises.push(loadPriorityOptions())
    if (loaded.value.taskStatuses) promises.push(loadTaskStatusOptions())
    if (loaded.value.taskIssueTypes) promises.push(loadTaskIssueTypeOptions())
    
    await Promise.all(promises)
  }

  return {
    // 选项数据
    siteOptions,
    regionOptions,
    businessDepartmentOptions,
    assetStateOptions,
    usageStatusOptions,
    pmOwnerOptions,
    projectTypeOptions,
    projectStatusOptions,
    priorityOptions,
    taskStatusOptions,
    taskIssueTypeOptions,
    
    // 状态
    loading,
    loaded,
    
    // 方法
    reloadAllOptions,
    
    // 具体的加载方法
    loadSiteOptions,
    loadRegionOptions,
    loadBusinessDepartmentOptions,
    loadAssetStateOptions,
    loadUsageStatusOptions,
    loadPmOwnerOptions,
    loadProjectCategoryOptions,
    loadProjectStatusOptions,
    loadPriorityOptions,
    loadTaskStatusOptions,
    loadTaskIssueTypeOptions
  }
}
