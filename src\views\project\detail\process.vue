<template>
  <div class="process-page flex bg-gray-50 dark:bg-gray-900 p-4">
    <!-- 左侧流程菜单 -->
    <div class="process-sidebar w-80 bg-white dark:bg-gray-800 border-r border-gray-100 dark:border-gray-700 flex flex-col">
      <!-- 主要步骤 -->
      <div class="border-b border-gray-100 dark:border-gray-700 flex flex-col main-steps-container">
        <div class="p-4 flex-shrink-0">
          <div class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400">
            <n-icon :component="DeploymentUnitOutlined" class="mr-2 text-blue-500" />
            <span>主要步骤</span>
          </div>
        </div>
        <div class="flex-1 overflow-hidden">
          <n-scrollbar style="height: 100%;">
            <div class="px-3 pb-4">
              <div v-for="item in mainProcessOptions" :key="item.stageKey" @click="handleMenuSelect(item.stageKey)" :class="[
                'relative mb-2 p-3 rounded-lg cursor-pointer transition-all',
                activeKey === item.stageKey
                  ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800/50'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-transparent'
              ]">
                <ProcessItem :item="item" :active="activeKey === item.stageKey" />
              </div>
            </div>
          </n-scrollbar>
        </div>
      </div>

      <!-- 其他步骤 -->
      <div class="flex flex-col secondary-steps-container">
        <div class="p-4 border-b border-gray-100 dark:border-gray-700 flex-shrink-0">
          <div class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400">
            <n-icon :component="UnorderedListOutlined" class="mr-2 text-blue-500" />
            <span>其他步骤</span>
          </div>
        </div>
        <div class="flex-1 overflow-hidden">
          <n-scrollbar style="height: 100%;">
            <div class="p-3">
              <div v-for="item in secondaryProcessOptions" :key="item.stageKey" @click="handleMenuSelect(item.stageKey)"
                :class="[
                  'relative mb-2 p-3 rounded-lg cursor-pointer transition-all',
                  activeKey === item.stageKey
                    ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800/50'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-transparent'
                ]">
                <ProcessItem :item="item" :active="activeKey === item.stageKey" />
              </div>
            </div>
          </n-scrollbar>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 bg-gray-50 dark:bg-gray-900 pl-4 pr-4 process-content">
      <transition name="fade" mode="out-in">
        <div v-if="activeStage" class="h-full">
          <!-- 显示表单加载错误 -->
          <div v-if="formLoadError" class="h-full flex flex-col items-center justify-center">
            <n-empty :description="formLoadError" class="py-16">
              <template #icon>
                <n-icon :size="80" class="text-gray-300">
                  <FormOutlined />
                </n-icon>
              </template>
              <template #extra>
                <div class="text-gray-500 text-sm mt-4">
                  请联系管理员配置此阶段的表单流程
                </div>
              </template>
            </n-empty>
          </div>
          <!-- 正常显示时间线 -->
          <div v-else class="timeline-container flex flex-col h-full">
            <!-- 固定的头部和时间线 -->
            <div class="timeline-header flex-shrink-0">
              <n-card :bordered="false" class="shadow-sm">
                <template #header>
                  <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium">{{ activeStage?.stageName || '流程管理' }}</h3>
                    <n-tag :type="getStageStatusType(activeStage?.status)" size="small" round>
                      {{ getStageStatusText(activeStage?.status) }}
                    </n-tag>
                  </div>
                </template>

                <!-- 简化的时间线显示 - 固定在顶部，垂直居中 -->
                <div v-if="enrichedActiveStage?.nodes && enrichedActiveStage.nodes.length > 0" class="timeline-simple-fixed">
                  <div class="flex items-center space-x-2 mb-3">
                    <div v-for="(node, index) in enrichedActiveStage.nodes" :key="node.key || index"
                      class="flex items-center">
                      <div class="flex flex-col items-center cursor-pointer" @click="handleNodeClick(node, index)">
                        <div
                          class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs transition-all hover:scale-110"
                          :class="[
                            getNodeStatusClass(node.status),
                            currentNodeIndex === index ? 'ring-2 ring-blue-300' : ''
                          ]">
                          {{ index + 1 }}
                        </div>
                        <div class="text-xs mt-0.5 text-center max-w-120">{{ node.title }}</div>
                      </div>
                      <div v-if="index < enrichedActiveStage.nodes.length - 1" class="w-6 h-0.5 bg-gray-300 mx-1"></div>
                    </div>
                  </div>
                </div>
              </n-card>
            </div>

            <!-- 可滚动的表单内容区域 -->
            <div class="form-content-scrollable flex-1 overflow-y-auto">
              <n-card :bordered="false" class="shadow-sm mt-4">
                <div class="form-content">
                  <div v-if="formType === 0">
                    <div v-if="currentFormStruct" class="current-form">
                      <DynamicForm :form-struct="currentFormStruct" :initial-data="currentFormData"
                        :readonly="currentFormReadonly" :submit-text="getSubmitButtonText()" @submit="handleFormSubmit" />
                    </div>
                    <div v-else-if="currentFormTitle" class="form-not-configured">
                      <n-empty description="此表单未配置" class="py-16">
                        <template #icon>
                          <n-icon :size="60" class="text-gray-300">
                            <FormOutlined />
                          </n-icon>
                        </template>
                        <template #extra>
                          <div class="text-gray-500 text-sm mt-4">
                            请联系管理员配置此表单的结构
                          </div>
                        </template>
                      </n-empty>
                    </div>
                    <n-empty v-else description="请选择一个流程步骤查看表单" class="py-16">
                      <template #icon>
                        <n-icon :size="60" class="text-gray-300">
                          <FormOutlined />
                        </n-icon>
                      </template>
                    </n-empty>
                  </div>
                  <div v-else>
                    <!-- 自定义表单组件渲染 -->
                    <div v-if="currentFormStruct && currentFormStruct.component === 'PlanningSubmitForm'" class="custom-form">
                      <h4 class="text-base font-medium mb-4">{{ currentFormTitle }}</h4>
                      <PlanningSubmitForm
                        :initial-data="currentFormData"
                        :readonly="currentFormReadonly"
                        :submit-text="getSubmitButtonText()"
                        :project-id="projectInfo?.id"
                        @submit="handleFormSubmit" />
                    </div>
                    <!-- CAB提交表单组件 -->
                    <div v-else-if="currentFormStruct && currentFormStruct.component === 'CABSubmitForm'" class="custom-form">
                      <h4 class="text-base font-medium mb-4">{{ currentFormTitle }}</h4>
                      <CABSubmitForm
                        :initial-data="currentFormData"
                        :readonly="currentFormReadonly"
                        :submit-text="getSubmitButtonText()"
                        :project-id="projectInfo?.id"
                        @submit="handleFormSubmit" />
                    </div>
                    <!-- 其他自定义组件可以在这里添加 -->
                    <div v-else-if="currentFormStruct" class="unsupported-form">
                      <h4 class="text-base font-medium mb-4">{{ currentFormTitle }}</h4>
                      <n-alert type="warning" :show-icon="false">
                        不支持的表单组件类型：{{ currentFormStruct.component }}
                      </n-alert>
                    </div>
                  </div>
                </div>
              </n-card>
            </div>
          </div>
        </div>
        <n-empty v-else description="请从左侧选择一个流程阶段" class="h-full flex flex-col items-center justify-center">
          <template #icon>
            <n-icon :size="60" class="text-gray-300">
              <SelectOutlined />
            </n-icon>
          </template>
        </n-empty>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  NIcon,
  NTag,
  NScrollbar,
  NEmpty,
  NAlert,
  NCard,
  NDivider
} from 'naive-ui'
import {
  DeploymentUnitOutlined,
  UnorderedListOutlined,
  ClockCircleOutlined,
  SelectOutlined,
  FormOutlined
} from '@vicons/antd'

// 导入动态表单组件
import DynamicForm from '@/components/form/DynamicForm.vue'
import PlanningSubmitForm from '@/components/form/PlanningSubmitForm.vue'
import CABSubmitForm from '@/components/form/CABSubmitForm.vue'
// 导入类型定义
import type { ProjectProcess, ProcessForm } from '@/types/project_process'
import { ProjectProcessApi } from '@/apis/project_process'
import { ProcessFormApi } from '@/apis/process_form'

const props = defineProps<{
  projectInfo: {
    id: number
    name: string
    type?: string
    [key: string]: any
  }
}>()

const message = useMessage()

// 计算进度的函数
const calculateProgress = (item: ProjectProcess): number => {
  switch (item.status) {
    case 'pending':
      return 0
    case 'completed':
      return 100
    case 'active':
      // 基于当前日期在计划日期段的位置比例计算
      if (item.plannedStartTime && item.plannedEndTime) {
        const now = new Date().getTime()
        const startTime = new Date(item.plannedStartTime).getTime()
        const endTime = new Date(item.plannedEndTime).getTime()

        if (now <= startTime) {
          return 0
        } else if (now >= endTime) {
          return 99 // 等于结束日期是99%
        } else {
          // 在中间位置按比例计算
          const totalDuration = endTime - startTime
          const elapsedTime = now - startTime
          return Math.round((elapsedTime / totalDuration) * 99)
        }
      }
      return 50 // 如果没有计划时间，默认50%
    case 'skipped':
      return 0
    default:
      return 0
  }
}

// 导入流程项组件
const ProcessItem = defineComponent({
  name: 'ProcessItem',
  props: {
    item: {
      type: Object as PropType<ProjectProcess>,
      required: true
    },
    active: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const statusColors: Record<string, 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error'> = {
      pending: 'default',
      active: 'info',
      completed: 'success',
      skipped: 'warning'
    }

    const statusTextMap = {
      pending: '待开始',
      active: '进行中',
      completed: '已完成',
      skipped: '已跳过'
    }

    const formatDate = (dateStr: string, format: string) => {
      if (!dateStr) return '--'
      const date = new Date(dateStr)
      const map: Record<string, any> = {
        'M+': date.getMonth() + 1,
        'D+': date.getDate(),
        'H+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
      }

      if (/(Y+)/.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').toString().substr(4 - RegExp.$1.length))
      }

      for (const k in map) {
        if (new RegExp(`(${k})`).test(format)) {
          format = format.replace(RegExp.$1,
            RegExp.$1.length === 1 ? map[k] : ('00' + map[k]).substr(('' + map[k]).length))
        }
      }

      return format
    }

    // 计算当前进度
    const currentProgress = computed(() => calculateProgress(props.item))

    // 格式化时间显示
    const formatTimeRange = () => {
      const plannedStart = props.item.plannedStartTime ? formatDate(props.item.plannedStartTime, 'MM-DD') : '--'
      const plannedEnd = props.item.plannedEndTime ? formatDate(props.item.plannedEndTime, 'MM-DD') : '--'
      const actualStart = props.item.actualStartTime ? formatDate(props.item.actualStartTime, 'MM-DD') : '--'
      const actualEnd = props.item.actualEndTime ? formatDate(props.item.actualEndTime, 'MM-DD') : '--'

      return {
        planned: `计划: ${plannedStart} ~ ${plannedEnd}`,
        actual: `实际: ${actualStart} ~ ${actualEnd}`
      }
    }

    return () => {
      const timeRange = formatTimeRange()

      return h('div', {
        class: 'process-item'
      }, [
        // 顶部行：标题和状态
        h('div', { class: 'flex items-center justify-between mb-1' }, [
          h('div', { class: 'flex items-center' }, [
            h('span', { class: 'font-medium text-gray-900 dark:text-gray-100' }, props.item.stageName)
          ]),
          h(NTag, {
            size: 'small',
            type: statusColors[props.item.status] || 'default',
            round: true,
            class: 'ml-2'
          }, { default: () => statusTextMap[props.item.status] || '未知' })
        ]),

        // 描述
        props.item.description && h('div', {
          class: 'text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-1'
        }, props.item.description),

        // 时间进度 - 显示两个日期段
        h('div', { class: 'text-xs text-gray-500 dark:text-gray-400 mb-2 space-y-1' }, [
          h('div', { class: 'flex items-center' }, [
            h(NIcon, { component: ClockCircleOutlined, class: 'mr-1', size: 12 }),
            timeRange.planned
          ]),
          h('div', { class: 'flex items-center' }, [
            h(NIcon, { component: ClockCircleOutlined, class: 'mr-1', size: 12 }),
            timeRange.actual
          ])
        ]),

        // 进度条
        h('div', { class: 'mb-1' }, [
          h('div', { class: 'flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1' }, [
            h('span', {}, '进度'),
            h('span', {}, `${currentProgress.value}%`)
          ]),
          h('div', {
            class: 'h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden',
          }, [
            h('div', {
              class: [
                'h-full rounded-full transition-all duration-500',
                {
                  'bg-blue-500': props.item.status === 'active',
                  'bg-green-500': props.item.status === 'completed',
                  'bg-orange-500': props.item.status === 'skipped',
                  'bg-gray-300': props.item.status === 'pending',
                }
              ],
              style: { width: `${currentProgress.value}%` }
            })
          ])
        ])
      ])
    }
  }
})





// 当前选中的菜单项
const activeKey = ref<string>('')
// 当前激活的阶段
const activeStage = ref<any | null>(null)
// 加载状态
const loading = ref(false)
// 项目流程数据
const projectProcesses = ref<any[]>([])
// 当前流程的表单列表
const currentStageForms = ref<ProcessForm[]>([])
// 表单加载状态
const formLoadError = ref<string | null>(null)
// 当前选中的节点索引
const currentNodeIndex = ref(0)
// 表单类型
const formType = ref(0)
// 当前表单相关数据
const currentFormStruct = ref<any>(null)
const currentFormData = ref<Record<string, any>>({})
const currentFormTitle = ref<string>('')
const currentFormReadonly = ref<boolean>(false)
const currentFormEditable = ref<boolean>(false) // 是否可编辑（已完成但允许编辑）

// 获取项目流程信息
const fetchProjectProcess = async () => {
  try {
    loading.value = true
    const response = await ProjectProcessApi.getStagesByProjectId(props.projectInfo.id)
    if (response.data && Array.isArray(response.data)) {
      // 保存项目流程数据
      projectProcesses.value = response.data

      // 查找当前活跃阶段（状态为active的第一个阶段）
      if (!activeKey.value) {
        const activeStageData = projectProcesses.value.find(s => s.status === 'active')
        if (activeStageData) {
          activeKey.value = activeStageData.stageKey
          activeStage.value = { ...activeStageData }
        } else {
          // 如果没有active状态的阶段，选择第一个阶段
          const firstStage = projectProcesses.value[0]
          if (firstStage) {
            activeKey.value = firstStage.stageKey
            activeStage.value = { ...firstStage }
          }
        }
      }
    } else {
      projectProcesses.value = []
    }
  } catch (error: any) {
    console.error('获取项目流程信息失败:', error)
    message.error(error.message || '获取项目流程信息失败')
  } finally {
    loading.value = false
  }
}

// 主要流程阶段
const mainProcessOptions = computed(() =>
  projectProcesses.value.filter(stage => stage.isMainProcess)
)

// 次要流程阶段
const secondaryProcessOptions = computed(() =>
  projectProcesses.value.filter(stage => !stage.isMainProcess)
)

// 所有阶段
const allStages = computed(() => projectProcesses.value)

// 增强的当前阶段数据（转换为ApprovalTimeline组件需要的格式）
const enrichedActiveStage = computed(() => {
  if (!activeStage.value) return null

  const stage = {
    ...activeStage.value,
    // 确保必要的字段存在
    label: activeStage.value.stageName || activeStage.value.stageKey || '未知阶段',
    status: activeStage.value.status || 'pending',
    nodes: []
  }

  // 将stageProcess转换为nodes格式
  if (stage.stageProcess) {
    try {
      const stageData = stage.stageProcess.forms
      if (Array.isArray(stageData) && stageData.length > 0) {
        // 按order排序并转换为nodes格式
        const sortedForms = stageData.sort((a, b) => (a.order || 0) - (b.order || 0))
        stage.nodes = sortedForms.map((formConfig, index) => ({
          key: formConfig.formKey || `node_${index}`,
          title: formConfig.formName || formConfig.title || `步骤 ${index + 1}`,
          type: formConfig.type || 'form',
          status: formConfig.status || 'pending',
          completedAt: formConfig.completedAt || null,
          order: formConfig.order || index,
          data: formConfig.data || {}
        }))
      }
    } catch (error) {
      console.error('解析stageProcess失败:', error)
      stage.nodes = []
    }
  }

  return stage
})

// 获取阶段状态类型
const getStageStatusType = (status: string) => {
  const statusMap: Record<string, 'default' | 'error' | 'primary' | 'info' | 'success' | 'warning'> = {
    'pending': 'default',
    'active': 'info',
    'completed': 'success',
    'skipped': 'warning'
  }
  return statusMap[status] || 'default'
}

// 获取阶段状态文本
const getStageStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待开始',
    'active': '进行中',
    'completed': '已完成',
    'skipped': '已跳过'
  }
  return statusMap[status] || '未知'
}

// 获取节点状态样式类
const getNodeStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'bg-gray-400',
    'current': 'bg-blue-500',
    'completed': 'bg-green-500',
    'skipped': 'bg-yellow-500'
  }
  return statusMap[status] || 'bg-gray-400'
}

// 获取提交按钮文本
const getSubmitButtonText = () => {
  if (currentFormReadonly.value) {
    return '查看'
  } else if (currentFormEditable.value) {
    return '更新'
  } else {
    return '提交'
  }
}

// 处理节点点击
const handleNodeClick = async (node: any, index: number) => {
  currentNodeIndex.value = index

  // 获取对应的表单模板
  if (node.key) {
    try {
      // 获取表单模板结构
      const formResponse = await ProcessFormApi.getFormByKey(node.key)
      if (formResponse.data) {
        formType.value = formResponse.data.formType
        if (formResponse.data.formStruct) {
          // 解析表单结构，支持字符串和对象格式
          try {
            currentFormStruct.value = typeof formResponse.data.formStruct === 'string'
              ? JSON.parse(formResponse.data.formStruct)
              : formResponse.data.formStruct
          } catch (error) {
            console.error('解析表单结构失败:', error)
            currentFormStruct.value = formResponse.data.formStruct
          }
        }
        currentFormTitle.value = formResponse.data.formName || node.title || '表单'

        // 获取表单数据（如果存在）
        try {
          const dataResponse = await ProjectProcessApi.getStageFormData(
            props.projectInfo.id,
            activeStage.value?.stageKey || '',
            node.key
          )
          currentFormData.value = dataResponse.data || {}
        } catch (dataError) {
          console.log('表单数据不存在，使用空数据:', node.key)
          currentFormData.value = {}
        }

        // 设置表单状态
        const isCompleted = node.status === 'completed'
        currentFormReadonly.value = false // 默认可编辑
        currentFormEditable.value = isCompleted // 已完成的表单标记为可编辑状态
      } else {
        // 表单结构为空，显示"此表单未配置"
        currentFormStruct.value = null
        currentFormData.value = {}
        currentFormTitle.value = node.title || '表单'
        currentFormReadonly.value = false
      }
    } catch (error: any) {
      console.error('获取表单失败:', error)
      // 不显示错误提示，而是显示"此表单未配置"
      currentFormStruct.value = null
      currentFormData.value = {}
      currentFormTitle.value = node.title || '表单'
      currentFormReadonly.value = false
    }
  } else {
    // 没有formKey，显示"此表单未配置"
    currentFormStruct.value = null
    currentFormData.value = {}
    currentFormTitle.value = node.title || '表单'
    currentFormReadonly.value = false
  }
}

// 处理表单提交
const handleFormSubmit = async (formData: Record<string, any>) => {
  if (!activeStage.value || currentNodeIndex.value < 0) {
    message.error('请先选择一个流程步骤')
    return
  }

  const currentNode = enrichedActiveStage.value?.nodes?.[currentNodeIndex.value]
  if (!currentNode) {
    message.error('当前节点不存在')
    return
  }

  try {
    if (currentFormEditable.value) {
      // 编辑模式：更新已完成的表单数据
      await ProjectProcessApi.updateStageFormData(
        props.projectInfo.id,
        activeStage.value.stageKey,
        currentNode.key,
        formData
      )
      message.success('表单更新成功')
    } else {
      // 新增模式：提交新的表单数据
      await ProjectProcessApi.submitStageForm(
        props.projectInfo.id,
        activeStage.value.stageKey,
        currentNode.key,
        formData
      )
      message.success('表单提交成功')
    }

    // 重新加载数据
    await fetchProjectProcess()
    if (activeStage.value) {
      await loadStageForms(activeStage.value)
    }

    // 重新选择当前节点以刷新状态
    if (enrichedActiveStage.value?.nodes && enrichedActiveStage.value.nodes[currentNodeIndex.value]) {
      await handleNodeClick(enrichedActiveStage.value.nodes[currentNodeIndex.value], currentNodeIndex.value)
    }
  } catch (error: any) {
    console.error('表单操作失败:', error)
    message.error('表单操作失败: ' + (error.message || '未知错误'))
  }
}


// 处理节点更新
const handleNodeUpdate = async (updateData: any) => {
  console.log('Node updated:', updateData)

  try {
    const { stageKey, nodeKey, status, formData } = updateData

    // 如果有表单数据，提交或更新表单数据到ProjectProcess
    if (formData && nodeKey && stageKey) {
      if (status === 'completed') {
        // 提交表单数据
        await ProjectProcessApi.submitStageForm(
          props.projectInfo.id,
          stageKey,
          nodeKey,
          formData
        )
      } else {
        // 更新表单数据
        await ProjectProcessApi.updateStageFormData(
          props.projectInfo.id,
          stageKey,
          nodeKey,
          formData
        )
      }

      message.success('流程状态更新成功')

      // 重新获取流程数据和表单数据
      await fetchProjectProcess()
      if (activeStage.value) {
        await loadStageForms(activeStage.value)
      }
    }
  } catch (error: any) {
    console.error('更新流程状态失败:', error)
    message.error(error.message || '更新流程状态失败')
  }
}

// 处理菜单选择
const handleMenuSelect = async (key: string) => {
  activeKey.value = key
  const stage = allStages.value.find(s => s.stageKey === key)
  if (stage) {
    activeStage.value = { ...stage }
    // 加载当前流程的表单
    await loadStageForms(stage)
  }
}

// 加载流程阶段的表单模板和数据
const loadStageForms = async (stage: any) => {
  try {
    currentStageForms.value = []
    formLoadError.value = null

    if (stage.stageProcess) {
      // 解析阶段流程配置，获取表单Key列表（按排序）
      const stageData = stage.stageProcess.forms

      if (Array.isArray(stageData) && stageData.length > 0) {
        // 检查是否有formKey配置
        const hasFormKey = stageData.some(item => item.formKey)

        if (!hasFormKey) {
          // 情况1：阶段有stageProcess但没有formKey配置
          formLoadError.value = '请联系管理员配置此阶段的表单流程'
          return
        }

        // 情况2：有formKey配置，正常处理
        const formConfigs: any[] = []
        let hasValidForm = false

        // 按order排序
        stageData.sort((a, b) => (a.order || 0) - (b.order || 0))

        for (const formConfig of stageData) {
          if (formConfig.formKey) {
            try {
              // 获取表单模板结构
              const formResponse = await ProcessFormApi.getFormByKey(formConfig.formKey)
              if (formResponse.data && formResponse.data.formStruct) {
                const formTemplate = formResponse.data

                // 获取表单数据（如果存在）
                let formData = {}
                try {
                  const dataResponse = await ProjectProcessApi.getStageFormData(
                    props.projectInfo.id,
                    stage.stageKey,
                    formConfig.formKey
                  )
                  if (dataResponse.data) {
                    formData = dataResponse.data
                  }
                } catch (dataError) {
                  console.log('表单数据不存在，使用空数据:', formConfig.formKey)
                }

                // 合并表单模板和数据
                const enrichedForm = {
                  ...formTemplate,
                  formData: formData,
                  status: formConfig.status || 'pending',
                  order: formConfig.order || 0
                }

                formConfigs.push(enrichedForm)
                hasValidForm = true
              } else {
                console.warn('表单结构为空:', formConfig.formKey)
              }
            } catch (formError: any) {
              console.error('获取表单模板失败:', formConfig.formKey, formError)
            }
          }
        }

        currentStageForms.value = formConfigs

        // 如果有formKey但获取不到有效表单，在表单区域显示"此表单未配置"
        // 这里不设置formLoadError，让时间线正常显示

        // 自动选择第一个节点
        if (enrichedActiveStage.value?.nodes && enrichedActiveStage.value.nodes.length > 0) {
          await handleNodeClick(enrichedActiveStage.value.nodes[0], 0)
        }
      } else {
        // 情况1：stageProcess为空数组或无效
        formLoadError.value = '请联系管理员配置此阶段的表单流程'
      }
    } else {
      // 情况1：没有stageProcess配置
      formLoadError.value = '请联系管理员配置此阶段的表单流程'
    }
  } catch (error: any) {
    console.error('加载流程表单失败:', error)
    message.error('加载流程表单失败')
    formLoadError.value = '请联系管理员配置此阶段的表单流程'
    currentStageForms.value = []
  }
}

// 初始化项目流程阶段（已删除，新建项目时会自动初始化）
const initProjectProcessIfNeeded = async () => {
  // 不再需要手动初始化，新建项目时后端会自动初始化流程阶段
  console.log('项目流程阶段会在项目创建时自动初始化')
}

// 在组件挂载时获取流程数据
onMounted(async () => {
  try {
    // 先获取项目流程信息
    await fetchProjectProcess()

    // 如果没有流程数据，尝试初始化
    if (projectProcesses.value.length === 0) {
      await initProjectProcessIfNeeded()
      // 重新获取流程数据
      await fetchProjectProcess()
    }

    // 如果没有设置当前阶段，则默认选择第一个阶段
    if (!activeKey.value) {
      const firstStage = mainProcessOptions.value[0]
      if (firstStage) {
        handleMenuSelect(firstStage.stageKey)
      }
    }
  } catch (error: any) {
    console.error('初始化流程页面失败:', error)
    message.error('初始化流程页面失败')
  }
})
</script>

<style scoped>
/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 动态高度样式 */
.process-page {
  height: calc(100vh - 120px);
  min-height: 600px;
  overflow: hidden;
}

.process-sidebar {
  height: 100%;
  min-height: 0;
}

/* 主要步骤区域 - 占70%高度 */
.main-steps-container {
  height: 70%;
  min-height: 300px;
}

/* 其他步骤区域 - 占30%高度 */
.secondary-steps-container {
  height: 30%;
  min-height: 150px;
}

.process-content {
  height: 100%;
  overflow-y: auto;
}

/* 响应式高度调整 */
@media (max-height: 800px) {
  .process-page {
    height: calc(100vh - 100px);
  }

  .main-steps-container {
    min-height: 250px;
  }

  .secondary-steps-container {
    min-height: 120px;
  }
}

@media (max-height: 600px) {
  .process-page {
    height: calc(100vh - 80px);
  }

  .main-steps-container {
    min-height: 200px;
  }

  .secondary-steps-container {
    min-height: 100px;
  }
}

/* 自定义滚动条 */
:deep(.n-scrollbar-content) {
  padding: 8px 0;
}

:deep(.n-scrollbar-rail) {
  right: 2px !important;
}

/* 确保内容区域能够正确滚动 */
:deep(.n-scrollbar) {
  height: 100%;
}

:deep(.n-scrollbar-container) {
  height: 100%;
}

/* 卡片悬停效果 */
.process-item {
  transition: all 0.2s ease;
}

/* 时间线样式 */
.timeline-container {
  height: 100%;
}

/* 固定的时间线头部 */
.timeline-header {
  flex-shrink: 0;
}

/* 简化的时间线显示 - 高度减半，垂直居中 */
.timeline-simple-fixed {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  min-height: 60px;
}

.timeline-simple-fixed .flex {
  padding-bottom: 0.25rem;
  align-items: center;
}

/* 可滚动的表单内容区域 */
.form-content-scrollable {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* 确保表单内容有适当的间距 */
.form-content-scrollable .n-card {
  margin-bottom: 1rem;
}

/* 多行文本省略 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 暗黑模式适配 */
.dark .n-card {
  background-color: rgba(17, 24, 39, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 标签页样式 */
:deep(.n-tabs-tab) {
  padding: 0 16px 12px;
  font-size: 14px;
  transition: color 0.2s;
}

:deep(.n-tabs-tab--active) {
  font-weight: 500;
  color: var(--n-tab-text-color-active);
}

:deep(.n-tabs-bar) {
  height: 2px;
  border-radius: 1px;
}

/* 菜单项样式 */
:deep(.process-menu) {
  width: 100%;
  --n-item-height: auto;
  --n-item-padding: 8px 0;
}

:deep(.process-menu .n-menu-item) {
  margin-bottom: 12px;
  border-radius: 8px;
  transition: all 0.3s;
  width: 100%;
  min-height: auto;
  background: transparent !important;
  padding: 0;
}

:deep(.process-menu .n-menu-item-content) {
  align-items: flex-start;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  min-height: auto;
  padding: 0;
  background: transparent !important;
}

:deep(.process-menu .n-menu-item-content__content) {
  width: 100%;
  min-width: 0;
  padding: 0;
}

:deep(.process-menu .n-menu-item-content__content-inner) {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 0;
  padding: 0;
}

/* 卡片悬停效果 */
:deep(.process-menu .n-menu-item-content--hover) {
  background: transparent !important;
}

:deep(.process-menu .n-menu-item-content--selected) {
  background: transparent !important;
  box-shadow: none !important;
}

/* 多行文本省略 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 确保文本不会溢出 */
:deep(.n-menu-item-content) {
  overflow: visible;
}

:deep(.n-menu-item-content__content) {
  overflow: visible;
  text-overflow: ellipsis;
  white-space: normal;
}

/* 进度条样式 */
.progress-bar {
  height: 4px;
  border-radius: 2px;
  background-color: #f0f0f0;
  overflow: hidden;
  margin-top: 4px;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 2px;
  background-color: var(--n-item-icon-color-active);
  transition: width 0.3s;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.dark ::-webkit-scrollbar-track {
  background-color: #1f2937;
}
</style>
