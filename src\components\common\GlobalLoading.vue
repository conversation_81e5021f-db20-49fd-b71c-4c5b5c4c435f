<template>
  <teleport to="body">
    <transition name="loading-fade">
      <div v-if="show" class="global-loading-overlay">
        <div class="loading-content">
          <n-spin size="large">
            <template #description>
              <div class="loading-text">{{ loadingText }}</div>
            </template>
          </n-spin>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useGlobalLoading } from '@/composables/useApi'

interface Props {
  show?: boolean
  text?: string
  delay?: number // 延迟显示时间（毫秒）
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  text: '加载中...',
  delay: 200
})

const { globalLoading } = useGlobalLoading()

// 内部显示状态
const internalShow = ref(false)
const loadingText = ref(props.text)

// 计算最终显示状态
const show = computed(() => props.show || globalLoading.value || internalShow.value)

// 延迟显示逻辑
let showTimer: number | null = null
let hideTimer: number | null = null

watch(show, (newShow) => {
  if (newShow) {
    // 清除隐藏定时器
    if (hideTimer) {
      clearTimeout(hideTimer)
      hideTimer = null
    }
    
    // 延迟显示
    if (props.delay > 0) {
      showTimer = window.setTimeout(() => {
        internalShow.value = true
      }, props.delay)
    } else {
      internalShow.value = true
    }
  } else {
    // 清除显示定时器
    if (showTimer) {
      clearTimeout(showTimer)
      showTimer = null
    }
    
    // 立即隐藏或延迟隐藏
    hideTimer = window.setTimeout(() => {
      internalShow.value = false
    }, 100)
  }
}, { immediate: true })

// 更新加载文本
watch(() => props.text, (newText) => {
  loadingText.value = newText
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (showTimer) {
    clearTimeout(showTimer)
  }
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
})

// 暴露方法供外部调用
const showLoading = (text?: string) => {
  if (text) {
    loadingText.value = text
  }
  internalShow.value = true
}

const hideLoading = () => {
  internalShow.value = false
}

defineExpose({
  showLoading,
  hideLoading
})
</script>

<style lang="less" scoped>
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.loading-text {
  margin-top: 12px;
  color: #666;
  font-size: 14px;
  text-align: center;
}

// 过渡动画
.loading-fade-enter-active,
.loading-fade-leave-active {
  transition: opacity 0.3s ease;
}

.loading-fade-enter-from,
.loading-fade-leave-to {
  opacity: 0;
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .global-loading-overlay {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .loading-content {
    background: #2d2d2d;
    color: #fff;
  }
  
  .loading-text {
    color: #ccc;
  }
}
</style>
