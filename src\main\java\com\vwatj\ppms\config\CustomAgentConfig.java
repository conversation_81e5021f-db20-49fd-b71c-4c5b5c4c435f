package com.vwatj.ppms.config;

import com.vwatj.ppms.agent.function.BusinessFunctionRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自定义Agent配置类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class CustomAgentConfig {
    
    private final BusinessFunctionRegistry businessFunctionRegistry;
    
    /**
     * 初始化业务功能注册表
     */
    @Bean
    public ApplicationRunner initBusinessFunctionRegistry() {
        return args -> {
            log.info("初始化业务功能注册表");
            businessFunctionRegistry.initialize();
            log.info("业务功能注册表初始化完成，注册功能数量: {}", 
                    businessFunctionRegistry.getAllFunctionNames().size());
        };
    }
}
