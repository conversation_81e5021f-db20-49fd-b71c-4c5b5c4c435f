<template>
  <div class="planning-submit-form">
    <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="top"
      require-mark-placement="right-hanging">
      <!-- 整体项目信息 -->
      <div v-if="stages.length > 0" class="project-summary mb-6 p-4 bg-gray-50 rounded-lg">
        <h4 class="text-base font-medium mb-4">项目整体信息</h4>
        <n-grid :cols="2" :x-gap="16">
          <n-gi>
            <n-form-item label="项目计划开始时间" path="projectStartTime">
              <n-date-picker v-model:value="formData.projectStartTime" type="date" placeholder="请选择项目开始时间"
                :disabled="readonly" clearable class="w-full" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="项目计划结束时间" path="projectEndTime">
              <n-date-picker v-model:value="formData.projectEndTime" type="date" placeholder="请选择项目结束时间"
                :disabled="readonly" clearable class="w-full" />
            </n-form-item>
          </n-gi>
        </n-grid>

        <n-form-item label="项目备注" path="projectRemarks">
          <n-input v-model:value="formData.projectRemarks" type="textarea" placeholder="请输入项目整体备注信息" :rows="3"
            :disabled="readonly" show-count :maxlength="500" />
        </n-form-item>
      </div>

      <div class="mb-6">
        <h4 class="text-base font-medium mb-4">项目计划排期</h4>
      </div>

      <!-- 流程阶段排期表格 -->
      <div v-if="stages.length > 0" class="stages-planning-table">
        <div class="table-container">
          <!-- 表格头部 -->
          <div class="table-header">
            <div class="header-cell milestone-col">流程阶段</div>
            <div class="header-cell date-col">计划时间</div>
            <div class="header-cell date-col">实际时间</div>
            <div class="header-cell status-col">状态</div>
          </div>

          <!-- 表格内容 -->
          <div class="table-body">
            <div v-for="(stage, index) in stages" :key="stage.stageKey" class="table-row">
              <!-- 流程阶段名 -->
              <div class="table-cell milestone-col">
                <div class="milestone-name">{{ stage.stageName }}</div>
                <div class="milestone-key">({{ stage.stageKey }})</div>
              </div>

              <!-- 计划时间 -->
              <div class="table-cell date-col">
                <div class="date-range" v-if="formData.stages[index]">
                  <n-form-item :path="`stages.${index}.plannedStartTime`" :show-label="false">
                    <n-date-picker v-model:value="formData.stages[index].plannedStartTime" type="date"
                      placeholder="开始日期" :disabled="readonly" clearable size="small" class="date-input" />
                  </n-form-item>
                  <span class="date-separator">-</span>
                  <n-form-item :path="`stages.${index}.plannedEndTime`" :show-label="false">
                    <n-date-picker v-model:value="formData.stages[index].plannedEndTime" type="date" placeholder="结束日期"
                      :disabled="readonly" clearable size="small" class="date-input" />
                  </n-form-item>
                </div>
              </div>

              <!-- 实际时间 -->
              <div class="table-cell date-col">
                <div class="date-range" v-if="formData.stages[index]">
                  <n-form-item :path="`stages.${index}.actualStartTime`" :show-label="false">
                    <n-date-picker v-model:value="formData.stages[index].actualStartTime" type="date" placeholder="开始日期"
                      :disabled="readonly" clearable size="small" class="date-input" />
                  </n-form-item>
                  <span class="date-separator">-</span>
                  <n-form-item :path="`stages.${index}.actualEndTime`" :show-label="false">
                    <n-date-picker v-model:value="formData.stages[index].actualEndTime" type="date" placeholder="结束日期"
                      :disabled="readonly" clearable size="small" class="date-input" />
                  </n-form-item>
                </div>
              </div>

              <!-- 状态 -->
              <div class="table-cell status-col">
                <n-tag :type="getStageStatusType(stage.status)" size="small">
                  {{ getStageStatusText(stage.status) }}
                </n-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="text-center py-8">
        <n-spin size="medium" />
        <div class="mt-2 text-gray-500">正在加载流程阶段...</div>
      </div>

      <!-- 空状态 -->
      <n-empty v-else description="暂无流程阶段数据" class="py-8">
        <template #icon>
          <n-icon :size="48" class="text-gray-300">
            <CalendarOutlined />
          </n-icon>
        </template>
      </n-empty>


    </n-form>

    <!-- 操作按钮 -->
    <div v-if="!readonly && stages.length > 0" class="form-actions mt-6 flex justify-end space-x-3">
      <n-button @click="handleReset">重置</n-button>
      <n-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ submitText || '提交计划' }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import {
  NForm,
  NFormItem,
  NDatePicker,
  NInput,
  NButton,
  NGrid,
  NGi,
  NTag,
  NEmpty,
  NIcon,
  NSpin,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { CalendarOutlined } from '@vicons/antd'
import { ProjectProcessApi } from '@/apis/project_process'
import type { ProjectProcess } from '@/types/project_process'

// Props定义
const props = defineProps<{
  initialData?: Record<string, any> // 初始数据
  readonly?: boolean // 是否只读
  submitText?: string // 提交按钮文本
  projectId?: number // 项目ID
}>()

// Emits定义
const emit = defineEmits<{
  submit: [data: Record<string, any>]
  change: [data: Record<string, any>]
}>()

const message = useMessage()
const formRef = ref<FormInst>()
const submitting = ref(false)
const loading = ref(false)
const stages = ref<ProjectProcess[]>([])

// 表单数据
const formData = ref<{
  stages: Array<{
    stageKey: string
    stageName: string
    plannedStartTime: number | null
    plannedEndTime: number | null
    actualStartTime: number | null
    actualEndTime: number | null
    remarks: string
  }>
  projectStartTime: number | null
  projectEndTime: number | null
  projectRemarks: string
}>({
  stages: [],
  projectStartTime: null,
  projectEndTime: null,
  projectRemarks: ''
})

// 表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {
    projectStartTime: {
      required: true,
      validator: (_rule: any, value: any) => {
        if (!value) {
          return new Error('请选择项目计划开始时间')
        }
        return true
      },
      trigger: ['blur', 'change']
    },
    projectEndTime: {
      required: true,
      validator: (_rule: any, value: any) => {
        if (!value) {
          return new Error('请选择项目计划结束时间')
        }
        return true
      },
      trigger: ['blur', 'change']
    }
  }

  // 为每个阶段添加验证规则
  if (formData.value.stages && formData.value.stages.length > 0) {
    formData.value.stages.forEach((stage, index) => {
      rules[`stages.${index}.plannedStartTime`] = {
        required: true,
        validator: (_rule: any, value: any) => {
          if (!value) {
            return new Error(`请选择${stage.stageName}的计划开始时间`)
          }
          return true
        },
        trigger: ['blur', 'change']
      }
      rules[`stages.${index}.plannedEndTime`] = {
        required: true,
        validator: (_rule: any, value: any) => {
          if (!value) {
            return new Error(`请选择${stage.stageName}的计划结束时间`)
          }
          return true
        },
        trigger: ['blur', 'change']
      }
    })
  }

  return rules
})



// 获取阶段状态类型
const getStageStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'active': return 'warning'
    case 'pending': return 'default'
    case 'skipped': return 'error'
    default: return 'default'
  }
}

// 获取阶段状态文本
const getStageStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'active': return '进行中'
    case 'pending': return '待开始'
    case 'skipped': return '已跳过'
    default: return '未知'
  }
}

// 加载流程阶段
const loadStages = async () => {
  if (!props.projectId) {
    message.error('缺少项目ID参数')
    return
  }

  loading.value = true
  try {
    const response = await ProjectProcessApi.getStagesByProjectId(props.projectId)
    if (response.data) {
      stages.value = response.data.sort((a, b) => a.sortOrder - b.sortOrder)
      initFormData()
    }
  } catch (error) {
    console.error('加载流程阶段失败:', error)
    message.error('加载流程阶段失败')
  } finally {
    loading.value = false
  }
}

// 初始化表单数据
const initFormData = () => {
  const stagesData = stages.value.map(stage => ({
    stageKey: stage.stageKey,
    stageName: stage.stageName,
    plannedStartTime: stage.plannedStartTime ? new Date(stage.plannedStartTime).getTime() : null,
    plannedEndTime: stage.plannedEndTime ? new Date(stage.plannedEndTime).getTime() : null,
    actualStartTime: stage.actualStartTime ? new Date(stage.actualStartTime).getTime() : null,
    actualEndTime: stage.actualEndTime ? new Date(stage.actualEndTime).getTime() : null,
    remarks: stage.remarks || ''
  }))

  formData.value = {
    stages: stagesData,
    projectStartTime: null,
    projectEndTime: null,
    projectRemarks: '',
    ...props.initialData
  }

  emit('change', formData.value)
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    // 构建提交数据
    const submitData = {
      ...formData.value,
      submittedAt: new Date().toISOString()
    }

    emit('submit', submitData)
    message.success('计划提交成功')
  } catch (error) {
    message.error('请检查表单填写是否完整')
  } finally {
    submitting.value = false
  }
}

// 处理表单重置
const handleReset = () => {
  formRef.value?.restoreValidation()
  initFormData()
  message.info('已重置表单')
}

// 监听表单数据变化
watch(formData, (newData) => {
  emit('change', newData)
}, { deep: true })

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    Object.assign(formData.value, newData)
  }
}, { deep: true })

// 监听stages变化，重新生成验证规则
watch(() => formData.value.stages.length, () => {
  // 触发验证规则重新计算
  nextTick(() => {
    formRef.value?.restoreValidation()
  })
})

// 组件挂载时加载数据
onMounted(() => {
  loadStages()
})
</script>

<style scoped>
.planning-submit-form {
  width: 100%;
}

/* 表格样式 */
.stages-planning-table {
  margin: 16px 0;
}

.table-container {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 13px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: #f9fafb;
}

.table-row:last-child {
  border-bottom: none;
}

.header-cell,
.table-cell {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #f3f4f6;
}

.header-cell:last-child,
.table-cell:last-child {
  border-right: none;
}

/* 列宽设置 */
.milestone-col {
  flex: 0 0 200px;
  min-width: 200px;
}

.date-col {
  flex: 0 0 280px;
  min-width: 280px;
}

.status-col {
  flex: 0 0 120px;
  min-width: 120px;
  justify-content: center;
}

/* 里程碑名称样式 */
.milestone-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  line-height: 1.4;
}

.milestone-key {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 2px;
}

/* 日期范围样式 */
.date-range {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.date-input {
  flex: 1;
  min-width: 0;
}

.date-separator {
  color: #9ca3af;
  font-size: 14px;
  flex-shrink: 0;
}

/* 表单项样式调整 */
:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-form-item .n-form-item-feedback-wrapper) {
  display: none;
}

:deep(.n-date-picker) {
  width: 100%;
}

:deep(.n-date-picker.n-date-picker--small) {
  font-size: 12px;
}

/* 状态标签样式 */
:deep(.n-tag) {
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
}

.project-summary {
  border: 1px solid #e5e7eb;
}

.stage-item {
  transition: all 0.2s ease;
}

.stage-item:hover {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-container {
    overflow-x: auto;
  }

  .table-header,
  .table-row {
    min-width: 800px;
  }
}
</style>
