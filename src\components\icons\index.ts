// 图标自动导入配置
// 从 @vicons/antd 导入所有需要的图标
export {
  // 箭头类
  ArrowUpOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  UpOutlined,
  DownOutlined,
  LeftOutlined,
  RightOutlined,
  
  // 星星类
  StarOutlined,
  StarFilled,
  StarTwotone as StarTwoTone,
  
  // 用户类
  UserOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  TeamOutlined,
  
  // 操作类
  PlusOutlined,
  MinusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SaveOutlined,
  
  // 搜索类
  SearchOutlined,
  FilterOutlined,
  
  // 设置类
  SettingOutlined,
  SettingFilled,
  
  // 导航类
  HomeOutlined,
  DashboardOutlined,
  ProjectOutlined,
  
  // 通知类
  BellOutlined,
  BellFilled,
  MessageOutlined,
  
  // 文件类
  FileOutlined,
  FileTextOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  BookOutlined,
  
  // 状态类
  CheckOutlined,
  CheckCircleOutlined,
  CheckCircleFilled,
  CloseOutlined,
  CloseCircleOutlined,
  ExclamationOutlined,
  ExclamationCircleOutlined,
  ExclamationCircleFilled,
  InfoOutlined,
  InfoCircleOutlined,
  QuestionOutlined,
  QuestionCircleOutlined,
  WarningOutlined,
  WarningFilled,
  
  // 时间类
  ClockCircleOutlined,
  CalendarOutlined,
  
  // 媒体类
  PlayCircleOutlined,
  PauseCircleOutlined,
  
  // 其他常用
  EyeOutlined,
  EyeInvisibleOutlined,
  HeartOutlined,
  HeartFilled,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined,
  
  // 系统类
  LogoutOutlined,
  LoginOutlined,
  PoweroffOutlined,
  ReloadOutlined,
  SyncOutlined,
  
  // 开发类
  CodeOutlined,
  BugOutlined,
  ExperimentOutlined,
  RocketOutlined,
  
  // 布局类
  MenuOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  
  // 列表类
  OrderedListOutlined,
  UnorderedListOutlined,
  
  // 机器人
  RobotOutlined,
  
  // 安全类
  SafetyOutlined,
  SafetyOutlined as ShieldOutlined,
  
  // 调度类
  ScheduleOutlined,
  
  // 部署类
  DeploymentUnitOutlined,
  
  // 选择类
  SelectOutlined,
  
  // 发送类
  SendOutlined,
  
} from '@vicons/antd'

// 导出图标类型，用于 TypeScript 支持
export type IconComponent = any
