// API 调用的通用 Composable
import { ref, readonly } from 'vue'
import { ApiError } from '@/apis'

// 全局加载状态管理
const globalLoading = ref(false)
const loadingCount = ref(0)

// 更新全局加载状态
const updateGlobalLoading = () => {
  globalLoading.value = loadingCount.value > 0
}

// 导入消息提示函数
import { showMessage } from '@/utils/errorHandler'
import { createDiscreteApi } from 'naive-ui'

// 创建独立的 API 实例
let discreteApi: any = null

function initDiscreteApi() {
  if (typeof window !== 'undefined' && !discreteApi) {
    try {
      discreteApi = createDiscreteApi(['message', 'loadingBar', 'dialog'])
    } catch (error) {
      console.warn('Failed to initialize discrete API:', error)
    }
  }
}

// 创建消息提示函数
const createMessage = () => {
  return {
    success: (msg: string) => showMessage('success', msg),
    error: (msg: string) => showMessage('error', msg),
    warning: (msg: string) => showMessage('warning', msg),
    info: (msg: string) => showMessage('info', msg)
  }
}

// 创建加载条函数
const createLoadingBar = () => {
  if (!discreteApi) {
    initDiscreteApi()
  }

  if (discreteApi) {
    return discreteApi.loadingBar
  }

  return {
    start: () => {},
    finish: () => {},
    error: () => {}
  }
}

// API 调用 Hook
export const useApi = () => {
  const message = createMessage()
  const loadingBar = createLoadingBar()
  
  // 本地加载状态
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 执行 API 调用
  const execute = async <T>(
    apiCall: () => Promise<T>,
    options: {
      showLoading?: boolean
      showLoadingBar?: boolean
      showSuccessMessage?: string
      showErrorMessage?: boolean
      onSuccess?: (data: T) => void
      onError?: (error: any) => void
    } = {}
  ): Promise<T | null> => {
    const {
      showLoading = true,
      showLoadingBar = false,
      showSuccessMessage,
      showErrorMessage = true,
      onSuccess,
      onError
    } = options

    try {
      // 开始加载
      if (showLoading) {
        loading.value = true
        loadingCount.value++
        updateGlobalLoading()
      }
      
      if (showLoadingBar) {
        loadingBar.start()
      }

      // 清空之前的错误
      error.value = null

      // 执行 API 调用
      const result = await apiCall()

      // 成功处理
      if (showSuccessMessage) {
        message.success(showSuccessMessage)
      }

      if (onSuccess) {
        onSuccess(result)
      }

      if (showLoadingBar) {
        loadingBar.finish()
      }

      return result
    } catch (err: any) {
      // 错误处理
      let errorMessage = '操作失败'
      
      if (err instanceof ApiError) {
        errorMessage = err.message
        error.value = err.message
      } else if (err.message) {
        errorMessage = err.message
        error.value = err.message
      } else {
        error.value = errorMessage
      }

      if (showErrorMessage) {
        message.error(errorMessage)
      }

      if (onError) {
        onError(err)
      }

      if (showLoadingBar) {
        loadingBar.error()
      }

      console.error('API调用失败:', err)
      return null
    } finally {
      // 结束加载
      if (showLoading) {
        loading.value = false
        loadingCount.value--
        updateGlobalLoading()
      }
    }
  }

  // 批量执行 API 调用
  const executeBatch = async <T>(
    apiCalls: Array<() => Promise<T>>,
    options: {
      showLoading?: boolean
      showLoadingBar?: boolean
      showSuccessMessage?: string
      showErrorMessage?: boolean
      onSuccess?: (results: (T | null)[]) => void
      onError?: (errors: any[]) => void
      failFast?: boolean // 是否在第一个失败时停止
    } = {}
  ): Promise<(T | null)[]> => {
    const {
      showLoading = true,
      showLoadingBar = false,
      showSuccessMessage,
      showErrorMessage = true,
      onSuccess,
      onError,
      failFast = false
    } = options

    try {
      if (showLoading) {
        loading.value = true
        loadingCount.value++
        updateGlobalLoading()
      }
      
      if (showLoadingBar) {
        loadingBar.start()
      }

      const results: (T | null)[] = []
      const errors: any[] = []

      for (let i = 0; i < apiCalls.length; i++) {
        try {
          const result = await apiCalls[i]()
          results.push(result)
        } catch (err) {
          results.push(null)
          errors.push(err)
          
          if (failFast) {
            throw err
          }
        }
      }

      if (errors.length > 0 && showErrorMessage) {
        message.error(`${errors.length} 个操作失败`)
      } else if (showSuccessMessage) {
        message.success(showSuccessMessage)
      }

      if (onSuccess) {
        onSuccess(results)
      }

      if (onError && errors.length > 0) {
        onError(errors)
      }

      if (showLoadingBar) {
        loadingBar.finish()
      }

      return results
    } catch (err: any) {
      if (showErrorMessage) {
        message.error(err.message || '批量操作失败')
      }

      if (onError) {
        onError([err])
      }

      if (showLoadingBar) {
        loadingBar.error()
      }

      throw err
    } finally {
      if (showLoading) {
        loading.value = false
        loadingCount.value--
        updateGlobalLoading()
      }
    }
  }

  return {
    loading,
    error,
    globalLoading,
    execute,
    executeBatch
  }
}

// 导出全局加载状态
export const useGlobalLoading = () => {
  return {
    globalLoading: readonly(globalLoading),
    loadingCount: readonly(loadingCount)
  }
}

// 错误处理工具函数
export const handleApiError = (error: any, defaultMessage = '操作失败') => {
  const message = createMessage()
  
  let errorMessage = defaultMessage
  
  if (error instanceof ApiError) {
    errorMessage = error.message
  } else if (error.message) {
    errorMessage = error.message
  }
  
  message.error(errorMessage)
  console.error('API错误:', error)
  
  return errorMessage
}

// 成功消息工具函数
export const showSuccessMessage = (msg: string) => {
  const messageApi = createMessage()
  messageApi.success(msg)
}

// 确认对话框工具函数
export const useConfirm = () => {
  if (!discreteApi) {
    initDiscreteApi()
  }

  const confirm = (
    title: string,
    content: string,
    onConfirm: () => void | Promise<void>,
    options: {
      positiveText?: string
      negativeText?: string
      type?: 'warning' | 'error' | 'info'
    } = {}
  ) => {
    const {
      positiveText = '确定',
      negativeText = '取消',
      type = 'warning'
    } = options

    if (discreteApi?.dialog) {
      discreteApi.dialog[type]({
        title,
        content,
        positiveText,
        negativeText,
        onPositiveClick: async () => {
          try {
            await onConfirm()
          } catch (error) {
            handleApiError(error)
          }
        }
      })
    } else {
      // 降级处理
      if (window.confirm(`${title}\n\n${content}`)) {
        Promise.resolve(onConfirm()).catch(error => {
          handleApiError(error)
        })
      }
    }
  }

  return { confirm }
}
