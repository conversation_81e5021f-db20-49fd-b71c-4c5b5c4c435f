<template>
  <div class="assign-person-node">
    <div class="node-header">
      <h4 class="node-title">{{ title }}</h4>
      <n-tag :type="statusType" size="small">{{ statusText }}</n-tag>
    </div>

    <div class="assign-form" v-if="!readonly">
      <n-form :model="formData" label-placement="left" label-width="100px">
        <n-form-item label="负责人" required>
          <n-select
            v-model:value="formData.assignee"
            :options="assigneeOptions"
            placeholder="请选择负责人"
            clearable
            filterable
          >
            <template #empty>
              <div class="empty-option">
                <n-empty description="暂无可选负责人" size="small" />
              </div>
            </template>
          </n-select>
        </n-form-item>
        
        <n-form-item label="备注">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5 }"
            placeholder="请输入备注信息（可选）..."
          />
        </n-form-item>
      </n-form>
    </div>

    <div class="assign-info" v-else-if="assignData">
      <div class="info-item">
        <span class="info-label">负责人:</span>
        <span class="info-value">{{ assignData.assigneeName }}</span>
      </div>
      <div class="info-item" v-if="assignData.remark">
        <span class="info-label">备注:</span>
        <span class="info-value">{{ assignData.remark }}</span>
      </div>
      <div class="info-item" v-if="assignData.assignedAt">
        <span class="info-label">分配时间:</span>
        <span class="info-value">{{ formatTime(assignData.assignedAt) }}</span>
      </div>
    </div>

    <div class="empty-state" v-else-if="readonly">
      <n-empty description="暂无分配信息" />
    </div>

    <div class="node-actions" v-if="!readonly">
      <n-button 
        type="primary" 
        @click="submitNode" 
        :loading="submitting" 
        :disabled="!formData.assignee"
      >
        确认分配
      </n-button>
      <n-button @click="resetNode">重置</n-button>
    </div>

    <div class="node-actions" v-else>
      <n-button @click="editNode">重新分配</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { 
  NTag, NForm, NFormItem, NSelect, NInput, NButton, NEmpty,
  useMessage 
} from 'naive-ui'
import type { AssignPersonData } from '@/types/project_process'

interface Props {
  title: string
  status: 'pending' | 'current' | 'completed'
  data?: AssignPersonData & { assignedAt?: string }
  readonly?: boolean
  defaultAssignee?: string // 默认负责人（如项目PM）
}

interface Emits {
  (e: 'submit', data: AssignPersonData & { assignedAt: string }): void
  (e: 'edit'): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<Emits>()
const message = useMessage()

const submitting = ref(false)
const assignData = ref<(AssignPersonData & { assignedAt?: string }) | null>(props.data || null)

// 表单数据
const formData = ref({
  assignee: '',
  assigneeName: '',
  remark: ''
})

// 负责人选项（这里可以从API获取）
const assigneeOptions = ref([
  { label: '张三 (PM)', value: 'zhangsan', role: 'PM' },
  { label: '李四 (开发)', value: 'lisi', role: 'Developer' },
  { label: '王五 (测试)', value: 'wangwu', role: 'Tester' },
  { label: '赵六 (运维)', value: 'zhaoliu', role: 'DevOps' },
  { label: '钱七 (产品)', value: 'qianqi', role: 'Product' },
  { label: '孙八 (设计)', value: 'sunba', role: 'Designer' }
])

// 状态映射
const statusMap = {
  pending: { type: 'default', text: '待处理' },
  current: { type: 'info', text: '进行中' },
  completed: { type: 'success', text: '已完成' }
}

const statusType = computed(() => statusMap[props.status].type as 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary')
const statusText = computed(() => statusMap[props.status].text)

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    assignData.value = { ...newData }
    formData.value = {
      assignee: newData.assignee || '',
      assigneeName: newData.assigneeName || '',
      remark: newData.remark || ''
    }
  }
}, { immediate: true })

// 监听负责人选择变化
watch(() => formData.value.assignee, (newAssignee) => {
  const option = assigneeOptions.value.find(opt => opt.value === newAssignee)
  if (option) {
    formData.value.assigneeName = option.label
  }
})

// 组件挂载时设置默认值
onMounted(() => {
  if (props.defaultAssignee && !formData.value.assignee) {
    const defaultOption = assigneeOptions.value.find(opt => opt.value === props.defaultAssignee)
    if (defaultOption) {
      formData.value.assignee = defaultOption.value
      formData.value.assigneeName = defaultOption.label
    }
  }
})

import { formatDate, DATE_FORMATS } from '@/utils/dateTime'

// 格式化时间
const formatTime = (timeStr: string) => {
  return formatDate(timeStr, DATE_FORMATS.DATETIME_MINUTE)
}

// 提交节点
const submitNode = async () => {
  if (!formData.value.assignee) {
    message.error('请选择负责人')
    return
  }

  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const data: AssignPersonData & { assignedAt: string } = {
      assignee: formData.value.assignee,
      assigneeName: formData.value.assigneeName,
      remark: formData.value.remark,
      assignedAt: new Date().toISOString()
    }
    
    assignData.value = data
    emit('submit', data)
    message.success('负责人分配成功')
  } catch (error) {
    message.error('分配失败')
  } finally {
    submitting.value = false
  }
}

// 重置节点
const resetNode = () => {
  formData.value = {
    assignee: props.defaultAssignee || '',
    assigneeName: '',
    remark: ''
  }
  
  // 重新设置默认负责人名称
  if (props.defaultAssignee) {
    const defaultOption = assigneeOptions.value.find(opt => opt.value === props.defaultAssignee)
    if (defaultOption) {
      formData.value.assigneeName = defaultOption.label
    }
  }
  
  message.info('已重置')
}

// 编辑节点
const editNode = () => {
  emit('edit')
}
</script>

<style scoped>
.assign-person-node {
  padding: 20px;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.node-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.assign-form {
  margin-bottom: 20px;
}

.assign-info {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #6b7280;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #374151;
  flex: 1;
}

.empty-option {
  padding: 20px;
  text-align: center;
}

.empty-state {
  margin: 40px 0;
}

.node-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* 暗黑模式适配 */
.dark .node-title {
  color: #f3f4f6;
}

.dark .assign-info {
  background: #1f2937;
  border-color: #374151;
}

.dark .info-label {
  color: #9ca3af;
}

.dark .info-value {
  color: #f3f4f6;
}

.dark .node-actions {
  border-top-color: #374151;
}
</style>
