package com.vwatj.ppms.agent.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * LLM提供商工厂类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LLMProviderFactory {

    private final List<LLMProvider> providers;

    /**
     * 根据提供商名称获取对应的LLM提供商
     *
     * @param providerName 提供商名称
     * @return LLM提供商实例
     */
    public LLMProvider getProvider(String providerName) {
        if (providerName == null || providerName.isEmpty()) {
            throw new IllegalArgumentException("LLM提供商名称不能为空");
        }

        for (LLMProvider provider : providers) {
            if (provider.supports(providerName)) {
                log.debug("找到LLM提供商: {} -> {}", providerName, provider.getClass().getSimpleName());
                return provider;
            }
        }

        throw new IllegalArgumentException("不支持的LLM提供商: " + providerName);
    }

    /**
     * 获取所有支持的提供商名称
     *
     * @return 提供商名称列表
     */
    public List<String> getSupportedProviders() {
        return providers.stream()
                .map(LLMProvider::getProviderName)
                .toList();
    }

    /**
     * 检查是否支持指定的提供商
     *
     * @param providerName 提供商名称
     * @return 是否支持
     */
    public boolean isSupported(String providerName) {
        if (providerName == null || providerName.isEmpty()) {
            return false;
        }

        return providers.stream()
                .anyMatch(provider -> provider.supports(providerName));
    }
}
