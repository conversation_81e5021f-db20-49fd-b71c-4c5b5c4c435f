/**
 * 时间格式转换工具函数
 */

/**
 * 将各种时间格式转换为时间戳（毫秒）
 * @param time 时间值，可以是字符串、数字或Date对象
 * @returns 时间戳（毫秒）或undefined
 */
export function toTimestamp(time: string | number | Date | null | undefined): number | undefined {
  if (!time) return undefined
  
  if (typeof time === 'number') {
    // 如果是秒级时间戳，转换为毫秒
    return time < 10000000000 ? time * 1000 : time
  }
  
  if (typeof time === 'string') {
    const date = new Date(time)
    return isNaN(date.getTime()) ? undefined : date.getTime()
  }
  
  if (time instanceof Date) {
    return time.getTime()
  }
  
  return undefined
}

/**
 * 将时间戳转换为Date对象
 * @param timestamp 时间戳（毫秒或秒）
 * @returns Date对象或null
 */
export function timestampToDate(timestamp: number | null | undefined): Date | null {
  if (!timestamp) return null
  
  // 如果是秒级时间戳，转换为毫秒
  const ms = timestamp < 10000000000 ? timestamp * 1000 : timestamp
  return new Date(ms)
}

/**
 * 格式化时间戳为字符串
 * @param timestamp 时间戳（毫秒或秒）
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function formatTimestamp(timestamp: number | null | undefined, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!timestamp) return ''
  
  const date = timestampToDate(timestamp)
  if (!date) return ''
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 将任务数据中的时间字段转换为时间戳格式
 * @param taskData 任务数据
 * @returns 转换后的任务数据
 */
export function convertTaskTimesToTimestamp(taskData: any): any {
  return {
    ...taskData,
    plannedStartTime: toTimestamp(taskData.plannedStartTime),
    plannedEndTime: toTimestamp(taskData.plannedEndTime),
    actualStartTime: toTimestamp(taskData.actualStartTime),
    actualEndTime: toTimestamp(taskData.actualEndTime),
  }
}

/**
 * 检查日期值是否有效
 * @param dateValue 日期值
 * @returns 是否为有效日期
 */
export function isValidDate(dateValue: any): boolean {
  if (!dateValue) return false
  if (dateValue === '') return false
  const date = new Date(dateValue)
  return !isNaN(date.getTime())
}

/**
 * 安全地转换日期为时间戳
 * @param dateValue 日期值
 * @returns 时间戳或null
 */
export function safeToTimestamp(dateValue: any): number | null {
  if (!isValidDate(dateValue)) return null
  return new Date(dateValue).getTime()
}

/**
 * 安全地格式化日期字符串
 * @param dateValue 日期值
 * @param format 格式字符串
 * @returns 格式化后的日期字符串或空字符串
 */
export function safeFormatDate(dateValue: any, format: string = 'YYYY-MM-DD'): string {
  if (!isValidDate(dateValue)) return ''
  return formatTimestamp(safeToTimestamp(dateValue)!, format)
}

/**
 * 将任务数据中的时间戳转换为Date对象
 * @param taskData 任务数据
 * @returns 转换后的任务数据
 */
export function convertTaskTimesToDate(taskData: any): any {
  return {
    ...taskData,
    plannedStartTime: timestampToDate(taskData.plannedStartTime),
    plannedEndTime: timestampToDate(taskData.plannedEndTime),
    actualStartTime: timestampToDate(taskData.actualStartTime),
    actualEndTime: timestampToDate(taskData.actualEndTime),
  }
}

/**
 * 获取当前时间戳（毫秒）
 * @returns 当前时间戳
 */
export function now(): number {
  return Date.now()
}

/**
 * 判断时间戳是否为今天
 * @param timestamp 时间戳
 * @returns 是否为今天
 */
export function isToday(timestamp: number): boolean {
  const date = timestampToDate(timestamp)
  if (!date) return false
  
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

/**
 * 计算两个时间戳之间的天数差
 * @param start 开始时间戳
 * @param end 结束时间戳
 * @returns 天数差
 */
export function daysBetween(start: number, end: number): number {
  const startDate = timestampToDate(start)
  const endDate = timestampToDate(end)
  
  if (!startDate || !endDate) return 0
  
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}
