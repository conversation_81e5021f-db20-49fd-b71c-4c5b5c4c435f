/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ApprovalTimeline: typeof import('./components/project/process/ApprovalTimeline.vue')['default']
    AssignPersonNode: typeof import('./components/project/process/nodes/AssignPersonNode.vue')['default']
    CABSubmitForm: typeof import('./components/form/CABSubmitForm.vue')['default']
    CompleteNode: typeof import('./components/project/process/nodes/CompleteNode.vue')['default']
    DocumentUploadNode: typeof import('./components/project/process/nodes/DocumentUploadNode.vue')['default']
    DynamicForm: typeof import('./components/form/DynamicForm.vue')['default']
    ErrorBoundary: typeof import('./components/common/ErrorBoundary.vue')['default']
    FormNode: typeof import('./components/project/process/nodes/FormNode.vue')['default']
    GlobalLoading: typeof import('./components/common/GlobalLoading.vue')['default']
    IconAntDesignArrowDownOutlined: typeof import('~icons/ant-design/arrow-down-outlined')['default']
    IconAntDesignArrowUpOutlined: typeof import('~icons/ant-design/arrow-up-outlined')['default']
    IconAntDesignBookOutlined: typeof import('~icons/ant-design/book-outlined')['default']
    IconAntDesignCalendarOutlined: typeof import('~icons/ant-design/calendar-outlined')['default']
    IconAntDesignClockCircleOutlined: typeof import('~icons/ant-design/clock-circle-outlined')['default']
    IconAntDesignDeleteOutlined: typeof import('~icons/ant-design/delete-outlined')['default']
    IconAntDesignEditOutlined: typeof import('~icons/ant-design/edit-outlined')['default']
    IconAntDesignEyeOutlined: typeof import('~icons/ant-design/eye-outlined')['default']
    IconAntDesignLinkOutlined: typeof import('~icons/ant-design/link-outlined')['default']
    IconAntDesignPlusOutlined: typeof import('~icons/ant-design/plus-outlined')['default']
    IconAntDesignProjectOutlined: typeof import('~icons/ant-design/project-outlined')['default']
    IconAntDesignStarFilled: typeof import('~icons/ant-design/star-filled')['default']
    IconAntDesignStarOutlined: typeof import('~icons/ant-design/star-outlined')['default']
    IconAntDesignUserOutlined: typeof import('~icons/ant-design/user-outlined')['default']
    ImportModal: typeof import('./components/common/ImportModal.vue')['default']
    NA: typeof import('naive-ui')['NA']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBadge: typeof import('naive-ui')['NBadge']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCode: typeof import('naive-ui')['NCode']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDivider: typeof import('naive-ui')['NDivider']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NDynamicTags: typeof import('naive-ui')['NDynamicTags']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NIcon: typeof import('naive-ui')['NIcon']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NModal: typeof import('naive-ui')['NModal']
    NPagination: typeof import('naive-ui')['NPagination']
    NPopover: typeof import('naive-ui')['NPopover']
    NProgress: typeof import('naive-ui')['NProgress']
    NResult: typeof import('naive-ui')['NResult']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NStatistic: typeof import('naive-ui')['NStatistic']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NTimeline: typeof import('naive-ui')['NTimeline']
    NTimelineItem: typeof import('naive-ui')['NTimelineItem']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NUpload: typeof import('naive-ui')['NUpload']
    OtherInfoForm: typeof import('./components/system/OtherInfoForm.vue')['default']
    PlanningSubmitForm: typeof import('./components/form/PlanningSubmitForm.vue')['default']
    ProcessForm: typeof import('./components/project/process/ProcessForm.vue')['default']
    ProjectCard: typeof import('./components/project/ProjectCard.vue')['default']
    ProjectCategoryFormModal: typeof import('./components/setting/ProjectCategoryFormModal.vue')['default']
    ProjectCategoryTable: typeof import('./components/setting/ProjectCategoryTable.vue')['default']
    ProjectFormModal: typeof import('./components/project/ProjectFormModal.vue')['default']
    ProjectGrid: typeof import('./components/project/ProjectGrid.vue')['default']
    RichTextEditor: typeof import('./components/common/RichTextEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ServerInfoForm: typeof import('./components/system/ServerInfoForm.vue')['default']
    SystemAssetModal: typeof import('./components/system/SystemAssetModal.vue')['default']
    SystemDictFormModal: typeof import('./components/setting/SystemDictFormModal.vue')['default']
    SystemDictTable: typeof import('./components/setting/SystemDictTable.vue')['default']
    TaskCategoryFormModal: typeof import('./components/setting/TaskCategoryFormModal.vue')['default']
    TaskCategoryTable: typeof import('./components/setting/TaskCategoryTable.vue')['default']
    TaskDetailModal: typeof import('./components/task/TaskDetailModal.vue')['default']
    TaskFormModal: typeof import('./components/task/TaskFormModal.vue')['default']
    TaskKanbanView: typeof import('./components/task/views/TaskKanbanView.vue')['default']
    TaskListView: typeof import('./components/task/views/TaskListView.vue')['default']
    TaskTableView: typeof import('./components/task/views/TaskTableView.vue')['default']
    TaskView: typeof import('./components/task/TaskView.vue')['default']
  }
}
