<template>
  <div v-if="hasError" class="error-boundary">
    <n-result status="500" title="页面出错了" description="抱歉，页面遇到了一些问题">
      <template #footer>
        <n-space>
          <n-button @click="retry" type="primary">重试</n-button>
          <n-button @click="goHome">返回首页</n-button>
          <n-button @click="reportError" quaternary>报告问题</n-button>
        </n-space>
      </template>
    </n-result>
    
    <!-- 错误详情（开发环境显示） -->
    <n-card v-if="isDev && errorInfo" class="error-details" title="错误详情">
      <n-code :code="errorInfo" language="javascript" />
    </n-card>
  </div>
  
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'

const router = useRouter()
const message = useMessage()

const hasError = ref(false)
const errorInfo = ref('')
const isDev = import.meta.env.DEV

// 捕获子组件错误
onErrorCaptured((error: Error, instance, info) => {
  console.error('ErrorBoundary 捕获到错误:', error)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)
  
  hasError.value = true
  errorInfo.value = `${error.message}\n\n${error.stack}\n\n组件信息: ${info}`
  
  // 在生产环境中上报错误
  if (!isDev) {
    reportErrorToService(error, info)
  }
  
  return false // 阻止错误继续传播
})

// 重试
const retry = async () => {
  hasError.value = false
  errorInfo.value = ''
  
  // 等待下一个 tick 后重新渲染
  await nextTick()
  
  // 可以在这里添加重新加载数据的逻辑
  message.info('正在重试...')
}

// 返回首页
const goHome = () => {
  hasError.value = false
  router.push('/')
}

// 报告错误
const reportError = () => {
  // 这里可以打开错误报告对话框或跳转到反馈页面
  message.info('感谢您的反馈，我们会尽快处理')
}

// 上报错误到服务端（生产环境）
const reportErrorToService = (error: Error, info: string) => {
  // 这里可以调用错误上报 API
  const errorData = {
    message: error.message,
    stack: error.stack,
    componentInfo: info,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: Date.now()
  }
  
  // 发送到错误监控服务
  fetch('/api/errors', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(errorData)
  }).catch(err => {
    console.error('错误上报失败:', err)
  })
}

// 监听全局未捕获的错误
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)
  if (!isDev) {
    reportErrorToService(event.error, 'Global Error')
  }
})

// 监听 Promise 未捕获的错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的 Promise 错误:', event.reason)
  if (!isDev) {
    reportErrorToService(new Error(event.reason), 'Unhandled Promise Rejection')
  }
})
</script>

<style lang="less" scoped>
.error-boundary {
  padding: 24px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.error-details {
  margin-top: 24px;
  max-width: 800px;
  width: 100%;
}
</style>
