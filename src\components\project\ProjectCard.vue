<script setup lang="ts">
import { computed } from 'vue'
import type { Project } from '@/types'
import {
  getProjectStatusText,
  getProjectStatusType,
  getProgressColor,
  getProjectProgress
} from '@/utils/project'
import { formatDate as formatDateTime, DATE_FORMATS } from '@/utils/dateTime'

interface Props {
  project: Project
}

interface Emits {
  (e: 'click', projectId: string | number): void
  (e: 'star', projectId: string | number): void
  (e: 'edit', projectId: string | number): void
  (e: 'delete', projectId: string | number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 生成默认封面图片 - 显示资产号+名称
const generateDefaultCover = (assetNo: string, name: string, category: string) => {
  // 限制文本长度避免溢出
  const maxLength = 20
  let displayText = assetNo ? `${assetNo}` : name
  if (displayText.length > maxLength) {
    displayText = displayText.substring(0, maxLength - 3) + '...'
  }

  let projectName = name
  if (projectName.length > maxLength) {
    projectName = projectName.substring(0, maxLength - 3) + '...'
  }

  const encodedAssetText = encodeURIComponent(displayText)
  const encodedProjectText = encodeURIComponent(projectName)

  // 根据 category 设置不同的渐变背景色
  let gradientColors
  switch (category) {
  case '新项目开发': // 新项目开发
    gradientColors = '%3Cstop offset="0%25" style="stop-color:%233b82f6;stop-opacity:1" /%3E%3Cstop offset="100%25" style="stop-color:%231d4ed8;stop-opacity:1" /%3E'
    break
  case '需求变更': // 需求变更
    gradientColors = '%3Cstop offset="0%25" style="stop-color:%23f59e0b;stop-opacity:1" /%3E%3Cstop offset="100%25" style="stop-color:%23d97706;stop-opacity:1" /%3E'
    break
  default: // 默认
    gradientColors = '%3Cstop offset="0%25" style="stop-color:%23667eea;stop-opacity:1" /%3E%3Cstop offset="100%25" style="stop-color:%23764ba2;stop-opacity:1" /%3E'
}

  return `data:image/svg+xml,%3Csvg width="300" height="200" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3ClinearGradient id="grad1" x1="0%25" y1="0%25" x2="100%25" y2="100%25"%3E${gradientColors}%3C/linearGradient%3E%3C/defs%3E%3Crect width="300" height="200" fill="url(%23grad1)" /%3E%3Ctext x="150" y="90" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle"%3E${encodedAssetText}%3C/text%3E%3Ctext x="150" y="110" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.9)" text-anchor="middle" dominant-baseline="middle"%3E${encodedProjectText}%3C/text%3E%3C/svg%3E`
}

// 计算实际显示的封面图片
const displayCover = computed(() => {
  if (props.project.cover && props.project.cover.trim() !== '') {
    return props.project.cover
  }
  // 获取关联资产信息来生成默认封面
  const assetNo = props.project.assetNo || ''
  const projectName = props.project.name || 'Untitled Project'
  const category = props.project.category || '';
  return generateDefaultCover(assetNo, projectName, category)
})

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 如果不是默认生成的图片，则使用默认图片
  if (!img.src.startsWith('data:image/svg+xml')) {
      const assetNo = props.project.assetNo || ''
    const projectName = props.project.name || 'Untitled Project'
    const category = props.project.category || '';
    img.src = generateDefaultCover(assetNo, projectName, category)
  } else {
    // 如果默认图片也加载失败，隐藏图片显示fallback
    const projectCard = img.closest('.project-card')
    if (projectCard) {
      img.style.display = 'none'
    }
  }
}

// 图片加载成功处理
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = 'block';
};

// 工具函数
const getStatusType = (status: string): 'default' | 'error' | 'info' | 'primary' | 'success' | 'warning' => {
  return getProjectStatusType(status)
}

const getStatusDisplay = (status: string | undefined): string => {
  if (!status) return '未知状态'
  return getProjectStatusText(status as any)
}



const formatDate = (timestamp: number | null): string => {
  return formatDateTime(timestamp, DATE_FORMATS.DATE)
}

// 计算基于时间的项目进度
const projectProgress = computed(() => {
  return getProjectProgress(props.project)
})
</script>

<template>
  <div class="project-card" @click="emit('click', project.id)" style="cursor: pointer">
    <div class="project-cover">
      <img :src="displayCover" :alt="project.name" @error="handleImageError" @load="handleImageLoad" />
      <div class="project-cover-fallback">
        <div class="fallback-content">
          <n-icon size="48" color="#ccc">
            <IconAntDesignProjectOutlined />
          </n-icon>
          <span class="fallback-text">{{ project.name }}</span>
        </div>
      </div>

    </div>
    <div class="project-info">
      <div class="project-header">
        <n-ellipsis class="project-name" :line-clamp="1">
          {{ project.name }}
        </n-ellipsis>
        <n-tag v-if="project.version" size="small" type="info" round>
          {{ project.version }}
        </n-tag>
      </div>
      <div class="project-meta">
        <div class="meta-item">
          <n-icon size="14">
            <IconAntDesignUserOutlined />
          </n-icon>
          <span>{{ project.manager }}</span>
        </div>
        <div class="meta-item">
          <n-icon size="14">
            <IconAntDesignBookOutlined />
          </n-icon>
          <span>{{ project.category }}</span>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="project-dates">
        <div class="date-item" v-if="project.startDate">
          <n-icon size="12">
            <IconAntDesignCalendarOutlined />
          </n-icon>
          <span class="date-label">开始:</span>
          <span class="date-value">{{ formatDate(project.startDate) }}</span>
        </div>
        <div class="date-item" v-if="project.endDate">
          <n-icon size="12">
            <IconAntDesignClockCircleOutlined />
          </n-icon>
          <span class="date-label">结束:</span>
          <span class="date-value">{{ formatDate(project.endDate) }}</span>
        </div>
      </div>

      <!-- 关联资产信息 -->
      <div class="project-asset" v-if="project.relatedAssetId">
        <div class="asset-item">
          <n-icon size="12">
            <IconAntDesignLinkOutlined />
          </n-icon>
          <span class="asset-label">关联资产:</span>
          <span class="asset-value">{{`${project.assetName ?? '--'} ${project.assetNo ? `(${project.assetNo ?? ''})` : ''} `}}</span>
        </div>
      </div>

      <!-- 标签 -->
      <div class="project-tags" v-if="project.tags && project.tags.length > 0">
        <n-tag v-for="tag in project.tags.slice(0, 3)" :key="tag" size="tiny" type="default" round class="tag-item">
          {{ tag }}
        </n-tag>
        <n-tag v-if="project.tags.length > 3" size="tiny" type="default" round class="tag-more">
          +{{ project.tags.length - 3 }}
        </n-tag>
      </div>
      <div class="project-status">
        <n-tag size="small" :type="getStatusType(project.status)">
          {{ getStatusDisplay(project.status) }}
        </n-tag>
        <n-progress type="line" :percentage="projectProgress" :height="6" :border-radius="3"
          :fill-border-radius="3" :show-indicator="false" :color="getProgressColor(projectProgress)"
          style="flex: 1; margin-left: 8px" />
        <span class="progress-text">{{ projectProgress }}%</span>
      </div>
    </div>

    <!-- 悬浮遮罩层 -->
    <div class="project-overlay">
      <!-- Star按钮 -->
      <div class="project-star" @click.stop="emit('star', project.id)">
        <n-icon :size="20" :color="project.starred ? '#ffcc00' : '#8c8c8c'">
          <IconAntDesignStarFilled v-if="project.starred" />
          <IconAntDesignStarOutlined v-else />
        </n-icon>
      </div>

      <!-- 查看按钮 -->
      <div class="project-view-action">
        <n-button size="large" type="primary" circle @click.stop="emit('click', project.id)">
          <template #icon>
            <n-icon size="20">
              <IconAntDesignEyeOutlined />
            </n-icon>
          </template>
        </n-button>
        <span class="view-text">查看详情</span>
      </div>

      <!-- 底部操作按钮 -->
      <div class="project-actions">
        <n-button size="small" quaternary @click.stop="emit('edit', project.id)" class="action-btn edit-btn">
          <template #icon>
            <n-icon>
              <IconAntDesignEditOutlined />
            </n-icon>
          </template>
          编辑
        </n-button>
        <n-button size="small" quaternary @click.stop="emit('delete', project.id)" class="action-btn delete-btn">
          <template #icon>
            <n-icon>
              <IconAntDesignDeleteOutlined />
            </n-icon>
          </template>
          删除
        </n-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import "@/assets/styles/variables.less";

.project-card {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fff;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 320px;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #52c41a);
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.08),
      0 0 0 3px rgba(24, 144, 255, 0.1),
      0 0 20px rgba(24, 144, 255, 0.15);
    border-color: rgba(24, 144, 255, 0.3);

    &::before {
      opacity: 1;
    }

    .project-overlay {
      opacity: 1;
      visibility: visible;
    }
  }
}

.project-cover {
  position: relative;
  height: 140px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
    opacity: 0.8;
    transition: opacity 0.3s;
    z-index: 2;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
  }

  .project-card:hover & {
    &::after {
      opacity: 0.6;
    }

    img {
      transform: scale(1.08);
    }
  }
}

// 默认背景样式
.project-cover-fallback {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 0;

  .fallback-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;
    padding: 20px;

    .fallback-text {
      color: white;
      font-size: 14px;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      line-height: 1.4;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .n-icon {
      opacity: 0.8;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }
  }
}



.project-info {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 0;
  /* 确保 flex 子元素可以收缩 */
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;

  .project-name {
    font-weight: 600;
    color: #1f2d3d;
    font-size: 15px;
    margin: 0;
    flex: 1;
    min-width: 0;
    line-height: 1.4;
    text-align: left;
  }

  .n-tag {
    font-size: 11px;
    height: 20px;
    line-height: 18px;
    border-radius: 10px;
    padding: 0 8px;
    flex-shrink: 0;
  }
}

.project-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #666;
  margin: 4px 0;

  .meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 0;

    .n-icon {
      color: #999;
      font-size: 14px;
      flex-shrink: 0;
    }

    span {
      line-height: 1;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 时间信息样式
.project-dates {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #666;

  .date-item {
    display: flex;
    align-items: center;
    gap: 4px;

    .n-icon {
      color: #999;
      flex-shrink: 0;
    }

    .date-label {
      font-weight: 500;
      min-width: 32px;
    }

    .date-value {
      color: #333;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 11px;
    }
  }
}

// 关联资产样式
.project-asset {
  font-size: 12px;
  color: #666;

  .asset-item {
    display: flex;
    align-items: center;
    gap: 4px;

    .n-icon {
      color: #999;
      flex-shrink: 0;
    }

    .asset-label {
      font-weight: 500;
      min-width: 56px;
    }

    .asset-value {
      color: #333;
      font-size: 11px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }
  }
}

// 标签样式
.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;

  .tag-item {
    font-size: 10px;
    height: 18px;
    line-height: 16px;
    padding: 0 6px;
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    border: 1px solid rgba(24, 144, 255, 0.2);
  }

  .tag-more {
    font-size: 10px;
    height: 18px;
    line-height: 16px;
    padding: 0 6px;
    background: rgba(0, 0, 0, 0.05);
    color: #999;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}

.project-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: auto;
  /* 推到底部 */
  padding-top: 12px;
  border-top: 1px dashed #f0f0f0;

  .n-tag {
    height: 22px;
    line-height: 20px;
    font-size: 12px;
    font-weight: 500;
    padding: 0 8px;
  }

  .n-progress {
    flex: 1;

    :deep(.n-progress-rail) {
      background-color: #f5f5f5;
    }
  }

  .progress-text {
    font-size: 12px;
    color: #8c8c8c;
    min-width: 36px;
    text-align: right;
    font-weight: 500;
    font-feature-settings: "tnum";
  }
}

// 悬浮遮罩层
.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px 16px;
  border-radius: 12px;
  z-index: 10;
  /* 确保覆盖层在最上层 */

  // Star按钮在覆盖层中的样式
  .project-star {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(255, 255, 255, 0.9);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    z-index: 11;
    /* 确保在覆盖层之上 */
    transition: all 0.3s;

    &:hover {
      background: #fff;
      transform: scale(1.1);

      .n-icon {
        color: #ffcc00 !important;
      }
    }

    .n-icon {
      transition: all 0.3s;
    }
  }
}

// 查看按钮区域
.project-view-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: center;

  .n-button {
    width: 56px;
    height: 56px;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
    }
  }

  .view-text {
    color: white;
    font-size: 14px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

// 底部操作按钮
.project-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  width: 100%;

  .action-btn {
    flex: 1;
    font-size: 12px;
    height: 36px;
    border-radius: 8px;
    font-weight: 500;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    // 编辑按钮样式
    &.edit-btn {
      background: rgba(24, 144, 255, 0.8);

      &:hover {
        background: rgba(24, 144, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.4);
      }
    }

    // 删除按钮样式
    &.delete-btn {
      background: rgba(245, 63, 63, 0.8);

      &:hover {
        background: rgba(245, 63, 63, 0.9);
        border-color: rgba(255, 255, 255, 0.4);
      }
    }

    :deep(.n-button__content) {
      color: white;
    }

    :deep(.n-icon) {
      color: white;
    }
  }
}
</style>
