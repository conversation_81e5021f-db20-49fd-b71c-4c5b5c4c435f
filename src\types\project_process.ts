// 项目流程相关类型定义（基于后端ProjectProcess实体）

// 项目流程阶段状态
export type ProjectProcessStatus = 'pending' | 'active' | 'completed' | 'skipped'

// 阶段流程表单状态
export type StageFormStatus = 'pending' | 'current' | 'completed'

// 项目流程阶段实体
export interface ProjectProcess {
  /** 主键ID */
  id: number
  /** 项目ID */
  projectId: number
  /** 阶段Key（如：requirement_collect, bbp, tab等） */
  stageKey: string
  /** 阶段名称 */
  stageName: string
  /** 阶段描述 */
  description?: string
  /** 阶段排序号 */
  sortOrder: number
  /** 阶段进度（0-100） */
  progress: number
  /** 阶段状态：pending-待开始，active-进行中，completed-已完成，skipped-已跳过 */
  status: ProjectProcessStatus
  /** 是否主要流程（true-主要流程，false-其他流程） */
  isMainProcess: boolean
  /** 阶段所处流程JSON配置 */
  stageProcess?: string
  /** 计划开始时间 */
  plannedStartTime?: string
  /** 计划结束时间 */
  plannedEndTime?: string
  /** 实际开始时间 */
  actualStartTime?: string
  /** 实际结束时间 */
  actualEndTime?: string
  /** 备注 */
  remarks?: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人 */
  createBy: string
  /** 更新人 */
  updateBy: string
  /** 是否删除 */
  deleted: boolean
}

// 阶段流程表单配置
export interface StageProcessForm {
  /** 表单Key */
  formKey: string
  /** 表单名称 */
  formName: string
  /** 表单状态 */
  status: StageFormStatus
  /** 排序号 */
  order: number
  /** 表单数据 */
  data?: Record<string, any>
  /** 完成时间 */
  completedAt?: string
  /** 完成人 */
  completedBy?: string
}



// 项目流程响应数据
export interface ProjectProcessResponse {
  /** 流程阶段列表 */
  stages: ProjectProcess[]
  /** 当前活跃阶段Key */
  currentStage?: string
  /** 整体进度 */
  progress: number
}



// ===== 组件需要的简化类型定义 =====

// 节点类型
export type NodeType = 'upload_document' | 'assign_person' | 'form' | 'complete'

// 节点状态
export type NodeStatus = 'pending' | 'current' | 'completed'

// 流程节点定义（组件用）
export interface ProcessNode {
  key: string
  title: string
  type: NodeType
  status: NodeStatus
  completedAt?: string
  data?: any
}

// 文档上传节点数据
export interface DocumentUploadData {
  documents: {
    id: string
    name: string
    url: string
    uploadTime: string
    size: number
  }[]
}

// 分配负责人节点数据
export interface AssignPersonData {
  assignee: string
  assigneeName: string
  remark?: string
}

// 表单节点数据
export interface FormData {
  [key: string]: any
}

// 完成节点数据
export interface CompleteData {
  completedBy: string
  completedAt: string
  remark?: string
}

// ===== 流程表单相关类型定义 =====

// 流程表单模板实体
export interface ProcessForm {
  /** 主键ID */
  id: number
  /** 表单Key（唯一标识） */
  formKey: string
  /** 表单名称 */
  formName: string
  /** 排序号 */
  orderNum: number
  /** 表单结构JSON配置 */
  formStruct?: string
  /** 表单描述 */
  description?: string
  /** 是否启用 */
  enabled: boolean
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人 */
  createBy: string
  /** 更新人 */
  updateBy: string
  /** 是否删除 */
  deleted: boolean
  /** 表单组件类型 */
  formType: number
}

// 流程表单提交参数
export interface SubmitStageFormParams {
  /** 项目ID */
  projectId: number
  /** 阶段Key */
  stageKey: string
  /** 表单Key */
  formKey: string
  /** 表单数据 */
  formData: Record<string, any>
}

// 流程表单数据更新参数
export interface UpdateStageFormDataParams {
  /** 项目ID */
  projectId: number
  /** 阶段Key */
  stageKey: string
  /** 表单Key */
  formKey: string
  /** 表单数据 */
  formData: Record<string, any>
}



