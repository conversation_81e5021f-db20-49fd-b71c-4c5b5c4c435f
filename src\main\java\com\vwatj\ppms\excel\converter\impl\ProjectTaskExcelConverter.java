package com.vwatj.ppms.excel.converter.impl;

import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.enums.TaskStatusEnum;
import com.vwatj.ppms.excel.converter.ExcelDataConverter;
import com.vwatj.ppms.excel.converter.ExcelDataConvertException;
import com.vwatj.ppms.excel.util.ExcelUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 任务Excel数据转换器
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public class ProjectTaskExcelConverter implements ExcelDataConverter<ProjectTask> {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Object[] entityToRowData(ProjectTask entity) {
        return new Object[]{
                entity.getTaskNo(),
                entity.getTitle(),
                entity.getStageName(),
                entity.getIssueType(),
                entity.getAssignee(),
                entity.getReporter(),
                entity.getDescription(),
                entity.getStatus().getDescription(),
                formatDateTime(entity.getPlannedStartTime()),
                formatDateTime(entity.getPlannedEndTime()),
                formatDateTime(entity.getActualStartTime()),
                formatDateTime(entity.getActualEndTime()),
                entity.getDuration(),
                entity.getPriority()
        };
    }

    @Override
    public ProjectTask rowDataToEntity(Map<String, Object> rowData, int rowIndex) throws ExcelDataConvertException {
        // 验证必填字段
        validateRequiredFields(rowData, rowIndex);

        ProjectTask projectTask = new ProjectTask();

        try {
            // 必填字段
            projectTask.setTaskNo(getRequiredString(rowData, "任务编号", rowIndex));
            projectTask.setTitle(getRequiredString(rowData, "任务标题", rowIndex));
            projectTask.setStageName(getString(rowData, "阶段"));
            projectTask.setIssueType(getString(rowData, "问题类型"));
            projectTask.setAssignee(getRequiredString(rowData, "指派人", rowIndex));
            projectTask.setReporter(getRequiredString(rowData, "报告人", rowIndex));
            projectTask.setStatus(getTaskStatus(rowData, "状态"));
            projectTask.setPriority(getString(rowData, "优先级"));

            // 可选字段
            projectTask.setDescription(getString(rowData, "描述"));

            // 时间字段
            projectTask.setPlannedStartTime(parseDateTime(rowData, "计划开始时间"));
            projectTask.setPlannedEndTime(parseDateTime(rowData, "计划结束时间"));
            projectTask.setActualStartTime(parseDateTime(rowData, "实际开始时间"));
            projectTask.setActualEndTime(parseDateTime(rowData, "实际结束时间"));

            // 数值字段
            projectTask.setDuration(ExcelUtils.parseBigDecimal(rowData.get("时长(小时)")));

            return projectTask;

        } catch (Exception e) {
            if (e instanceof ExcelDataConvertException) {
                throw e;
            }
            throw new ExcelDataConvertException(rowIndex, "数据转换失败: " + e.getMessage());
        }
    }

    @Override
    public String[] getExportHeaders() {
        return new String[]{
                "任务编号*", "任务标题*", "阶段", "问题类型*", "指派人*", "报告人*", "描述", "状态*",
                "计划开始时间", "计划结束时间", "实际开始时间", "实际结束时间",
                "时长(小时)", "优先级*"
        };
    }

    @Override
    public String[] getTemplateHeaders() {
        return new String[]{
                "任务编号*", "任务标题*", "阶段", "问题类型*", "指派人*", "报告人*", "描述", "状态*",
                "计划开始时间", "计划结束时间", "实际开始时间", "实际结束时间",
                "时长(小时)", "优先级*"
        };
    }

    @Override
    public String getUniqueField() {
        return "taskNo";
    }

    @Override
    public Object getUniqueFieldValue(ProjectTask entity) {
        return entity.getTaskNo();
    }

    @Override
    public int[] getColumnWidths() {
        return new int[]{
                15, // 任务编号
                25, // 任务标题
                15, // 阶段
                15, // 问题类型
                15, // 指派人
                15, // 报告人
                35, // 描述
                12, // 状态
                20, // 计划开始时间
                20, // 计划结束时间
                20, // 实际开始时间
                20, // 实际结束时间
                12, // 时长
                12  // 优先级
        };
    }

    // 辅助方法
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(FORMATTER) : "";
    }

    private String getRequiredString(Map<String, Object> rowData, String fieldName, int rowIndex)
            throws ExcelDataConvertException {
        String value = getString(rowData, fieldName);
        if (value == null || value.trim().isEmpty()) {
            throw new ExcelDataConvertException(rowIndex, fieldName, "必填字段不能为空");
        }
        return value.trim();
    }

    private TaskStatusEnum getTaskStatus(Map<String, Object> rowData, String fieldName) {
        Object value = rowData.get(fieldName);
        return value != null ? TaskStatusEnum.fromDescription(value.toString().trim()) : null;
    }

    private String getString(Map<String, Object> rowData, String fieldName) {
        Object value = rowData.get(fieldName);
        return value != null ? value.toString().trim() : null;
    }

    private LocalDateTime parseDateTime(Map<String, Object> rowData, String fieldName) {
        Object value = rowData.get(fieldName);
        if (value == null) {
            return null;
        }

        // 如果是Date对象，直接转换
        if (value instanceof java.util.Date) {
            return ((java.util.Date) value).toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime();
        }

        // 如果是数值，可能是Excel的日期序列号
        if (value instanceof Number) {
            try {
                // Excel日期序列号转换
                double excelDate = ((Number) value).doubleValue();
                if (excelDate > 0) {
                    // Excel的日期基准是1900年1月1日，但实际上是1899年12月30日
                    java.time.LocalDate baseDate = java.time.LocalDate.of(1899, 12, 30);
                    long days = (long) excelDate;
                    double timeFraction = excelDate - days;

                    java.time.LocalDate date = baseDate.plusDays(days);
                    java.time.LocalTime time = java.time.LocalTime.ofSecondOfDay((long) (timeFraction * 24 * 60 * 60));

                    return java.time.LocalDateTime.of(date, time);
                }
            } catch (Exception e) {
                // 如果不是有效的Excel日期，继续按字符串处理
            }
        }

        String dateStr = value.toString().trim();
        if (dateStr.isEmpty()) {
            return null;
        }

        try {
            return ExcelUtils.parseDateTime(dateStr);
        } catch (Exception e) {
            throw new RuntimeException(fieldName + "格式错误: " + e.getMessage());
        }
    }
}
