package com.vwatj.ppms.agent.function;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务功能注册中心
 * 管理所有可用的业务功能
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BusinessFunctionRegistry {
    
    private final List<BusinessFunction> businessFunctions;
    private final Map<String, BusinessFunction> functionMap = new HashMap<>();
    
    /**
     * 初始化功能注册表
     */
    public void initialize() {
        functionMap.clear();
        for (BusinessFunction function : businessFunctions) {
            functionMap.put(function.getFunctionName(), function);
            log.info("注册业务功能: {} - {}", function.getFunctionName(), function.getFunctionDescription());
        }
    }
    
    /**
     * 获取功能实例
     */
    public BusinessFunction getFunction(String functionName) {
        if (functionMap.isEmpty()) {
            initialize();
        }
        return functionMap.get(functionName);
    }
    
    /**
     * 获取所有功能名称
     */
    public List<String> getAllFunctionNames() {
        if (functionMap.isEmpty()) {
            initialize();
        }
        return List.copyOf(functionMap.keySet());
    }
    
    /**
     * 获取所有功能的Schema定义
     */
    public Map<String, Map<String, Object>> getAllFunctionSchemas() {
        if (functionMap.isEmpty()) {
            initialize();
        }
        
        Map<String, Map<String, Object>> schemas = new HashMap<>();
        for (BusinessFunction function : functionMap.values()) {
            Map<String, Object> schema = new HashMap<>();
            schema.put("name", function.getFunctionName());
            schema.put("description", function.getFunctionDescription());
            schema.put("parameters", function.getParameterSchema());
            schemas.put(function.getFunctionName(), schema);
        }
        return schemas;
    }
    
    /**
     * 执行功能
     */
    public Object executeFunction(String functionName, Map<String, Object> parameters) {
        BusinessFunction function = getFunction(functionName);
        if (function == null) {
            return Map.of("error", "功能不存在: " + functionName);
        }
        
        if (!function.validateParameters(parameters)) {
            return Map.of("error", "参数验证失败");
        }
        
        try {
            return function.execute(parameters);
        } catch (Exception e) {
            log.error("执行功能失败: functionName={}", functionName, e);
            return Map.of("error", "执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查功能是否存在
     */
    public boolean hasFunction(String functionName) {
        if (functionMap.isEmpty()) {
            initialize();
        }
        return functionMap.containsKey(functionName);
    }
}
