// 用户状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi, type LoginParams } from '@/apis'
import type { User } from '@/types'
import { showMessage } from '@/utils/errorHandler'

export const useUserStore = defineStore('user', () => {

  // 状态
  const currentUser = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refreshToken'))
  const permissions = ref<string[]>([])
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!currentUser.value)
  const isAdmin = computed(() => currentUser.value?.role === 'admin')
  const isManager = computed(() => currentUser.value?.role === 'manager')

  // 检查权限
  const hasPermission = (resource: string, action: string) => {
    if (isAdmin.value) return true
    return permissions.value.includes(`${resource}:${action}`)
  }

  // 登录
  const login = async (params: LoginParams) => {
    try {
      loading.value = true
      const response = await userApi.login(params)
      
      // 保存认证信息
      token.value = response.data.token
      refreshToken.value = response.data.refreshToken
      currentUser.value = response.data.user
      permissions.value = response.data.permissions.map(p => `${p.resource}:${p.action}`)
      
      // 保存到本地存储
      localStorage.setItem('token', response.data.token)
      localStorage.setItem('refreshToken', response.data.refreshToken)
      localStorage.setItem('currentUserId', String(response.data.user.id))
      
      // 记住用户名
      if (params.rememberMe) {
        localStorage.setItem('saved_username', params.username)
      } else {
        localStorage.removeItem('saved_username')
      }
      
      showMessage('success', '登录成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await userApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清空状态
      token.value = null
      refreshToken.value = null
      currentUser.value = null
      permissions.value = []
      
      // 清空本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('currentUserId')
      
      showMessage('success', '已退出登录')
    }
  }

  // 刷新token
  const refreshAuthToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }
      
      const response = await userApi.refreshToken(refreshToken.value)
      
      token.value = response.data.token
      localStorage.setItem('token', response.data.token)
      
      return response.data.token
    } catch (error: any) {
      // 刷新失败，清空认证信息
      await logout()
      throw error
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    try {
      loading.value = true
      const response = await userApi.getCurrentUser()
      currentUser.value = response.data
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '获取用户信息失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新当前用户信息
  const updateCurrentUser = async (data: Partial<User>) => {
    try {
      loading.value = true
      const response = await userApi.updateCurrentUser(data)
      currentUser.value = response.data
      showMessage('success', '个人信息更新成功')
      return response.data
    } catch (error: any) {
      showMessage('error', error.message || '更新个人信息失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      loading.value = true
      await userApi.changePassword({ oldPassword, newPassword })
      showMessage('success', '密码修改成功')
    } catch (error: any) {
      showMessage('error', error.message || '密码修改失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 上传头像
  const uploadAvatar = async (file: File) => {
    try {
      loading.value = true
      const response = await userApi.uploadAvatar(file)
      
      if (currentUser.value) {
        currentUser.value.avatar = response.data.avatarUrl
      }
      
      showMessage('success', '头像上传成功')
      return response.data.avatarUrl
    } catch (error: any) {
      showMessage('error', error.message || '头像上传失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 检查权限（API调用）
  const checkPermission = async (resource: string, action: string) => {
    try {
      const response = await userApi.checkPermission(resource, action)
      return response.data.hasPermission
    } catch (error: any) {
      console.error('权限检查失败:', error)
      return false
    }
  }

  // 初始化用户状态
  const initializeAuth = async () => {
    const savedToken = localStorage.getItem('token')
    const savedRefreshToken = localStorage.getItem('refreshToken')
    
    if (savedToken && savedRefreshToken) {
      token.value = savedToken
      refreshToken.value = savedRefreshToken
      
      try {
        // 尝试获取用户信息
        await fetchCurrentUser()
      } catch (error) {
        // 如果获取失败，尝试刷新token
        try {
          await refreshAuthToken()
          await fetchCurrentUser()
        } catch (refreshError) {
          // 刷新也失败，清空认证信息
          await logout()
        }
      }
    }
  }

  // 重置状态
  const reset = () => {
    currentUser.value = null
    token.value = null
    refreshToken.value = null
    permissions.value = []
    loading.value = false
  }

  return {
    // 状态
    currentUser,
    token,
    refreshToken,
    permissions,
    loading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isManager,
    
    // 方法
    hasPermission,
    login,
    logout,
    refreshAuthToken,
    fetchCurrentUser,
    updateCurrentUser,
    changePassword,
    uploadAvatar,
    checkPermission,
    initializeAuth,
    reset
  }
})
