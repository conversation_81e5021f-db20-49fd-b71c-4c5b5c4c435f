// 用户和权限相关 API 接口
import { httpClient, buildPaginationParams } from './index'
import type {
  User,
  UserRole,
  Permission,
  ApiResponse,
  PaginationParams,
  PaginationResponse
} from '@/types'

// 登录参数
export interface LoginParams {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录响应
export interface LoginResponse {
  token: string
  refreshToken: string
  user: User
  permissions: Permission[]
  expiresIn: number
}

// 用户查询参数
export interface UserQueryParams extends PaginationParams {
  keyword?: string
  role?: UserRole
  department?: string
  isActive?: boolean | undefined
}

// 用户创建参数
export interface CreateUserParams {
  username: string
  email: string
  password: string
  fullName: string
  role: UserRole
  department?: string
  position?: string
  phone?: string
  avatar?: string
}

// 用户更新参数
export interface UpdateUserParams extends Partial<Omit<CreateUserParams, 'username' | 'password'>> {
  id: string
}

// 修改密码参数
export interface ChangePasswordParams {
  oldPassword: string
  newPassword: string
}

// 重置密码参数
export interface ResetPasswordParams {
  userId: string
  newPassword: string
}

// 用户 API 类
export class UserApi {
  private static readonly BASE_PATH = '/users'
  private static readonly AUTH_PATH = '/auth'

  /**
   * 用户登录
   */
  static async login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
    return httpClient.post<LoginResponse>(`${this.AUTH_PATH}/login`, params)
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<ApiResponse<void>> {
    return httpClient.post<void>(`${this.AUTH_PATH}/logout`)
  }

  /**
   * 刷新token
   */
  static async refreshToken(refreshToken: string): Promise<ApiResponse<{ token: string; expiresIn: number }>> {
    return httpClient.post<{ token: string; expiresIn: number }>(`${this.AUTH_PATH}/refresh`, { refreshToken })
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    return httpClient.get<User>(`${this.AUTH_PATH}/me`)
  }

  /**
   * 更新当前用户信息
   */
  static async updateCurrentUser(data: Partial<Omit<User, 'id' | 'username' | 'role' | 'createdAt' | 'updatedAt'>>): Promise<ApiResponse<User>> {
    return httpClient.put<User>(`${this.AUTH_PATH}/me`, data)
  }

  /**
   * 修改当前用户密码
   */
  static async changePassword(params: ChangePasswordParams): Promise<ApiResponse<void>> {
    return httpClient.patch<void>(`${this.AUTH_PATH}/change-password`, params)
  }

  /**
   * 获取用户列表
   */
  static async getUsers(params?: UserQueryParams): Promise<ApiResponse<PaginationResponse<User>>> {
    const queryParams = params ? {
      ...buildPaginationParams(params),
      keyword: params.keyword,
      role: params.role,
      department: params.department,
      isActive: params.isActive,
    } : undefined

    return httpClient.get<PaginationResponse<User>>(this.BASE_PATH, queryParams)
  }

  /**
   * 获取用户选项列表（用于下拉选择）
   * 统一供项目经理、任务报告人/指派人、系统资产PM负责人等字段使用
   */
  static async getUserOptions(): Promise<ApiResponse<Array<{label: string, value: string}>>> {
    return httpClient.get<Array<{label: string, value: string}>>(`${this.BASE_PATH}/options`)
  }

  /**
   * 获取用户详情
   */
  static async getUser(id: string): Promise<ApiResponse<User>> {
    return httpClient.get<User>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建用户
   */
  static async createUser(data: CreateUserParams): Promise<ApiResponse<User>> {
    return httpClient.post<User>(this.BASE_PATH, data)
  }

  /**
   * 更新用户
   */
  static async updateUser(data: UpdateUserParams): Promise<ApiResponse<User>> {
    const { id, ...updateData } = data
    return httpClient.put<User>(`${this.BASE_PATH}/${id}`, updateData)
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: string): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 启用/禁用用户
   */
  static async toggleUserStatus(id: string): Promise<ApiResponse<{ isActive: boolean }>> {
    return httpClient.patch<{ isActive: boolean }>(`${this.BASE_PATH}/${id}/toggle-status`)
  }

  /**
   * 重置用户密码
   */
  static async resetUserPassword(params: ResetPasswordParams): Promise<ApiResponse<void>> {
    const { userId, ...data } = params
    return httpClient.patch<void>(`${this.BASE_PATH}/${userId}/reset-password`, data)
  }

  /**
   * 上传用户头像
   */
  static async uploadAvatar(file: File): Promise<ApiResponse<{ avatarUrl: string }>> {
    return httpClient.upload<{ avatarUrl: string }>(`${this.AUTH_PATH}/avatar`, file)
  }

  /**
   * 获取用户权限
   */
  static async getUserPermissions(id: string): Promise<ApiResponse<Permission[]>> {
    return httpClient.get<Permission[]>(`${this.BASE_PATH}/${id}/permissions`)
  }

  /**
   * 更新用户权限
   */
  static async updateUserPermissions(id: string, permissions: string[]): Promise<ApiResponse<void>> {
    return httpClient.put<void>(`${this.BASE_PATH}/${id}/permissions`, { permissions })
  }

  /**
   * 获取所有权限列表
   */
  static async getAllPermissions(): Promise<ApiResponse<Permission[]>> {
    return httpClient.get<Permission[]>('/permissions')
  }

  /**
   * 检查用户权限
   */
  static async checkPermission(resource: string, action: string): Promise<ApiResponse<{ hasPermission: boolean }>> {
    return httpClient.get<{ hasPermission: boolean }>(`${this.AUTH_PATH}/check-permission`, { resource, action })
  }

  /**
   * 获取用户活动日志
   */
  static async getUserActivityLog(id: string, params?: PaginationParams): Promise<ApiResponse<PaginationResponse<{
    id: string
    action: string
    resource: string
    resourceId: string
    details?: Record<string, any>
    ipAddress?: string
    userAgent?: string
    createdAt: number
  }>>> {
    const queryParams = params ? buildPaginationParams(params) : undefined
    return httpClient.get(`${this.BASE_PATH}/${id}/activity-log`, queryParams)
  }

  /**
   * 批量导入用户
   */
  static async importUsers(file: File): Promise<ApiResponse<{
    total: number
    success: number
    failed: number
    errors?: Array<{
      row: number
      field: string
      message: string
    }>
  }>> {
    return httpClient.upload('/users/import', file)
  }

  /**
   * 导出用户数据
   */
  static async exportUsers(params?: UserQueryParams): Promise<ApiResponse<{ downloadUrl: string }>> {
    const queryParams = params ? {
      keyword: params.keyword,
      role: params.role,
      department: params.department,
      isActive: params.isActive,
    } : undefined

    return httpClient.get<{ downloadUrl: string }>(`${this.BASE_PATH}/export`, queryParams)
  }
}

// 导出默认实例
export const userApi = UserApi
