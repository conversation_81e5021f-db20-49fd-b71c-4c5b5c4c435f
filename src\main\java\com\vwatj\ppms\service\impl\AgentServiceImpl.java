package com.vwatj.ppms.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.agent.CustomAgent;
import com.vwatj.ppms.agent.function.BusinessFunctionRegistry;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.*;
import com.vwatj.ppms.entity.Agent;
import com.vwatj.ppms.enums.AgentTypeEnum;
import com.vwatj.ppms.mapper.AgentMapper;
import com.vwatj.ppms.service.AgentService;
import com.vwatj.ppms.agent.llm.LLMProvider;
import com.vwatj.ppms.agent.llm.LLMProviderFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一Agent服务实现类
 * 合并了原AgentServiceImpl、CustomAgentServiceImpl、UnifiedAgentServiceImpl和ChatServiceImpl的功能
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentServiceImpl extends ServiceImpl<AgentMapper, Agent> implements AgentService {

    private final AgentMapper agentMapper;
    private final BusinessFunctionRegistry functionRegistry;
    private final LLMProviderFactory llmProviderFactory;
    private final ApplicationContext applicationContext;

    // Agent实例缓存
    private final ConcurrentHashMap<Long, CustomAgent> agentInstanceCache = new ConcurrentHashMap<>();

    @Override
    public PageResult<Agent> getAgentPage(AgentQueryDTO queryDTO) {
        Page<Agent> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<Agent> result = agentMapper.selectAgentPage(page,
                queryDTO.getKeyword(),
                queryDTO.getStatus(),
                queryDTO.getType(),
                queryDTO.getCreatorId());
        return PageResult.from(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Agent createAgent(CreateAgentDTO createAgentDTO) {
        try {
            log.info("创建Agent: name={}, type={}", createAgentDTO.getName(), createAgentDTO.getType());

            // 检查名称是否已存在
            Agent existingAgent = agentMapper.selectByName(createAgentDTO.getName());
            if (existingAgent != null) {
                throw new RuntimeException("Agent名称已存在");
            }

            Agent agent = new Agent();
            BeanUtils.copyProperties(createAgentDTO, agent);

            // 根据Agent类型设置默认配置
            AgentTypeEnum agentType = AgentTypeEnum.fromCode(createAgentDTO.getType());
            if (agentType == null) {
                agentType = AgentTypeEnum.GENERAL; // 默认为通用类型
                agent.setType(AgentTypeEnum.GENERAL);
            }

            // 设置默认LLM配置
            setDefaultLLMConfig(agent, agentType);

            // 设置默认提示词
            setDefaultPrompts(agent, agentType, createAgentDTO.getType());

            // 设置功能配置
            setDefaultFunctionConfig(agent, agentType);

            // 设置实现类名（预设类型）
            setImplementationClass(agent, agentType, createAgentDTO.getType());

            // 设置创建者信息
            agent.setCreatorId(1L); // TODO: 从当前用户上下文获取
            agent.setCreatorName("系统管理员");

            // 保存Agent
            agentMapper.insert(agent);

            // 如果是预设类型，自动初始化
            if (agentType.isPreset()) {
                initializeAgent(agent.getId());
            }

            log.info("Agent创建成功: agentId={}, name={}, type={}", agent.getId(), agent.getName(), agent.getType());
            return agent;

        } catch (Exception e) {
            log.error("创建Agent失败: name={}, type={}", createAgentDTO.getName(), createAgentDTO.getType(), e);
            throw new RuntimeException("创建Agent失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Agent updateAgent(UpdateAgentDTO updateAgentDTO) {
        Agent agent = agentMapper.selectById(updateAgentDTO.getId());
        if (agent == null) {
            throw new RuntimeException("Agent不存在");
        }

        // 如果名称有变更，检查新名称是否已存在
        if (updateAgentDTO.getName() != null && !updateAgentDTO.getName().equals(agent.getName())) {
            Agent existingAgent = agentMapper.selectByName(updateAgentDTO.getName());
            if (existingAgent != null && !existingAgent.getId().equals(updateAgentDTO.getId())) {
                throw new RuntimeException("Agent名称已存在");
            }
        }

        BeanUtils.copyProperties(updateAgentDTO, agent, "id", "creatorId", "creatorName");
        agentMapper.updateById(agent);
        return agent;
    }

    @Override
    public void deleteAgent(Long id) {
        Agent agent = agentMapper.selectById(id);
        if (agent == null) {
            throw new RuntimeException("Agent不存在");
        }
        agentMapper.deleteById(id);
    }

    @Override
    public Agent getAgentByName(String name) {
        return agentMapper.selectByName(name);
    }

    @Override
    public void toggleAgentStatus(Long id) {
        Agent agent = agentMapper.selectById(id);
        if (agent == null) {
            throw new RuntimeException("Agent不存在");
        }

        // 切换状态：0变1，1变0
        agent.setStatus(agent.getStatus() == 1 ? 0 : 1);
        agentMapper.updateById(agent);
    }


    @Override
    public void initializeAgent(Long agentId) {
        try {
            log.info("初始化Agent: agentId={}", agentId);

            Agent agent = this.getById(agentId);
            if (agent == null) {
                throw new RuntimeException("Agent不存在: " + agentId);
            }

            if (agent.getType() == null) {
                throw new RuntimeException("不支持的Agent类型");
            }

            // 只有预设类型的Agent需要特殊初始化
            if (agent.getType().isPreset()) {
                CustomAgent customAgent = getAgentInstance(agent);
                customAgent.initialize(agent);
                agentInstanceCache.put(agentId, customAgent);
                log.info("预设Agent初始化成功: agentId={}, class={}", agentId, agent.getImplementationClass());
            } else {
                log.info("通用Agent无需特殊初始化: agentId={}, type={}", agentId, agent.getType());
            }

        } catch (Exception e) {
            log.error("初始化Agent失败: agentId={}", agentId, e);
            throw new RuntimeException("初始化Agent失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ChatResponseDTO chat(ChatRequestDTO request) {
        try {
            log.info("处理Agent聊天请求: agentId={}, sessionId={}, userId={}",
                    request.getAgentId(), request.getSessionId(), request.getUserId());

            Agent agent = this.getById(request.getAgentId());
            if (agent == null) {
                return buildErrorResponse(request, "Agent不存在");
            }

            if (agent.getType() == null) {
                return buildErrorResponse(request, "不支持的Agent类型");
            }

            // 根据Agent类型路由到不同的处理器
            if (agent.getType().isPreset()) {
                return handlePresetAgentChat(agent, request);
            } else {
                return handleGeneralAgentChat(agent, request);
            }

        } catch (Exception e) {
            log.error("处理Agent聊天请求失败: agentId={}", request.getAgentId(), e);
            return buildErrorResponse(request, "处理聊天请求失败: " + e.getMessage());
        }
    }

    @Override
    public String streamChat(ChatRequestDTO request) {
        try {
            log.info("处理Agent流式聊天请求: agentId={}, sessionId={}, userId={}, enableFunctionCalling={}",
                    request.getAgentId(), request.getSessionId(), request.getUserId(), request.getEnableFunctionCalling());

            Agent agent = this.getById(request.getAgentId());
            if (agent == null) {
                return "错误：Agent不存在";
            }

            if (agent.getType() == null) {
                return "错误：不支持的Agent类型";
            }

            // 根据Agent类型路由到不同的处理器
            if (agent.getType().isPreset()) {
                return handlePresetAgentStreamChat(agent, request);
            } else {
                return handleGeneralAgentStreamChat(agent, request);
            }

        } catch (Exception e) {
            log.error("处理Agent流式聊天请求失败: agentId={}", request.getAgentId(), e);
            return "错误：处理请求失败 - " + e.getMessage();
        }
    }

    @Override
    public List<String> getSupportedFunctions(Long agentId) {
        try {
            Agent agent = this.getById(agentId);
            if (agent == null) {
                return List.of();
            }

            if (agent.getType() != null && agent.getType().isPreset()) {
                return getAllAvailableFunctions();
            } else {
                // 通用Agent的功能列表可以从配置中获取
                return List.of("chat", "general_query");
            }
        } catch (Exception e) {
            log.error("获取Agent支持的功能失败: agentId={}", agentId, e);
            return List.of();
        }
    }

    @Override
    public Object executeFunction(Long agentId, String functionName, Map<String, Object> parameters) {
        try {
            log.info("执行Agent功能: agentId={}, functionName={}", agentId, functionName);

            Agent agent = this.getById(agentId);
            if (agent == null) {
                return Map.of("error", "Agent不存在");
            }

            if (agent.getType() != null && agent.getType().isPreset()) {
                return functionRegistry.executeFunction(functionName, parameters);
            } else {
                return Map.of("error", "只有预设类型的Agent支持直接功能调用");
            }
        } catch (Exception e) {
            log.error("执行Agent功能失败: agentId={}, functionName={}", agentId, functionName, e);
            return Map.of("error", "执行失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateAgentConfig(Long agentId) {
        try {
            Agent agent = this.getById(agentId);
            if (agent == null) {
                return false;
            }

            if (agent.getType() == null) {
                return false;
            }

            if (agent.getType().isPreset()) {
                CustomAgent customAgent = getAgentInstance(agent);
                return customAgent.validateConfig(agent);
            } else {
                LLMProvider provider = llmProviderFactory.getProvider(agent.getLlmProvider());
                return provider.validateConfig(agent);
            }

        } catch (Exception e) {
            log.error("验证Agent配置失败: agentId={}", agentId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getAgentStatus(Long agentId) {
        try {
            Agent agent = this.getById(agentId);
            if (agent == null) {
                return Map.of("error", "Agent不存在");
            }

            Map<String, Object> status = new HashMap<>();
            status.put("agentId", agentId);
            status.put("name", agent.getName());
            status.put("type", agent.getType());
            status.put("status", agent.getStatus() == 1 ? "enabled" : "disabled");
            status.put("provider", agent.getLlmProvider());
            status.put("model", agent.getLlmModel());

            if (agent.getType() != null && agent.getType().isPreset()) {
                status.put("implementationClass", agent.getImplementationClass());
                status.put("supportedFunctions", getAllAvailableFunctions());
            }

            return status;
        } catch (Exception e) {
            log.error("获取Agent状态失败: agentId={}", agentId, e);
            return Map.of("error", "获取状态失败: " + e.getMessage());
        }
    }

    @Override
    public void destroyAgent(Long agentId) {
        try {
            log.info("销毁Agent实例: agentId={}", agentId);

            CustomAgent instance = agentInstanceCache.remove(agentId);
            if (instance != null) {
                instance.destroy();
                log.info("Agent实例销毁成功: agentId={}", agentId);
            } else {
                log.info("Agent实例不存在于缓存中: agentId={}", agentId);
            }

        } catch (Exception e) {
            log.error("销毁Agent实例失败: agentId={}", agentId, e);
        }
    }

    @Override
    public List<String> getAllAvailableFunctions() {
        return functionRegistry.getAllFunctionNames();
    }

    @Override
    public Map<String, Map<String, Object>> getFunctionSchemas() {
        return functionRegistry.getAllFunctionSchemas();
    }

    /**
     * 构建默认系统提示词
     */
    private String buildDefaultSystemPrompt(String agentType) {
        StringBuilder prompt = new StringBuilder();

        switch (agentType) {
            case "project_management":
                prompt.append("你是一个专业的项目管理助手，专门帮助用户处理项目管理相关的任务。");
                prompt.append("你可以帮助用户查询项目信息、分析项目状态、提供项目管理建议等。");
                break;
            case "task_management":
                prompt.append("你是一个智能的任务管理助手，专门帮助用户处理任务管理相关的工作。");
                prompt.append("你可以帮助用户查询任务信息、分析任务状态、提供任务分配建议等。");
                break;
            default:
                prompt.append("你是一个智能业务助手，专门帮助用户处理各种业务相关的任务。");
                prompt.append("你具备项目管理、任务管理等多种业务能力。");
        }

        prompt.append("\n\n请始终保持专业、友好的态度，为用户提供准确、有用的帮助。");

        return prompt.toString();
    }

    /**
     * 设置默认LLM配置
     */
    private void setDefaultLLMConfig(Agent agent, AgentTypeEnum agentType) {
        // 如果没有设置LLM配置，则设置默认值
        if (agent.getLlmProvider() == null) {
            agent.setLlmProvider("deepseek");
        }
        if (agent.getLlmModel() == null) {
            agent.setLlmModel("deepseek-chat");
        }
        if (agent.getLlmApiUrl() == null) {
            agent.setLlmApiUrl("https://api.deepseek.com");
        }

        // 设置默认LLM参数
        if (agent.getLlmParams() == null) {
            JSONObject defaultParams = new JSONObject();
            defaultParams.put("temperature", 0.7);
            defaultParams.put("max_tokens", 4000);
            defaultParams.put("top_p", 0.9);
            agent.setLlmParams(defaultParams);
        }
    }

    /**
     * 设置默认提示词
     */
    private void setDefaultPrompts(Agent agent, AgentTypeEnum agentType, String specificType) {
        if (agent.getSystemPrompt() == null) {
            if (agentType.isPreset()) {
                agent.setSystemPrompt(buildDefaultSystemPrompt(specificType));
            } else {
                agent.setSystemPrompt("你是一个智能助手，请根据用户的问题提供准确、有用的回答。");
            }
        }

        if (agent.getUserPromptTemplate() == null) {
            agent.setUserPromptTemplate("{message}");
        }
    }

    /**
     * 设置功能配置
     */
    private void setDefaultFunctionConfig(Agent agent, AgentTypeEnum agentType) {
        if (agent.getFunctionConfig() == null) {
            if (agentType.isPreset()) {
                List<String> functions = getAllAvailableFunctions();
                agent.setFunctionConfig(JSON.toJSONString(functions));
            } else {
                // 通用Agent的基础功能
                List<String> basicFunctions = List.of("chat", "general_query");
                agent.setFunctionConfig(JSON.toJSONString(basicFunctions));
            }
        }
    }

    /**
     * 设置实现类名
     */
    private void setImplementationClass(Agent agent, AgentTypeEnum agentType, String specificType) {
        if (agentType.isPreset()) {
            if (agent.getImplementationClass() == null) {
                // 根据具体类型设置对应的实现类
                String implementationClass = getImplementationClassByType(specificType);
                agent.setImplementationClass(implementationClass);
            }
        }
    }

    /**
     * 根据Agent类型获取实现类名
     */
    private String getImplementationClassByType(String agentType) {
        return "com.vwatj.ppms.agent.impl.CustomAgentImpl";
    }

    // ==================== 新增方法实现 ====================

    @Override
    public CustomAgent getAgentInstance(Agent agent) {
        try {
            // 先从缓存中获取
            CustomAgent cachedInstance = agentInstanceCache.get(agent.getId());
            if (cachedInstance != null) {
                return cachedInstance;
            }

            // 通过反射创建实例
            String implementationClass = agent.getImplementationClass();
            if (implementationClass == null || implementationClass.trim().isEmpty()) {
                throw new RuntimeException("自定义Agent缺少实现类名: agentId=" + agent.getId());
            }

            log.info("通过反射创建Agent实例: agentId={}, class={}", agent.getId(), implementationClass);

            // 尝试从Spring容器中获取Bean
            try {
                Class<?> clazz = Class.forName(implementationClass);
                CustomAgent instance = (CustomAgent) applicationContext.getBean(clazz);
                log.info("从Spring容器获取Agent实例成功: agentId={}, class={}", agent.getId(), implementationClass);
                return instance;
            } catch (Exception e) {
                log.warn("从Spring容器获取Agent实例失败，尝试直接实例化: agentId={}, class={}",
                        agent.getId(), implementationClass, e);
            }

            // 直接通过反射创建实例
            Class<?> clazz = Class.forName(implementationClass);
            CustomAgent instance = (CustomAgent) clazz.getDeclaredConstructor().newInstance();

            log.info("通过反射创建Agent实例成功: agentId={}, class={}", agent.getId(), implementationClass);
            return instance;

        } catch (Exception e) {
            log.error("创建Agent实例失败: agentId={}, class={}",
                    agent.getId(), agent.getImplementationClass(), e);
            throw new RuntimeException("创建Agent实例失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String[] getAgentFunctions(Long agentId) {
        try {
            Agent agent = this.getById(agentId);
            if (agent == null) {
                return new String[0];
            }

            if (agent.getType() != null && agent.getType().isPreset()) {
                List<String> functions = getAllAvailableFunctions();
                return functions.toArray(new String[0]);
            } else {
                // 通用Agent的基础功能
                return new String[]{"chat", "general_query"};
            }
        } catch (Exception e) {
            log.error("获取Agent功能列表失败: agentId={}", agentId, e);
            return new String[0];
        }
    }

    /**
     * 处理预设Agent聊天请求
     */
    private ChatResponseDTO handlePresetAgentChat(Agent agent, ChatRequestDTO request) {
        CustomAgent customAgent = getOrCreatePresetAgent(agent);
        return customAgent.chat(request);
    }

    /**
     * 处理通用Agent聊天请求
     */
    private ChatResponseDTO handleGeneralAgentChat(Agent agent, ChatRequestDTO request) {
        try {
            // 检查Agent状态
            if (agent.getStatus() == null || agent.getStatus() != 1) {
                return buildErrorResponse(request, "Agent已禁用");
            }

            // 生成会话ID（如果没有提供）
            if (request.getSessionId() == null || request.getSessionId().isEmpty()) {
                request.setSessionId("session-" + UUID.randomUUID().toString());
            }

            // 获取LLM提供商
            LLMProvider provider = llmProviderFactory.getProvider(agent.getLlmProvider());

            // 验证配置
            if (!provider.validateConfig(agent)) {
                return buildErrorResponse(request, "Agent配置无效");
            }

            // 发送聊天请求
            ChatResponseDTO response = provider.chat(agent, request);

            // 处理功能调用（如果启用）
            if (request.getEnableFunctionCalling() && needsFunctionCall(request.getMessage())) {
                handleFunctionCalls(agent, request, response);
            }

            // 添加建议操作
            addSuggestedActions(agent, request, response);

            log.info("通用Agent聊天请求处理完成: agentId={}, sessionId={}, responseTime={}ms",
                    request.getAgentId(), request.getSessionId(), response.getResponseTime());

            return response;

        } catch (Exception e) {
            log.error("处理通用Agent聊天请求失败: agentId={}", agent.getId(), e);
            return buildErrorResponse(request, "处理请求失败: " + e.getMessage());
        }
    }

    /**
     * 处理预设Agent流式聊天请求
     */
    private String handlePresetAgentStreamChat(Agent agent, ChatRequestDTO request) {
        CustomAgent customAgent = getOrCreatePresetAgent(agent);
        return customAgent.streamChat(request);
    }

    /**
     * 处理通用Agent流式聊天请求
     */
    private String handleGeneralAgentStreamChat(Agent agent, ChatRequestDTO request) {
        try {
            // 检查Agent状态
            if (agent.getStatus() == null || agent.getStatus() != 1) {
                return "错误：Agent已禁用";
            }

            // 生成会话ID（如果没有提供）
            if (request.getSessionId() == null || request.getSessionId().isEmpty()) {
                request.setSessionId("session-" + UUID.randomUUID().toString());
            }

            // 获取LLM提供商
            LLMProvider provider = llmProviderFactory.getProvider(agent.getLlmProvider());

            // 验证配置
            if (!provider.validateConfig(agent)) {
                return "错误：Agent配置无效";
            }

            // 发送流式聊天请求
            return provider.streamChat(agent, request);

        } catch (Exception e) {
            log.error("处理通用Agent流式聊天请求失败: agentId={}", agent.getId(), e);
            return "错误：处理请求失败 - " + e.getMessage();
        }
    }

    /**
     * 获取或创建预设Agent实例
     */
    private CustomAgent getOrCreatePresetAgent(Agent agent) {
        CustomAgent customAgent = agentInstanceCache.get(agent.getId());
        if (customAgent == null) {
            customAgent = getAgentInstance(agent);
            customAgent.initialize(agent);
            agentInstanceCache.put(agent.getId(), customAgent);
        }
        return customAgent;
    }

    /**
     * 构建错误响应
     */
    private ChatResponseDTO buildErrorResponse(ChatRequestDTO request, String errorMessage) {
        ChatResponseDTO response = new ChatResponseDTO();
        response.setSessionId(request.getSessionId());
        response.setAgentId(request.getAgentId());
        response.setMessage("抱歉，处理您的请求时遇到了问题：" + errorMessage);
        response.setResponseTime(0L);
        response.setTimestamp(new Date().getTime());
        return response;
    }

    /**
     * 判断是否需要功能调用
     */
    private boolean needsFunctionCall(String message) {
        if (message == null) return false;
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("查询") || lowerMessage.contains("获取") ||
                lowerMessage.contains("统计") || lowerMessage.contains("分析") ||
                lowerMessage.contains("项目") || lowerMessage.contains("任务");
    }

    /**
     * 处理功能调用
     */
    private void handleFunctionCalls(Agent agent, ChatRequestDTO request, ChatResponseDTO response) {
        // 这里可以添加功能调用的逻辑
        // 暂时保持简单实现
        log.info("处理功能调用: agentId={}, message={}", agent.getId(), request.getMessage());
    }

    /**
     * 添加建议操作
     */
    private void addSuggestedActions(Agent agent, ChatRequestDTO request, ChatResponseDTO response) {
        // 这里可以添加建议操作的逻辑
        // 暂时保持简单实现
        log.debug("添加建议操作: agentId={}", agent.getId());
    }
}
