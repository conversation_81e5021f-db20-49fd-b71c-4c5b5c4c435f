<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  CloseCircleOutlined,
  RightOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  CheckCircleFilled,
  ExclamationCircleFilled
} from '@vicons/antd'
import { ProjectApi } from '@/apis/project'
import { ProjectProcessApi } from '@/apis/project_process'
import {
  calculateStageProgress,
  formatProjectDate,
  getProjectStatusText,
  getProjectStatusType,
  getProjectProgress,
  getProgressColor
} from '@/utils/project'


const props = defineProps<{
  projectInfo: any
  taskStats: any
  taskList: any[]
  processSteps: any[]
}>()

const route = useRoute()
const message = useMessage()

// 加载状态
const loading = ref(false)

// 项目成员列表
const projectMembers = ref<string[]>([])

// 获取项目成员
const fetchProjectMembers = async () => {
  try {
    const projectId = Number(route.params.id)
    const response = await ProjectApi.getProjectMembers(projectId)
    if (response.data) {
      projectMembers.value = response.data
    }
  } catch (error: any) {
    console.error('获取项目成员失败:', error)
    // 成员信息获取失败不影响主要功能，只记录错误
  }
}

// 流程步骤
const processSteps = ref<any[]>([])

// 获取项目流程数据
const fetchProjectProcess = async () => {
  try {
    loading.value = true
    if (props.processSteps && Array.isArray(props.processSteps)) {
      // 只获取主要流程阶段，过滤掉次要阶段
      const mainStages = props.processSteps.filter((stage: any) => stage.isMainProcess === true)

      // 转换后端数据格式为前端需要的格式，并获取文档信息
      const processedStages = await Promise.all(mainStages.map(async (stage: any) => {
        // 提取表单中的文档信息
        const files = await extractDocumentsFromStage(stage)

        return {
          id: stage.id,
          title: stage.stageName || stage.stageKey,
          status: mapBackendStatus(stage.status),
          operator: stage.updateBy || stage.createBy || '系统',
          plannedTime: formatTimeRange(stage.plannedStartTime, stage.plannedEndTime, '计划'),
          actualTime: formatTimeRange(stage.actualStartTime, stage.actualEndTime, '实际'),
          description: stage.description || stage.remarks || `${stage.stageName}阶段`,
          progress: calculateStageProgress(stage),
          files: files,
          // 保留原始数据
          originalData: stage
        }
      }))

      processSteps.value = processedStages
    } else {
      processSteps.value = []
    }
  } catch (error: any) {
    console.error('处理项目流程数据失败:', error)
    message.error('处理项目流程数据失败')
    processSteps.value = []
  } finally {
    loading.value = false
  }
}

// 监听 props.processSteps 的变化
watch(() => props.processSteps, (newVal) => {
  if (newVal) {
    fetchProjectProcess()
  }
}, { immediate: true })

// 映射后端状态到前端状态
const mapBackendStatus = (backendStatus: string) => {
  const statusMap: Record<string, string> = {
    'completed': 'completed',
    'active': 'in-progress',
    'pending': 'pending',
    'skipped': 'pending'
  }
  return statusMap[backendStatus] || 'pending'
}

// 格式化时间范围 - 使用工具函数但保留特定的日期格式
const formatTimeRange = (startTime: string, endTime: string, prefix: string) => {
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '--'
    try {
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      })
    } catch (error) {
      return '--'
    }
  }

  const start = formatDate(startTime)
  const end = formatDate(endTime)

  if (start === '--' && end === '--') {
    return null // 如果都没有时间，返回null表示不显示
  }

  return `${prefix}: ${start} ~ ${end}`
}


// 从阶段中提取文档信息
const extractDocumentsFromStage = async (stage: any): Promise<string[]> => {
  const files: string[] = []

  try {
    // 检查阶段的stageProcess配置
    if (stage.stageProcess && typeof stage.stageProcess === 'object') {
      const stageProcess = stage.stageProcess

      // 获取表单列表
      let forms: any[] = []
      if (stageProcess.forms && Array.isArray(stageProcess.forms)) {
        forms = stageProcess.forms
      } else if (Array.isArray(stageProcess)) {
        forms = stageProcess
      }

      // 遍历表单，获取实际的表单数据并提取文档信息
      for (const form of forms) {
        if (form.formKey) {
          try {
            // 获取表单数据
            const response = await ProjectProcessApi.getStageFormData(
              props.projectInfo.id,
              stage.stageKey,
              form.formKey
            )

            if (response.data && typeof response.data === 'object') {
              // 从表单数据中提取文档
              extractDocumentsFromFormData(response.data, files)
            }
          } catch (error) {
            // 表单数据不存在或获取失败，跳过
            console.log(`表单数据不存在: ${form.formKey}`)
          }
        } else if (form.data && typeof form.data === 'object') {
          // 如果表单配置中直接包含数据，也尝试提取
          extractDocumentsFromFormData(form.data, files)
        }
      }
    }
  } catch (error) {
    console.error('提取文档信息失败:', error)
  }

  return files
}

// 从表单数据中提取文档
const extractDocumentsFromFormData = (formData: any, files: string[]) => {
  if (!formData || typeof formData !== 'object') return

  // 递归遍历表单数据
  for (const key in formData) {
    const value = formData[key]

    // 检查字段名是否包含文档相关关键词
    const isDocumentField = /document|file|attachment|upload|doc|pdf|excel|word|image|photo|picture/i.test(key)

    if (Array.isArray(value)) {
      // 如果是数组，检查是否是文档数组
      value.forEach(item => {
        if (typeof item === 'object' && item !== null) {
          // 检查是否是文档对象
          if (item.name && (item.url || item.path || item.fileUrl || item.fileName)) {
            files.push(item.name)
          } else if (item.fileName) {
            files.push(item.fileName)
          } else if (item.originalName) {
            files.push(item.originalName)
          } else {
            // 递归检查数组中的对象
            extractDocumentsFromFormData(item, files)
          }
        } else if (typeof item === 'string') {
          // 如果是文档字段或包含文件扩展名
          if (isDocumentField || isFileExtension(item)) {
            files.push(item)
          }
        }
      })
    } else if (typeof value === 'object' && value !== null) {
      // 检查是否是单个文档对象
      if (value.name && (value.url || value.path || value.fileUrl || value.fileName)) {
        files.push(value.name)
      } else if (value.fileName) {
        files.push(value.fileName)
      } else if (value.originalName) {
        files.push(value.originalName)
      } else {
        // 递归检查对象
        extractDocumentsFromFormData(value, files)
      }
    } else if (typeof value === 'string') {
      // 如果是文档字段或包含文件扩展名
      if (isDocumentField || isFileExtension(value)) {
        files.push(value)
      }
    }
  }
}

// 检查是否是文件扩展名
const isFileExtension = (str: string): boolean => {
  if (!str || typeof str !== 'string') return false

  const fileExtensions = [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.txt', '.rtf', '.csv',
    '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp',
    '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv',
    '.mp3', '.wav', '.flac', '.aac',
    '.zip', '.rar', '.7z', '.tar', '.gz'
  ]

  return fileExtensions.some(ext => str.toLowerCase().includes(ext))
}

// 流程步骤状态配置
const PROCESS_STATUS_CONFIG = {
  'completed': {
    icon: CheckCircleFilled,
    color: '#52c41a',
    bgColor: '#f6ffed',
    text: '已完成'
  },
  'in-progress': {
    icon: SyncOutlined,
    color: '#1890ff',
    bgColor: '#e6f7ff',
    text: '进行中'
  },
  'pending': {
    icon: ClockCircleOutlined,
    color: '#d9d9d9',
    bgColor: '#fafafa',
    text: '待处理'
  },
  'error': {
    icon: ExclamationCircleFilled,
    color: '#ff4d4f',
    bgColor: '#fff2f0',
    text: '异常'
  }
} as const

// 获取步骤状态信息
const getStatusInfo = (status: string) => {
  return PROCESS_STATUS_CONFIG[status as keyof typeof PROCESS_STATUS_CONFIG] || PROCESS_STATUS_CONFIG.pending
}

// 处理步骤点击
const handleStepClick = (step: any) => {
  console.log('Step clicked:', step)
  // 这里可以添加点击后的处理逻辑
}

// 计算项目整体进度 - 使用统一的项目进度计算函数
const overallProgress = computed(() => {
  try {
    // 调试信息
    console.log('ProjectInfo:', props.projectInfo)
    console.log('StartDate:', props.projectInfo.startDate, typeof props.projectInfo.startDate)
    console.log('EndDate:', props.projectInfo.endDate, typeof props.projectInfo.endDate)
    console.log('Status:', props.projectInfo.status)

    const progress = getProjectProgress(props.projectInfo)
    console.log('Calculated Progress:', progress)

    // 确保返回有效数字
    return isNaN(progress) ? 0 : progress
  } catch (error) {
    console.error('Error calculating overall progress:', error)
    return 0
  }
})

// 计算进度条颜色 - 使用统一的颜色函数
const progressColor = computed(() => {
  return getProgressColor(overallProgress.value)
})

// 项目信息显示数据
const projectInfoItems = computed(() => {
  // 调试日期格式
  console.log('Date formatting debug:')
  console.log('startDate raw:', props.projectInfo.startDate, typeof props.projectInfo.startDate)
  console.log('endDate raw:', props.projectInfo.endDate, typeof props.projectInfo.endDate)
  console.log('startDate formatted:', formatProjectDate(props.projectInfo.startDate))
  console.log('endDate formatted:', formatProjectDate(props.projectInfo.endDate))

  return [
    {
      label: '开始日期：',
      value: formatProjectDate(props.projectInfo.startDate)
    },
    {
      label: '结束日期：',
      value: formatProjectDate(props.projectInfo.endDate)
    },
  {
    label: '负责人：',
    value: props.projectInfo.manager,
    isAvatar: true
  },
  {
    label: '版本：',
    value: props.projectInfo.version
  },
  {
    label: '资产号',
    value: props.projectInfo.assetNo || '--'
  },
  {
    label: '资产名',
    value: props.projectInfo.assetName || '--'
  },
  {
    label: '资产版本：',
    value: props.projectInfo.assetVersion || '--'
  },
  {
    label: '状态：',
    value: getProjectStatusText(props.projectInfo.status),
    statusType: getProjectStatusType(props.projectInfo.status),
    isTag: true
  }
  ]
})



// 组件挂载时获取数据
onMounted(() => {
  fetchProjectMembers()
})
</script>

<template>
  <n-grid :cols="24" :x-gap="16" class="p-4">
    <n-gi :span="6">
      <n-card title="项目概览" class="mb-4">
        <n-statistic label="项目进度" :value="overallProgress" suffix="%">
          <template #suffix>/100%</template>
        </n-statistic>
        <n-progress
          type="line"
          :percentage="overallProgress"
          :indicator-placement="'inside'"
          :color="progressColor"
          class="mt-2"
        />
        <n-divider />
        <div class="flex flex-col gap-2">
          <div v-for="(item, index) in projectInfoItems" :key="index" class="flex items-center">
            <span class="w-20 flex-shrink-0 text-gray-500">{{ item.label }}</span>
            <span v-if="!item.isTag && !item.isAvatar" class="flex-1 break-all">{{ item.value }}</span>
            <span v-else-if="item.isAvatar" class="flex-1 flex items-center">
              <n-avatar size="small" :src="''" class="mr-2" />
              {{ item.value }}
            </span>
            <n-tag v-else :type="item.statusType" size="small">
              {{ item.value }}
            </n-tag>
          </div>
        </div>
      </n-card>

      <n-card title="项目成员" size="small" class="mt-4">
        <div class="flex flex-col gap-3">
          <div v-for="(member, index) in projectMembers.slice(0, 5)" :key="index" class="flex items-center py-2">
            <n-avatar round size="small" :src="''" class="mr-2" />
            <span>{{ member }}</span>
          </div>
          <div v-if="projectMembers.length === 0" class="text-center text-gray-400 py-4">
            暂无项目成员
          </div>
        </div>
      </n-card>
    </n-gi>

    <n-gi :span="12">
      <n-card title="项目流程" class="mb-4">
        <n-spin :show="loading">
          <div v-if="processSteps.length === 0 && !loading" class="text-center py-8 text-gray-500">
            暂无流程数据
          </div>
          <n-timeline v-else class="mx--3">
            <n-timeline-item
              v-for="step in processSteps"
              :key="step.id"
              :type="step.status === 'completed' ? 'success' : step.status === 'in-progress' ? 'info' : 'default'"
              :title="step.title"
              :content="step.description"
              @click="handleStepClick(step)"
              class="px-3 py-2 rounded transition-all duration-300 cursor-pointer hover:bg-gray-50"
              :class="{
                'bg-blue-50': step.status === 'in-progress',
                'text-blue-600': step.status === 'in-progress',
                'font-semibold': step.status === 'in-progress',
                'text-green-600': step.status === 'completed'
              }"
            >
            <template #icon>
              <n-icon :component="getStatusInfo(step.status).icon" :color="getStatusInfo(step.status).color" />
            </template>
            <div class="mt-2">
              <div class="mb-2">
                <n-space size="small" align="center" class="mb-2">
                  <n-tag
                    size="small"
                    :bordered="false"
                    :color="{
                      textColor: getStatusInfo(step.status).color,
                      color: getStatusInfo(step.status).bgColor
                    }"
                  >
                    <template #icon>
                      <n-icon :component="getStatusInfo(step.status).icon" :color="getStatusInfo(step.status).color" />
                    </template>
                    {{ getStatusInfo(step.status).text }}
                  </n-tag>
                  <span class="text-gray-400 text-xs flex items-center">
                    <n-icon :component="UserOutlined" class="mr-1" /> {{ step.operator }}
                  </span>
                </n-space>
              </div>

              <!-- 时间信息显示 -->
              <div class="mb-2 text-xs text-gray-500 space-y-1">
                <div v-if="step.plannedTime" class="flex items-center">
                  <n-icon :component="CalendarOutlined" class="mr-1" />
                  {{ step.plannedTime }}
                </div>
                <div v-if="step.actualTime" class="flex items-center">
                  <n-icon :component="CalendarOutlined" class="mr-1" />
                  {{ step.actualTime }}
                </div>
              </div>

              <n-progress
                v-if="step.status === 'in-progress'"
                type="line"
                :percentage="step.progress"
                :indicator-placement="'inside'"
                :height="6"
                :border-radius="0"
                :fill-border-radius="0"
                :show-indicator="false"
                :color="getStatusInfo(step.status).color"
                class="my-2"
              />

              <!-- 文档信息显示 -->
              <div v-if="step.files && step.files.length" class="mt-3">
                <n-divider class="my-2" />
                <div class="text-xs text-gray-600 mb-2 font-medium">相关文档:</div>
                <div class="flex flex-col gap-1">
                  <div
                    v-for="(file, index) in step.files"
                    :key="index"
                    class="flex items-center px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200 transition-colors cursor-pointer"
                    :title="file"
                  >
                    <n-icon :component="FileTextOutlined" class="mr-1 text-blue-500" />
                    <span class="truncate">{{ file }}</span>
                  </div>
                </div>
              </div>
            </div>
          </n-timeline-item>
        </n-timeline>
        </n-spin>
      </n-card>
    </n-gi>

    <n-gi :span="6">
     
      <n-card title="任务统计" size="small" class="mb-4">
        <n-statistic label="总任务数" :value="taskStats.total" />
        <n-progress
          type="line"
          :percentage="taskStats.total > 0 ? (taskStats.completed / taskStats.total) * 100 : 0"
          :indicator-placement="'inside'"
          status="success"
          class="mt-2"
        />
        <n-divider />
        <div class="flex flex-col gap-2">
          <div
            v-for="(stat, index) in [
              { type: 'success' as const, icon: CheckCircleOutlined, label: '已完成', value: taskStats.completed },
              { type: 'primary' as const, icon: SyncOutlined, label: '进行中', value: taskStats.inProgress },
              { type: 'warning' as const, icon: ExclamationCircleOutlined, label: '未开始', value: taskStats.notStarted },
              { type: 'default' as const, icon: ClockCircleOutlined, label: '待验证', value: taskStats.toVerify }
            ]"
            :key="index"
            class="flex items-center justify-between"
          >
            <n-tag :type="stat.type" size="small" round>
              <template #icon>
                <n-icon :component="stat.icon" />
              </template>
              {{ stat.label }}
            </n-tag>
            <span class="font-bold">{{ stat.value }}</span>
          </div>
        </div>
      </n-card>

      <n-card title="最近任务" size="small">
        <div class="flex flex-col">
          <div 
            v-for="task in taskList.slice(0, 5)" 
            :key="task.id" 
            class="py-3 border-solid border-gray-100 border-b border-0 last:border-0"
          >
            <div class="flex items-center mb-1">
              <span class="flex-1 truncate font-semibold">{{ task.title }}</span>
              <n-tag 
                :type="task.status === '已完成' ? 'success' : task.status === '进行中' ? 'primary' : 'default'" 
                size="small"
                class="flex-shrink-0"
              >
                {{ task.status }}
              </n-tag>
            </div>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span class="flex items-center">
                <n-avatar size="small" :src="task.avatar || ''" class="mr-2" />
                <span>{{ task.assignee }}</span>
              </span>
              <span 
                class="flex items-center"
                :class="{
                  'text-red-500 font-semibold': new Date(task.dueDate) < new Date(),
                  'text-gray-500': new Date(task.dueDate) >= new Date()
                }"
              >
                <n-icon :component="CalendarOutlined" class="mr-1" />
                截止: {{ task.dueDate }}
              </span>
            </div>
          </div>
          <div v-if="taskList.length === 0" class="text-center text-gray-400 py-4">
            暂无任务
          </div>
        </div>
      </n-card>
    
    </n-gi>
  </n-grid>
</template>