import { httpClient } from './index'
import type { ApiResponse, PaginationResponse } from '@/types'

export interface ProjectCategoryOption {
  label: string
  value: string
}

export interface ProjectCategory {
  id: number
  code: string
  name: string
  description?: string
  sortOrder: number
  enabled: boolean
  createTime: string
  updateTime: string
  createBy?: string
  updateBy?: string
}

export interface ProjectCategoryQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  enabled?: boolean
}

export interface CreateProjectCategoryParams {
  code: string
  name: string
  description?: string
  sortOrder?: number
  enabled?: boolean
}

export interface UpdateProjectCategoryParams extends CreateProjectCategoryParams {
  id: number
}

/**
 * 项目分类 API
 */
export class ProjectCategoryApi {
  private static readonly BASE_PATH = '/project-categories'

  /**
   * 获取项目分类选项列表
   */
  static async getProjectCategoryOptions(): Promise<ApiResponse<ProjectCategoryOption[]>> {
    return httpClient.get<ProjectCategoryOption[]>(`${this.BASE_PATH}/options`)
  }

  /**
   * 分页查询项目分类
   */
  static async getProjectCategoryPage(params?: ProjectCategoryQueryParams): Promise<ApiResponse<PaginationResponse<ProjectCategory>>> {
    return httpClient.get<PaginationResponse<ProjectCategory>>(`${this.BASE_PATH}/page`, params)
  }

  /**
   * 根据ID查询项目分类
   */
  static async getProjectCategory(id: number): Promise<ApiResponse<ProjectCategory>> {
    return httpClient.get<ProjectCategory>(`${this.BASE_PATH}/${id}`)
  }

  /**
   * 创建项目分类
   */
  static async createProjectCategory(data: CreateProjectCategoryParams): Promise<ApiResponse<ProjectCategory>> {
    return httpClient.post<ProjectCategory>(this.BASE_PATH, data)
  }

  /**
   * 更新项目分类
   */
  static async updateProjectCategory(data: UpdateProjectCategoryParams): Promise<ApiResponse<ProjectCategory>> {
    const { id, ...updateData } = data
    return httpClient.put<ProjectCategory>(`${this.BASE_PATH}/${id}`, updateData)
  }

  /**
   * 删除项目分类
   */
  static async deleteProjectCategory(id: number): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`${this.BASE_PATH}/${id}`)
  }
}
