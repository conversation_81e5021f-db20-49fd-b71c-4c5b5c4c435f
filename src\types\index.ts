// 类型定义统一导出

// 项目相关类型
export * from './project'

// 任务相关类型
export * from './task'

// 文档相关类型
export * from './document'

export * from './system'
export * from './process-definition'
export * from './project_process'
export * from './agent'

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  fullName: string
  role: UserRole
  department?: string
  position?: string
  phone?: string
  isActive: boolean
  lastLoginAt?: number
  createdAt: number
  updatedAt: number
}

export type UserRole = 'admin' | 'manager' | 'developer' | 'designer' | 'tester' | 'viewer'

// 通用响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResponse<T = any> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 与后端PageResult保持一致的类型定义
export interface PageResult<T = any> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages?: number
}

// 表单相关类型
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  validator?: (rule: any, value: any) => boolean | Promise<boolean>
}

export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
  children?: SelectOption[]
}

// 简单文件上传类型（用于表单组件）
export interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  url: string
  status: 'uploading' | 'success' | 'error'
  progress?: number
}

// 通知类型
export interface Notification {
  id: string
  title: string
  content: string
  type: 'info' | 'success' | 'warning' | 'error'
  recipientId: string
  senderId?: string
  senderName?: string
  isRead: boolean
  readAt?: number
  relatedId?: string
  relatedType?: 'project' | 'task' | 'document' | 'comment'
  createdAt: number
  expiresAt?: number
}

// 系统配置类型
export interface SystemConfig {
  siteName: string
  siteDescription: string
  logo?: string
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  dateFormat: string
  timeFormat: string
}

// 权限相关类型
export interface Permission {
  id: string
  name: string
  code: string
  description?: string
  resource: string
  action: string
}

export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: Permission[]
  isSystem: boolean
  createdAt: number
  updatedAt: number
}

// 菜单类型
export interface MenuItem {
  id: string
  title: string
  path?: string
  icon?: string
  component?: string
  redirect?: string
  children?: MenuItem[]
  meta?: {
    title: string
    requiresAuth?: boolean
    keepAlive?: boolean
    hidden?: boolean
    roles?: string[]
  }
}

// 统计图表类型
export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }>
}

export interface StatCard {
  title: string
  value: number | string
  unit?: string
  trend?: 'up' | 'down' | 'stable'
  trendValue?: number
  color?: string
  icon?: string
}

// 搜索相关类型
export interface SearchParams {
  keyword?: string
  type?: string
  status?: string
  dateRange?: [number, number]
  tags?: string[]
  assignee?: string
  priority?: string
}

export interface SearchResult<T = any> {
  items: T[]
  total: number
  facets?: Record<string, Array<{
    value: string
    count: number
  }>>
}

// 导出相关类型
export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  fields: string[]
  filters?: Record<string, any>
  fileName?: string
}

// 导入相关类型
export interface ImportResult {
  total: number
  success: number
  failed: number
  errors?: Array<{
    row: number
    field: string
    message: string
  }>
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: string
  payload: any
  timestamp: number
  sender?: string
}

// 日志类型
export interface ActivityLog {
  id: number
  userId: number
  userName: string
  action: string
  resource: string
  resourceId: number
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  createdAt: number
}

// 评论类型
export interface Comment {
  id: number
  content: string
  authorId: number
  authorName: string
  authorAvatar?: string
  parentId?: number
  replies?: Comment[]
  mentions?: string[]
  attachments?: UploadFile[]
  createdAt: number
  updatedAt: number
}

// 标签类型
export interface Tag {
  id: number
  name: string
  color?: string
  description?: string
  category?: string
  usageCount?: number
  createdAt: number
}

// IT资产类型
export interface ITAsset {
  id: string
  assetNo: string
  allProjectName: string
  shortCode: string
  ciName: string
  site: string
  location: string
  businessDepartment: string
  pmOwner: string
  description: string
  image?: string
  // 主要字段
  mainFields: {
    assetNo: string
    allProjectName: string
    shortCode: string
    ciName: string
    site: string
    location: string
    businessDepartment: string
    pmOwner: string
    description: string
  }
  // 额外属性分类
  extraProperties: {
    // 应用信息
    applicationInfo: {
      appId?: string
      onlineTime?: string
      mail?: string
      serverContainer?: string
      portExternal?: string
      version?: string
      language?: string
    }
    // 生产环境
    productionEnv: {
      appServer?: string
      dbName?: string
      dbServer?: string
      website?: string
    }
    // 测试环境
    testEnv: {
      appServer?: string
      dbName?: string
      dbServer?: string
      platform?: string
      website?: string
      testAccount?: string
      prodAccount?: string
    }
    // 安全配置
    securityConfig: {
      isIntegratedAD?: boolean
      adAccount?: string
      asa?: string
      authType?: string
      accountLockMechanism?: string
      networkAccess?: string
      port?: string
      hasNAT?: boolean
      usesSMTP?: boolean
      sslEnabled?: boolean
      xxlJobEnabled?: boolean
      executorName?: string
    }
    // 业务信息
    businessInfo: {
      businessImpact?: string
      critical?: string
      csl?: string
      group?: string
      externalNetwork?: string
      hostLocation?: string
      ic?: string
      integrity?: string
      maxDowntime?: string
      rpo?: string
      rto?: string
      sapConnection?: string
      businessKeyUser?: string
      userCount?: string
      usageStatus?: string
      region?: string
      projectName?: string
      projectType?: string
      status?: string
    }
    // 资产管理
    assetManagement: {
      assetState?: string
      remark?: string
      retireDate?: string
      itAssets?: string
      productName?: string
      assertNo?: string
    }
    // 文档资料
    documentation: {
      systemPasswordRuleConfig?: string
      accountPermissionApprovalForms?: string
      itSystemAccountPermissionReview?: string
    }
  }
  createdAt: number
  updatedAt: number
}
