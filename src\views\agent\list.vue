<template>
  <page-layout title="Agent列表">
    <template #action>
      <n-button type="primary" @click="handleCreate">
        <template #icon>
          <n-icon><PlusOutlined /></n-icon>
        </template>
        新建Agent
      </n-button>
    </template>

    <n-card :bordered="false" class="mb-4">
      <n-data-table :columns="columns" :data="agentList" :pagination="pagination" :loading="loading"
        :row-key="(row: any) => row.id" @update:sorter="handleSorterChange" />
    </n-card>

    <!-- 创建/编辑弹窗 -->
    <n-modal v-model:show="showModal" :title="modalTitle" preset="dialog" style="width: 800px;">
      <n-form ref="formRef" :model="formModel" :rules="rules" label-placement="left" label-width="120px">
        <n-tabs type="line" animated>
          <n-tab-pane name="basic" tab="基础信息">
            <n-form-item label="Agent名称" path="name">
              <n-input v-model:value="formModel.name" placeholder="请输入Agent名称" />
            </n-form-item>
            <n-form-item label="描述" path="description">
              <n-input v-model:value="formModel.description" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }"
                placeholder="请输入Agent描述" />
            </n-form-item>
            <n-form-item label="类型" path="type">
              <n-select v-model:value="formModel.type" placeholder="请选择Agent类型" :options="agentTypeOptions" />
            </n-form-item>
            <n-form-item label="状态" path="status">
              <n-switch v-model:value="formModel.status" :checked-value="1" :unchecked-value="0">
                <template #checked>已启用</template>
                <template #unchecked>已禁用</template>
              </n-switch>
            </n-form-item>
          </n-tab-pane>

          <n-tab-pane name="llm" tab="LLM配置">
            <n-form-item label="LLM提供商" path="llmProvider">
              <n-select v-model:value="formModel.llmProvider" placeholder="请选择LLM提供商"
                :options="llmProviderOptions" @update:value="handleProviderChange" />
            </n-form-item>
            <n-form-item label="模型名称" path="llmModel">
              <n-input v-model:value="formModel.llmModel" placeholder="请输入模型名称，如：gpt-3.5-turbo" />
            </n-form-item>
            <n-form-item label="API URL" path="llmApiUrl">
              <n-input v-model:value="formModel.llmApiUrl" placeholder="请输入API URL" />
            </n-form-item>
            <n-form-item label="API Key" path="llmApiKey">
              <n-input v-model:value="formModel.llmApiKey" type="password" show-password-on="click"
                placeholder="请输入API Key" />
            </n-form-item>
            <n-form-item label="LLM参数" path="llmParams">
              <n-input v-model:value="formModel.llmParams" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder='请输入LLM参数配置（JSON格式），如：{"temperature": 0.7, "max_tokens": 2000}' />
            </n-form-item>
          </n-tab-pane>

          <n-tab-pane name="prompt" tab="提示词配置">
            <n-form-item label="系统提示词" path="systemPrompt">
              <n-input v-model:value="formModel.systemPrompt" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }"
                placeholder="请输入系统提示词，定义Agent的角色和行为" />
            </n-form-item>
            <n-form-item label="用户提示词模板" path="userPromptTemplate">
              <n-input v-model:value="formModel.userPromptTemplate" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入用户提示词模板，支持变量替换，如：{user_input}" />
            </n-form-item>
          </n-tab-pane>

          <n-tab-pane name="function" tab="功能配置">
            <n-form-item label="功能配置" path="functionConfig">
              <n-input v-model:value="formModel.functionConfig" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }"
                placeholder='请输入功能配置（JSON格式），如：{"enabled_functions": ["project_query", "task_create"]}' />
            </n-form-item>
            <n-form-item label="通用配置" path="config">
              <n-input v-model:value="formModel.config" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入其他配置信息（JSON格式）" />
            </n-form-item>
          </n-tab-pane>
        </n-tabs>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" :loading="submitting" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </page-layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from "vue";
import { useRouter } from "vue-router";
import { NTag, useMessage, useDialog, NButton, NIcon } from "naive-ui";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  MessageOutlined,
} from "@vicons/antd";
import type { DataTableColumns } from "naive-ui";
import PageLayout from "@/layouts/PageLayout.vue";
import { AgentApi } from "@/apis";
import type { Agent, AgentQueryParams, CreateAgentParams, UpdateAgentParams } from "@/types";

const message = useMessage();
const dialog = useDialog();
const router = useRouter();
const loading = ref(false);
const showModal = ref(false);
const submitting = ref(false);
const formRef = ref();
const modalTitle = ref("新建Agent");

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
    fetchAgentList();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    fetchAgentList();
  },
});

// 表格列配置
const createColumns = (): DataTableColumns => [
  {
    title: "ID",
    key: "id",
    width: 80,
    sorter: "default",
  },
  {
    title: "Agent名称",
    key: "name",
    sorter: "default",
  },
  {
    title: "描述",
    key: "description",
    ellipsis: true,
  },
  {
    title: "状态",
    key: "status",
    width: 120,
    render: (row: any) => {
      return h("div", { class: "flex flex-col gap-1" }, [
        h(
          NTag,
          {
            type: row.status === 1 ? "success" : "default",
            size: "small",
            round: true,
          },
          { default: () => (row.status === 1 ? "已启用" : "已禁用") }
        ),
        h(
          NTag,
          {
            type: "info",
            size: "small",
            round: true,
          },
          { default: () => row.type || "通用" }
        )
      ]);
    },
  },
  {
    title: "创建时间",
    key: "createdAt",
    width: 180,
    sorter: "default",
  },
  {
    title: "操作",
    key: "actions",
    width: 200,
    render: (row) => {
      return h("div", { class: "flex gap-2" }, [
        h(
          NButton,
          {
            size: "small",
            type: "primary",
            ghost: true,
            onClick: () => handleEdit(row as unknown as Agent),
          },
          { default: () => "编辑" }
        ),
        h(
          NButton,
          {
            size: "small",
            type: "primary",
            onClick: () => handleChat(row as unknown as Agent),
          },
          { icon: () => h(MessageOutlined), default: () => "对话" }
        ),
        h(
          NButton,
          {
            size: "small",
            type: "info",
            ghost: true,
            onClick: () => handleInitialize(row as unknown as Agent),
          },
          { default: () => "初始化" }
        ),
        h(
          NButton,
          {
            size: "small",
            type: "warning",
            ghost: true,
            onClick: () => handleValidate(row as unknown as Agent),
          },
          { default: () => "验证" }
        ),
        h(
          NButton,
          {
            size: "small",
            type: "error",
            ghost: true,
            onClick: () => handleDelete(row as unknown as Agent),
          },
          { icon: () => h(DeleteOutlined) }
        ),
      ]);
    },
  },
];

const columns = createColumns();

// 表单模型
const formModel = reactive<{
  id?: number;
  name: string;
  description: string;
  status: number;
  type: string;
  config: string;
  llmProvider: string;
  llmModel: string;
  llmApiUrl: string;
  llmApiKey: string;
  llmParams: string;
  systemPrompt: string;
  userPromptTemplate: string;
  functionConfig: string;
}>({
  id: undefined,
  name: "",
  description: "",
  status: 1,
  type: "",
  config: "",
  llmProvider: "",
  llmModel: "",
  llmApiUrl: "",
  llmApiKey: "",
  llmParams: "",
  systemPrompt: "",
  userPromptTemplate: "",
  functionConfig: "",
});

// Agent类型选项
const agentTypeOptions = [
  { label: "通用助手", value: "general" },
  { label: "项目管理", value: "project" },
  { label: "任务管理", value: "task" },
  { label: "数据分析", value: "analysis" },
  { label: "代码助手", value: "code" },
];

// LLM提供商选项
const llmProviderOptions = [
  { label: "OpenAI", value: "openai" },
  { label: "DeepSeek", value: "deepseek" },
  { label: "通义千问", value: "qwen" },
  { label: "文心一言", value: "ernie" },
  { label: "智谱AI", value: "zhipu" },
];

// 表单验证规则
const rules = {
  name: [
    { required: true, message: "请输入Agent名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在2到50个字符之间", trigger: "blur" },
  ],
  description: [{ max: 200, message: "不能超过200个字符", trigger: "blur" }],
  llmProvider: [{ required: true, message: "请选择LLM提供商", trigger: "change" }],
  llmModel: [{ required: true, message: "请输入LLM模型名称", trigger: "blur" }],
  llmApiUrl: [{ required: true, message: "请输入API URL", trigger: "blur" }],
  llmApiKey: [{ required: true, message: "请输入API Key", trigger: "blur" }],
};

// Agent列表数据
const agentList = ref<Agent[]>([]);

// 获取Agent列表
const fetchAgentList = async () => {
  try {
    loading.value = true;
    const queryParams: AgentQueryParams = {
      page: pagination.page,
      pageSize: pagination.pageSize,
    };

    const response = await AgentApi.getAgents(queryParams);
    if (response.code === 200) {
      agentList.value = response.data.data;
      pagination.itemCount = response.data.total;
    } else {
      message.error(response.message || "获取Agent列表失败");
    }
  } catch (error) {
    console.error("获取Agent列表失败:", error);
    message.error("获取Agent列表失败");
  } finally {
    loading.value = false;
  }
};

// 处理排序变化
const handleSorterChange = (sorter: any) => {
  console.log("排序变化:", sorter);
  fetchAgentList();
};

// 处理LLM提供商变化
const handleProviderChange = (provider: string) => {
  // 根据提供商设置默认配置
  switch (provider) {
    case 'openai':
      formModel.llmApiUrl = 'https://api.openai.com/v1';
      formModel.llmModel = 'gpt-3.5-turbo';
      break;
    case 'deepseek':
      formModel.llmApiUrl = 'https://api.deepseek.com/v1';
      formModel.llmModel = 'deepseek-chat';
      break;
    case 'qwen':
      formModel.llmApiUrl = 'https://dashscope.aliyuncs.com/api/v1';
      formModel.llmModel = 'qwen-turbo';
      break;
    case 'ernie':
      formModel.llmApiUrl = 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1';
      formModel.llmModel = 'ernie-bot-turbo';
      break;
    case 'zhipu':
      formModel.llmApiUrl = 'https://open.bigmodel.cn/api/paas/v4';
      formModel.llmModel = 'glm-4';
      break;
  }
};

// 新建Agent
const handleCreate = () => {
  modalTitle.value = "新建Agent";
  Object.assign(formModel, {
    id: undefined,
    name: "",
    description: "",
    status: 1,
    type: "",
    config: "",
    llmProvider: "",
    llmModel: "",
    llmApiUrl: "",
    llmApiKey: "",
    llmParams: '{"temperature": 0.7, "max_tokens": 2000}',
    systemPrompt: "你是一个智能助手，可以帮助用户处理各种任务。",
    userPromptTemplate: "{user_input}",
    functionConfig: '{"enabled_functions": []}',
  });
  showModal.value = true;
};

// 编辑Agent
const handleEdit = (row: Agent) => {
  modalTitle.value = "编辑Agent";
  Object.assign(formModel, {
    id: row.id,
    name: row.name,
    description: row.description || "",
    status: row.status,
    type: row.type || "",
    config: row.config || "",
    llmProvider: row.llmProvider || "",
    llmModel: row.llmModel || "",
    llmApiUrl: row.llmApiUrl || "",
    llmApiKey: row.llmApiKey || "",
    llmParams: row.llmParams || '{"temperature": 0.7, "max_tokens": 2000}',
    systemPrompt: row.systemPrompt || "",
    userPromptTemplate: row.userPromptTemplate || "",
    functionConfig: row.functionConfig || '{"enabled_functions": []}',
  });
  showModal.value = true;
};

// 删除Agent
const handleDelete = (row: Agent) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除Agent "${row.name}" 吗？此操作不可恢复。`,
    positiveText: "删除",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const response = await AgentApi.deleteAgent(row.id);
        if (response.code === 200) {
          message.success("删除成功");
          fetchAgentList();
        } else {
          message.error(response.message || "删除失败");
        }
      } catch (error) {
        console.error("删除Agent失败:", error);
        message.error("删除失败");
      }
    },
  });
};

// 开始对话
const handleChat = (row: Agent) => {
  // 跳转到对话页面
  router.push({ name: 'AgentChat', params: { id: row.id.toString() } });
};

// 初始化Agent
const handleInitialize = async (row: Agent) => {
  try {
    const response = await AgentApi.initializeAgent(row.id);
    if (response.code === 200) {
      message.success(`Agent "${row.name}" 初始化成功`);
      fetchAgentList(); // 刷新列表
    } else {
      message.error(response.message || "初始化失败");
    }
  } catch (error) {
    console.error("初始化Agent失败:", error);
    message.error("初始化失败");
  }
};

// 验证Agent配置
const handleValidate = async (row: Agent) => {
  try {
    const response = await AgentApi.validateAgent(row.id);
    if (response.code === 200) {
      if (response.data) {
        message.success(`Agent "${row.name}" 配置验证通过`);
      } else {
        message.warning(`Agent "${row.name}" 配置验证失败`);
      }
    } else {
      message.error(response.message || "验证失败");
    }
  } catch (error) {
    console.error("验证Agent配置失败:", error);
    message.error("验证失败");
  }
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        submitting.value = true;
        let response;

        if (formModel.id) {
          // 更新Agent
          response = await AgentApi.updateAgent(formModel.id, {
            name: formModel.name,
            description: formModel.description,
            status: formModel.status,
            type: formModel.type,
            config: formModel.config,
            llmProvider: formModel.llmProvider,
            llmModel: formModel.llmModel,
            llmApiUrl: formModel.llmApiUrl,
            llmApiKey: formModel.llmApiKey,
            llmParams: formModel.llmParams,
            systemPrompt: formModel.systemPrompt,
            userPromptTemplate: formModel.userPromptTemplate,
            functionConfig: formModel.functionConfig,
          });
        } else {
          // 创建Agent
          response = await AgentApi.createAgent({
            name: formModel.name,
            description: formModel.description,
            status: formModel.status,
            type: formModel.type,
            config: formModel.config,
            llmProvider: formModel.llmProvider,
            llmModel: formModel.llmModel,
            llmApiUrl: formModel.llmApiUrl,
            llmApiKey: formModel.llmApiKey,
            llmParams: formModel.llmParams,
            systemPrompt: formModel.systemPrompt,
            userPromptTemplate: formModel.userPromptTemplate,
            functionConfig: formModel.functionConfig,
          });
        }

        if (response.code === 200) {
          message.success(formModel.id ? "更新成功" : "创建成功");
          showModal.value = false;
          fetchAgentList();
        } else {
          message.error(response.message || (formModel.id ? "更新失败" : "创建失败"));
        }
      } catch (error) {
        console.error("操作失败:", error);
        message.error(formModel.id ? "更新失败" : "创建失败");
      } finally {
        submitting.value = false;
      }
    }
  });
};

// 初始化加载数据
onMounted(() => {
  fetchAgentList();
});
</script>

<style scoped>
.agent-list {
  padding: 20px;
}

:deep(.n-data-table-th) {
  font-weight: 600;
}

:deep(.n-button) {
  margin-right: 8px;
}

:deep(.n-pagination) {
  margin-top: 20px;
  justify-content: flex-end;
}
</style>
